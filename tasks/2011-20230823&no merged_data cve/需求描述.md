现在我有一个merge_dataset.json文件，这个是现在所有的backporting工具使用的补丁数量，现在我进行实验的话，需要也使用同样的数据格式搜集数据集。应该满足以下需求。
1. 数据集的格式应该和merge_dataset.json文件的格式一样。
2. 新收集的CVE数据集时间应该是从2011年至2025年8月3日。
3. 新收集的CVE数据集的和merge_dataset.json应该有一定的区别：
    - CVE编号不能一致。
    - CVE影响的软件一定是C/C++语言的软件。需要使用github api去进行一定的查询进行验证哈。
4. 等价补丁筛选标准：一定需要使用mystique的等价补丁筛选标准。
    - mystique原文描述:
        electing CVE Patch Pairs. Following PPatHF [35], we manually filtered commits unrelated to the vulnerability, and further refined the commits that involved files unrelated to C or modifications outside functions. After filtering, we sorted all commits by timestamp, and selected the earliest and latest commit for each vulnerability to form a CVE patch pair. The earliest commits are regarded as the original patch and the latest commits are regarded as the target patch. If the vulnerability only contains one single patch, we also excluded it. After this process, we finalized CVEs with their corresponding patch pairs. ([p11](zotero://open-pdf/library/items/XXN57HCB?page=11&annotation=EH3UC4BV)) 选举CVE补丁对。继PPatHF[35]之后，我们手动过滤了与漏洞无关的提交，并进一步细化了涉及与C无关的文件或函数外部修改的提交。过滤后，我们按时间戳对所有提交进行排序，**并为每个漏洞选择最早和最新的提交，形成CVE补丁对。最早的提交被视为source commit，最新的提交视为target commit**。如果漏洞只包含单个补丁，我们也将其排除在外。经过这个过程，我们最终确定了CVE及其对应的补丁对。【👈 译】

5. 数据集的格式：
```json

        {
        "unified_id": "{cve_id}_{source_project}_{target_project}_{target_version_hash}", // target_version_hash如果无，可以用所在版本号代替
        "cve_id": "CVE-2018-1118",
        "patch_type": "cross_repo|same_repo", // 明确标识补丁类型
        "projects": {
            "source": {
            "name": "Linux Kernel",
            "repo": "https://github.com/torvalds/linux",
            "language": "C"
            },
            "target": {
            "name": "Linux Kernel",
            "repo": "https://github.com/torvalds/linux",
            "language": "C"
            }
        },
        "versions": {
            "source": { // 有的项目vulnerable和patched只给出其中一个信息。若为空 使用unknown表示即可
            "vulnerable": {"version": "4.17", "commit": "55e49dc43a835b19567e62142cb1c87dc7db7b3c"}, // 有的项目version和commit只存在其一，如果为空用unknown标识
            "patched": {"version": "unknown", "commit": "unknown"} // 有的项目version和commit只存在其一，如果为空用unknown标识
            },
            "target": {
            "vulnerable": {"version": "unknown", "commit": "unknown"}, // 有的项目version和commit只存在其一，如果为空用unknown标识
            "patched": {"version": "4.9", "commit": "a875bc1c9ec116b7b2b2f15f8edc2a2ac3c51f99"}
            }
        },
        "source_dataset": "FixMorph",
        "metadata": {
            // 该字段根据项目的特点走，不必统一
        }
        }

```
6. backporting类型的判断。
    1. 爬取完成后，在整理的时候，你可以对每一条记录的backporting的类型进行判断。cross_version(同一个repo，同一个branch，不同版本),cross_repo（不同repo）,cross_branch（同一个repo，不同branch）。然后将结果填充到记录的metadata中。
    2. 判断的依据：
        - cross_version：
            - 如果source_commit和target_commit的repo是同一个，并且source_commit和target_commit的branch是同一个，并且source_commit和target_commit的版本不同，那么就是cross_version。
        - cross_repo：
            - 如果source_commit和target_commit的repo是不同的，那么就是cross_repo。
        - cross_branch：
            - 如果source_commit和target_commit的repo是同一个，并且source_commit和target_commit的branch是不同的，那么就是cross_branch。

7. 完成步骤：
    1. 首先你应该用我的代码去完成等价补丁的筛选。代码在/home/<USER>/C++的项目在一开始就可以直接过滤了，没必要再进行后续的等价补丁爬取过程。
    2. 然后你应该满足我的1-6的要求。
