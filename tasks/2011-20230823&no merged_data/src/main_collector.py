#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
主要的数据收集脚本
收集2011年至2023年8月23日的CVE等价补丁对
满足与merged_dataset.json不重复的要求
"""

import os
import sys
import json
import logging
import requests
import time
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Set
import re
from collections import defaultdict

# 添加项目路径
sys.path.append('/home/<USER>/src')
sys.path.append('/home/<USER>')

# 设置日志
def setup_logging():
    """设置日志系统"""
    log_dir = "/home/<USER>/2011-20230823&no merged_data/logs"
    os.makedirs(log_dir, exist_ok=True)
    
    log_file = os.path.join(log_dir, f"collection_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler()
        ]
    )
    
    return logging.getLogger(__name__)

class CVEDataCollector:
    """CVE数据收集器"""
    
    def __init__(self):
        self.logger = setup_logging()
        self.base_dir = "/home/<USER>/2011-20230823&no merged_data"
        self.data_dir = os.path.join(self.base_dir, "data")
        self.output_dir = os.path.join(self.base_dir, "output")

        # 设置代理
        self.setup_proxy()

        # 创建目录
        os.makedirs(self.data_dir, exist_ok=True)
        os.makedirs(self.output_dir, exist_ok=True)

        # 加载现有数据集信息
        self.load_existing_dataset_info()

        # 加载API配置
        self.load_api_config()

        # 初始化结果存储
        self.collected_patches = []

    def setup_proxy(self):
        """设置代理"""
        proxy_url = "http://172.17.124.253:7897"
        os.environ['http_proxy'] = proxy_url
        os.environ['https_proxy'] = proxy_url

        # 设置requests会话的代理 - 不使用代理，因为直连也能工作
        self.proxies = None  # 禁用代理

        self.logger.info(f"代理设置已禁用，使用直连")

    def load_existing_dataset_info(self):
        """加载现有数据集信息"""
        analysis_file = os.path.join(self.data_dir, "existing_dataset_analysis.json")
        
        if os.path.exists(analysis_file):
            with open(analysis_file, 'r', encoding='utf-8') as f:
                analysis = json.load(f)
            
            self.existing_cves = set(analysis['existing_cves'])
            self.existing_repos = set(analysis['existing_repos'])
            self.target_cwe_types = set(analysis['cwe_types'])
            
            self.logger.info(f"加载现有数据集信息: {len(self.existing_cves)} CVE, {len(self.existing_repos)} 仓库")
        else:
            self.logger.error("未找到现有数据集分析文件，请先运行analyze_existing_dataset.py")
            sys.exit(1)
    
    def load_api_config(self):
        """加载API配置"""
        try:
            config_path = "/home/<USER>/src/data_collection/config/sources.json"
            with open(config_path, 'r') as f:
                config = json.load(f)
            
            self.github_token = config.get('sources', {}).get('GitHub', {}).get('token')
            self.nvd_api_key = config.get('sources', {}).get('NVD', {}).get('api_key')
            
            self.logger.info("API配置加载成功")
        except Exception as e:
            self.logger.warning(f"加载API配置失败: {str(e)}")
            self.github_token = None
            self.nvd_api_key = None
    
    def get_nvd_cves_by_date_range(self, start_date: str, end_date: str) -> List[Dict]:
        """从NVD API获取指定日期范围的CVE"""
        self.logger.info(f"获取CVE数据: {start_date} 到 {end_date}")

        # NVD API 2.0限制：单次查询不能超过120天
        # 需要将大的时间范围拆分为多个小范围
        from datetime import datetime, timedelta

        start_dt = datetime.fromisoformat(start_date.replace('T00:00:00.000', ''))
        end_dt = datetime.fromisoformat(end_date.replace('T23:59:59.999', ''))

        all_cves = []
        current_start = start_dt

        while current_start <= end_dt:
            # 计算当前批次的结束时间（最多120天）
            current_end = min(current_start + timedelta(days=119), end_dt)

            # 格式化时间
            batch_start = current_start.strftime('%Y-%m-%dT00:00:00.000')
            batch_end = current_end.strftime('%Y-%m-%dT23:59:59.999')

            self.logger.info(f"获取批次数据: {batch_start} 到 {batch_end}")

            # 获取这个批次的数据
            batch_cves = self._get_nvd_cves_batch(batch_start, batch_end)
            all_cves.extend(batch_cves)

            # 移动到下一个批次
            current_start = current_end + timedelta(days=1)

            # 批次间延迟
            time.sleep(1)

        self.logger.info(f"总共获取 {len(all_cves)} 个CVE")
        return all_cves

    def _get_nvd_cves_batch(self, start_date: str, end_date: str) -> List[Dict]:
        """获取单个批次的CVE数据（不超过120天）"""
        cves = []
        start_index = 0
        results_per_page = 2000

        while True:
            try:
                url = "https://services.nvd.nist.gov/rest/json/cves/2.0"
                params = {
                    'pubStartDate': start_date,
                    'pubEndDate': end_date,
                    'startIndex': start_index,
                    'resultsPerPage': results_per_page
                }
                
                headers = {}
                if self.nvd_api_key:
                    headers['apiKey'] = self.nvd_api_key
                
                # 增加重试和更长超时
                response = None
                for retry in range(5):
                    try:
                        response = requests.get(url, params=params, headers=headers, timeout=60)
                        break
                    except (requests.exceptions.Timeout, requests.exceptions.ConnectionError,
                           requests.exceptions.SSLError, requests.exceptions.RequestException) as e:
                        if retry == 4:  # 最后一次重试
                            self.logger.error(f"NVD API请求最终失败: {str(e)}")
                            break
                        self.logger.warning(f"NVD API请求失败，重试 {retry + 1}/5: {str(e)}")
                        time.sleep(5 * (retry + 1))  # 递增延迟

                if response is None:
                    self.logger.error("NVD API请求失败: 无法获取响应")
                    break
                
                if response.status_code == 200:
                    data = response.json()
                    vulnerabilities = data.get('vulnerabilities', [])
                    
                    if not vulnerabilities:
                        break
                    
                    cves.extend(vulnerabilities)
                    start_index += results_per_page
                    
                    self.logger.info(f"已获取 {len(cves)} 个CVE")
                    
                    # API限制
                    time.sleep(0.6)
                    
                    # 如果返回的数量少于请求的数量，说明已经到最后一页
                    if len(vulnerabilities) < results_per_page:
                        break
                        
                else:
                    self.logger.error(f"NVD API请求失败: {response.status_code}")
                    break
                    
            except Exception as e:
                self.logger.error(f"获取CVE数据失败: {str(e)}")
                break
        
        self.logger.info(f"总共获取 {len(cves)} 个CVE")
        return cves
    
    def filter_cves_by_requirements(self, cves: List[Dict]) -> List[Dict]:
        """根据需求过滤CVE"""
        self.logger.info("开始过滤CVE...")
        
        filtered_cves = []
        
        for vuln in cves:
            cve_data = vuln.get('cve', {})
            cve_id = cve_data.get('id', '')
            
            # 1. 检查CVE编号是否重复
            if cve_id in self.existing_cves:
                continue
            
            # 2. 检查是否有GitHub引用
            github_refs = self.extract_github_references(cve_data)
            if not github_refs:
                continue
            
            # 3. 检查仓库是否重复
            repos_in_cve = set()
            for ref in github_refs:
                repo_url = self.extract_repo_from_github_url(ref)
                if repo_url:
                    repos_in_cve.add(repo_url)
            
            # 如果所有仓库都在现有数据集中，跳过
            if repos_in_cve and repos_in_cve.issubset(self.existing_repos):
                continue
            
            # 4. 检查CWE类型
            cwe_types = self.extract_cwe_types(cve_data)
            if not any(cwe in self.target_cwe_types for cwe in cwe_types):
                continue
            
            filtered_cves.append({
                'cve_data': cve_data,
                'github_refs': github_refs,
                'repos': list(repos_in_cve),
                'cwe_types': cwe_types
            })
        
        self.logger.info(f"过滤后剩余 {len(filtered_cves)} 个CVE")
        return filtered_cves
    
    def extract_github_references(self, cve_data: Dict) -> List[str]:
        """提取GitHub引用"""
        github_refs = []
        
        references = cve_data.get('references', [])
        for ref in references:
            url = ref.get('url', '')
            if 'github.com' in url.lower():
                github_refs.append(url)
        
        return github_refs
    
    def extract_repo_from_github_url(self, url: str) -> Optional[str]:
        """从GitHub URL提取仓库地址"""
        match = re.match(r'https://github\.com/([^/]+)/([^/]+)', url)
        if match:
            owner, repo = match.groups()
            return f"https://github.com/{owner}/{repo}"
        return None
    
    def extract_cwe_types(self, cve_data: Dict) -> List[str]:
        """提取CWE类型"""
        cwe_types = []
        
        weaknesses = cve_data.get('weaknesses', [])
        for weakness in weaknesses:
            for desc in weakness.get('description', []):
                if desc.get('lang') == 'en':
                    cwe_id = desc.get('value', '')
                    if cwe_id.startswith('CWE-'):
                        # 这里需要映射到具体的bug_type
                        bug_type = self.map_cwe_to_bug_type(cwe_id)
                        if bug_type:
                            cwe_types.append(bug_type)
        
        return cwe_types
    
    def map_cwe_to_bug_type(self, cwe_id: str) -> Optional[str]:
        """将CWE ID映射到bug_type"""
        cwe_mapping = {
            'CWE-476': 'NULL pointer dereference',
            'CWE-400': 'denial of service',
            'CWE-369': 'divide by zero',
            'CWE-122': 'heap overflow',
            'CWE-200': 'information leakage',
            'CWE-190': 'integer overflow',
            'CWE-119': 'memory overflow',
            'CWE-264': 'privilege escalation',
            'CWE-338': 'random number weakness',
            'CWE-121': 'stack overflow',
            'CWE-416': 'use-after-free'
        }
        
        return cwe_mapping.get(cwe_id)
    
    def collect_data_by_year(self, start_year: int = 2011, end_year: int = 2023):
        """按年份收集数据"""
        self.logger.info(f"开始收集 {start_year} 到 {end_year} 年的数据")
        
        for year in range(start_year, end_year + 1):
            if year == 2023:
                # 2023年只到8月23日
                start_date = f"{year}-01-01T00:00:00.000"
                end_date = "2023-08-23T23:59:59.999"
            else:
                start_date = f"{year}-01-01T00:00:00.000"
                end_date = f"{year}-12-31T23:59:59.999"
            
            self.logger.info(f"处理 {year} 年数据...")
            
            # 获取CVE数据
            cves = self.get_nvd_cves_by_date_range(start_date, end_date)
            
            # 过滤CVE
            filtered_cves = self.filter_cves_by_requirements(cves)
            
            # 处理每个CVE
            for cve_info in filtered_cves:
                self.process_single_cve(cve_info)
            
            # 保存年度结果
            self.save_yearly_results(year)
    
    def process_single_cve(self, cve_info: Dict):
        """处理单个CVE"""
        cve_id = cve_info['cve_data']['id']
        github_refs = cve_info['github_refs']
        cwe_types = cve_info['cwe_types']

        self.logger.info(f"处理CVE: {cve_id}")

        # 导入等价补丁收集器
        sys.path.append(os.path.dirname(__file__))
        from equivalent_patch_collector import EquivalentPatchCollector

        patch_collector = EquivalentPatchCollector(self.github_token)

        # 收集等价补丁对
        patch_record = patch_collector.collect_equivalent_patches(
            cve_id, github_refs, cwe_types
        )

        if patch_record:
            self.collected_patches.append(patch_record)
            self.logger.info(f"CVE {cve_id}: 成功收集补丁对")
        else:
            self.logger.info(f"CVE {cve_id}: 未能收集到有效补丁对")
    
    def save_yearly_results(self, year: int):
        """保存年度结果"""
        if not self.collected_patches:
            self.logger.info(f"{year}年没有收集到补丁对")
            return

        output_file = os.path.join(self.output_dir, f"patches_{year}.json")

        # 分析backporting类型
        from backporting_analyzer import BackportingAnalyzer
        analyzer = BackportingAnalyzer(self.github_token)

        updated_patches = analyzer.batch_analyze_records(self.collected_patches)

        # 保存结果
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(updated_patches, f, indent=2, ensure_ascii=False)

        self.logger.info(f"{year}年结果已保存: {output_file}, 共 {len(updated_patches)} 个补丁对")

        # 清空当年收集的补丁
        self.collected_patches = []
    
    def run(self):
        """运行数据收集"""
        self.logger.info("开始CVE等价补丁数据收集")
        
        try:
            self.collect_data_by_year()
            self.logger.info("数据收集完成")
        except Exception as e:
            self.logger.error(f"数据收集失败: {str(e)}")
            raise

def main():
    """主函数"""
    collector = CVEDataCollector()
    collector.run()

if __name__ == "__main__":
    main()
