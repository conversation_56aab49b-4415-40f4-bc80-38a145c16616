#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
等价补丁收集器
使用mystique标准收集等价补丁对
"""

import os
import sys
import json
import logging
import requests
import time
import re
from datetime import datetime
from typing import List, Dict, Any, Optional, Tuple
from collections import defaultdict

# 添加现有项目路径
sys.path.append('/home/<USER>/src')
sys.path.append('/home/<USER>')

class EquivalentPatchCollector:
    """等价补丁收集器"""
    
    def __init__(self, github_token: str = None):
        self.logger = logging.getLogger(__name__)
        self.github_token = github_token
        self.github_api_base = "https://api.github.com"

        # 设置代理
        self.setup_proxy()

        # 缓存
        self.repo_language_cache = {}
        self.commit_cache = {}

    def setup_proxy(self):
        """设置代理"""
        proxy_url = "http://172.17.124.253:7897"
        os.environ['http_proxy'] = proxy_url
        os.environ['https_proxy'] = proxy_url

        # 设置requests会话的代理 - GitHub需要代理
        self.proxies = {
            'http': proxy_url,
            'https': proxy_url
        }

    def is_cpp_project(self, owner: str, repo: str) -> bool:
        """判断是否为C/C++项目"""
        repo_key = f"{owner}/{repo}"
        
        if repo_key in self.repo_language_cache:
            return self.repo_language_cache[repo_key]
        
        try:
            url = f"{self.github_api_base}/repos/{owner}/{repo}/languages"
            headers = {
                'Accept': 'application/vnd.github.v3+json',
                'User-Agent': 'CVE-Patch-Collector/1.0'
            }
            if self.github_token:
                headers['Authorization'] = f'Bearer {self.github_token}'

            # 添加重试机制
            for attempt in range(3):
                try:
                    response = requests.get(url, headers=headers, proxies=self.proxies, timeout=30)
                    break
                except (requests.exceptions.Timeout, requests.exceptions.ConnectionError) as e:
                    if attempt == 2:  # 最后一次尝试
                        raise
                    self.logger.warning(f"API请求失败，重试 {attempt + 1}/3: {str(e)}")
                    time.sleep(2 ** attempt)  # 指数退避
            
            if response.status_code == 200:
                languages = response.json()
                
                # 检查是否包含C/C++语言
                cpp_languages = {'C', 'C++', 'Objective-C', 'Objective-C++'}
                total_bytes = sum(languages.values())
                cpp_bytes = sum(languages.get(lang, 0) for lang in cpp_languages)
                
                if total_bytes > 0:
                    cpp_ratio = cpp_bytes / total_bytes
                    is_cpp = cpp_ratio >= 0.3  # C/C++代码占比超过30%
                else:
                    is_cpp = False
                
                self.repo_language_cache[repo_key] = is_cpp
                return is_cpp
            else:
                # API失败时使用基于名称的判断
                return self._is_cpp_by_name(owner, repo)
                
        except Exception as e:
            self.logger.warning(f"检查项目语言失败 {repo_key}: {str(e)}")
            return self._is_cpp_by_name(owner, repo)
    
    def _is_cpp_by_name(self, owner: str, repo: str) -> bool:
        """基于名称判断是否为C/C++项目"""
        repo_name_lower = f"{owner}/{repo}".lower()
        
        # C/C++项目指示符
        cpp_indicators = [
            'linux', 'kernel', 'gcc', 'clang', 'openssl', 'sqlite',
            'postgres', 'mysql', 'nginx', 'apache', 'curl', 'git',
            'opencv', 'ffmpeg', 'chromium', 'firefox', 'webkit',
            'bind', 'glibc', 'zlib', 'libpng', 'libjpeg',
            'libxml', 'libssl', 'mbedtls', 'wolfssl', 'redis',
            'v8', 'llvm', 'boost', 'qt', 'gtk', 'cmake'
        ]
        
        # 非C/C++项目指示符
        non_cpp_indicators = [
            'node', 'npm', 'javascript', 'typescript', 'react', 'vue', 'angular',
            'java', 'spring', 'maven', 'gradle', 'kotlin',
            'python', 'django', 'flask', 'pip',
            'ruby', 'rails', 'gem',
            'php', 'laravel', 'composer',
            'go', 'golang',
            'rust', 'cargo',
            'swift', 'ios',
            'csharp', 'dotnet', 'nuget'
        ]
        
        # 检查非C/C++指示符
        for indicator in non_cpp_indicators:
            if indicator in repo_name_lower:
                return False
        
        # 检查C/C++指示符
        for indicator in cpp_indicators:
            if indicator in repo_name_lower:
                return True
        
        return False
    
    def extract_commits_from_github_refs(self, github_refs: List[str]) -> List[Dict[str, Any]]:
        """从GitHub引用中提取commit信息"""
        commits = []
        
        for ref_url in github_refs:
            commit_info = self._extract_commit_from_url(ref_url)
            if commit_info:
                commits.append(commit_info)
        
        return commits
    
    def _extract_commit_from_url(self, url: str) -> Optional[Dict[str, Any]]:
        """从URL中提取commit信息"""
        # 匹配commit URL
        commit_match = re.match(r'https://github\.com/([^/]+)/([^/]+)/commit/([a-f0-9]+)', url)
        if commit_match:
            owner, repo, commit_hash = commit_match.groups()
            
            # 检查是否为C/C++项目
            if not self.is_cpp_project(owner, repo):
                return None
            
            commit_info = self._get_commit_details(owner, repo, commit_hash)
            if commit_info:
                commit_info.update({
                    'owner': owner,
                    'repo': repo,
                    'commit_hash': commit_hash,
                    'commit_url': url,
                    'repository': f"https://github.com/{owner}/{repo}"
                })
                return commit_info
        
        # 匹配pull request URL（需要获取对应的commit）
        pr_match = re.match(r'https://github\.com/([^/]+)/([^/]+)/pull/(\d+)', url)
        if pr_match:
            owner, repo, pr_number = pr_match.groups()
            
            # 检查是否为C/C++项目
            if not self.is_cpp_project(owner, repo):
                return None
            
            # 获取PR对应的commit
            pr_commits = self._get_pr_commits(owner, repo, pr_number)
            if pr_commits:
                # 返回PR的最后一个commit
                last_commit = pr_commits[-1]
                return {
                    'owner': owner,
                    'repo': repo,
                    'commit_hash': last_commit['sha'],
                    'commit_url': f"https://github.com/{owner}/{repo}/commit/{last_commit['sha']}",
                    'repository': f"https://github.com/{owner}/{repo}",
                    'commit_date': last_commit.get('commit', {}).get('author', {}).get('date', ''),
                    'files_changed': []  # 需要单独获取
                }
        
        return None
    
    def _get_commit_details(self, owner: str, repo: str, commit_hash: str) -> Optional[Dict[str, Any]]:
        """获取commit详细信息"""
        commit_key = f"{owner}/{repo}/{commit_hash}"
        
        if commit_key in self.commit_cache:
            return self.commit_cache[commit_key]
        
        try:
            url = f"{self.github_api_base}/repos/{owner}/{repo}/commits/{commit_hash}"
            headers = {
                'Accept': 'application/vnd.github.v3+json',
                'User-Agent': 'CVE-Patch-Collector/1.0'
            }
            if self.github_token:
                headers['Authorization'] = f'Bearer {self.github_token}'

            # 添加重试机制
            for attempt in range(3):
                try:
                    response = requests.get(url, headers=headers, proxies=self.proxies, timeout=30)
                    break
                except (requests.exceptions.Timeout, requests.exceptions.ConnectionError) as e:
                    if attempt == 2:
                        raise
                    self.logger.warning(f"获取commit详情重试 {attempt + 1}/3: {str(e)}")
                    time.sleep(2 ** attempt)
            
            if response.status_code == 200:
                commit_data = response.json()
                
                # 提取文件变更信息
                files_changed = []
                for file_info in commit_data.get('files', []):
                    filename = file_info.get('filename', '')
                    if self._is_c_cpp_file(filename):
                        files_changed.append(filename)
                
                # 如果没有C/C++文件变更，跳过
                if not files_changed:
                    return None
                
                commit_info = {
                    'commit_date': commit_data.get('commit', {}).get('author', {}).get('date', ''),
                    'files_changed': files_changed,
                    'message': commit_data.get('commit', {}).get('message', ''),
                    'author': commit_data.get('commit', {}).get('author', {}).get('name', '')
                }
                
                self.commit_cache[commit_key] = commit_info
                return commit_info
            else:
                self.logger.warning(f"获取commit详情失败: {commit_key}, status: {response.status_code}")
                return None
                
        except Exception as e:
            self.logger.warning(f"获取commit详情异常: {commit_key}, error: {str(e)}")
            return None
    
    def _get_pr_commits(self, owner: str, repo: str, pr_number: str) -> List[Dict]:
        """获取PR的commits"""
        try:
            url = f"{self.github_api_base}/repos/{owner}/{repo}/pulls/{pr_number}/commits"
            headers = {
                'Accept': 'application/vnd.github.v3+json',
                'User-Agent': 'CVE-Patch-Collector/1.0'
            }
            if self.github_token:
                headers['Authorization'] = f'Bearer {self.github_token}'
            
            response = requests.get(url, headers=headers, proxies=self.proxies, timeout=10)
            
            if response.status_code == 200:
                return response.json()
            else:
                return []
                
        except Exception as e:
            self.logger.warning(f"获取PR commits失败: {owner}/{repo}/pull/{pr_number}, error: {str(e)}")
            return []
    
    def _is_c_cpp_file(self, filename: str) -> bool:
        """判断是否为C/C++文件"""
        cpp_extensions = ['.c', '.cpp', '.cc', '.cxx', '.h', '.hpp', '.hxx', '.c++']
        return any(filename.lower().endswith(ext) for ext in cpp_extensions)
    
    def apply_mystique_criteria(self, commits: List[Dict[str, Any]]) -> Optional[Tuple[Dict, Dict]]:
        """应用mystique标准选择补丁对"""
        if len(commits) < 2:
            return None
        
        # 按时间戳排序
        valid_commits = []
        for commit in commits:
            commit_date = commit.get('commit_date', '')
            if commit_date:
                try:
                    timestamp = datetime.fromisoformat(commit_date.replace('Z', '+00:00'))
                    valid_commits.append((timestamp, commit))
                except:
                    continue
        
        if len(valid_commits) < 2:
            return None
        
        # 按时间排序
        valid_commits.sort(key=lambda x: x[0])
        
        # 选择最早和最新的commit
        earliest_commit = valid_commits[0][1]
        latest_commit = valid_commits[-1][1]
        
        # 确保两个commit不同
        if earliest_commit['commit_hash'] == latest_commit['commit_hash']:
            return None
        
        return earliest_commit, latest_commit
    
    def create_patch_pair_record(self, cve_id: str, source_commit: Dict, target_commit: Dict, 
                               cwe_types: List[str]) -> Dict[str, Any]:
        """创建补丁对记录"""
        
        # 确定补丁类型
        source_repo = source_commit['repository']
        target_repo = target_commit['repository']
        patch_type = "same_repo" if source_repo == target_repo else "cross_repo"
        
        # 生成unified_id
        source_project = source_commit['repository'].replace('https://github.com/', '').replace('/', '_')
        target_project = target_commit['repository'].replace('https://github.com/', '').replace('/', '_')
        target_hash = target_commit['commit_hash'][:8]
        unified_id = f"{cve_id}_{source_project}_{target_project}_{target_hash}"
        
        # 构建记录
        record = {
            "unified_id": unified_id,
            "cve_id": cve_id,
            "patch_type": patch_type,
            "projects": {
                "source": {
                    "name": source_commit['repository'].replace('https://github.com/', ''),
                    "repo": source_commit['repository'],
                    "language": "C"
                },
                "target": {
                    "name": target_commit['repository'].replace('https://github.com/', ''),
                    "repo": target_commit['repository'],
                    "language": "C"
                }
            },
            "versions": {
                "source": {
                    "vulnerable": {"version": "unknown", "commit": "unknown"},
                    "patched": {"version": "unknown", "commit": source_commit['commit_hash']}
                },
                "target": {
                    "vulnerable": {"version": "unknown", "commit": "unknown"},
                    "patched": {"version": "unknown", "commit": target_commit['commit_hash']}
                }
            },
            "source_dataset": "CustomEquivalentPatchCrawler",
            "metadata": {
                "cve_id": cve_id,
                "year": int(cve_id.split('-')[1]) if cve_id.startswith('CVE-') else None,
                "bug_type": cwe_types[0] if cwe_types else "unknown",
                "source_commit_date": source_commit.get('commit_date', ''),
                "target_commit_date": target_commit.get('commit_date', ''),
                "source_files_changed": source_commit.get('files_changed', []),
                "target_files_changed": target_commit.get('files_changed', []),
                "source_commit_url": source_commit.get('commit_url', ''),
                "target_commit_url": target_commit.get('commit_url', ''),
                "backporting_type": "unknown"  # 后续分析
            }
        }
        
        return record
    
    def collect_equivalent_patches(self, cve_id: str, github_refs: List[str], 
                                 cwe_types: List[str]) -> Optional[Dict[str, Any]]:
        """收集等价补丁对"""
        self.logger.info(f"收集CVE {cve_id} 的等价补丁")
        
        # 提取commits
        commits = self.extract_commits_from_github_refs(github_refs)
        
        if not commits:
            self.logger.info(f"CVE {cve_id}: 未找到有效的C/C++项目commits")
            return None
        
        # 应用mystique标准
        patch_pair = self.apply_mystique_criteria(commits)
        
        if not patch_pair:
            self.logger.info(f"CVE {cve_id}: 不满足mystique标准（需要至少2个不同的commits）")
            return None
        
        source_commit, target_commit = patch_pair
        
        # 创建补丁对记录
        record = self.create_patch_pair_record(cve_id, source_commit, target_commit, cwe_types)
        
        self.logger.info(f"CVE {cve_id}: 成功创建补丁对 ({record['patch_type']})")
        
        return record
