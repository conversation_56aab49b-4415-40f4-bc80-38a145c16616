#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Backporting类型分析器
分析补丁对的backporting类型：cross_version, cross_repo, cross_branch
"""

import os
import sys
import json
import logging
import requests
import re
import time
from typing import Dict, Any, Optional, Tuple

class BackportingAnalyzer:
    """Backporting类型分析器"""
    
    def __init__(self, github_token: str = None):
        self.logger = logging.getLogger(__name__)
        self.github_token = github_token
        self.github_api_base = "https://api.github.com"

        # 设置代理
        self.setup_proxy()

        # 缓存
        self.commit_branch_cache = {}
        self.commit_version_cache = {}

    def setup_proxy(self):
        """设置代理"""
        proxy_url = "http://172.17.124.253:7897"
        os.environ['http_proxy'] = proxy_url
        os.environ['https_proxy'] = proxy_url

        # 设置requests会话的代理
        self.proxies = {
            'http': proxy_url,
            'https': proxy_url
        }

    def analyze_backporting_type(self, record: Dict[str, Any]) -> str:
        """分析backporting类型（简化版本）"""
        source_repo = record['projects']['source']['repo']
        target_repo = record['projects']['target']['repo']
        source_commit = record['versions']['source']['patched']['commit']
        target_commit = record['versions']['target']['patched']['commit']

        # 如果是不同仓库，直接返回cross_repo
        if source_repo != target_repo:
            return "cross_repo"

        # 同一仓库，简化判断逻辑
        source_owner, source_repo_name = self._parse_repo_url(source_repo)

        if not source_owner or not source_repo_name:
            return "unknown"

        # 获取两个commit的时间信息来推断版本差异
        source_date = self._get_commit_date(source_owner, source_repo_name, source_commit)
        target_date = self._get_commit_date(source_owner, source_repo_name, target_commit)

        if source_date and target_date:
            # 计算时间差
            try:
                from datetime import datetime
                source_dt = datetime.fromisoformat(source_date.replace('Z', '+00:00'))
                target_dt = datetime.fromisoformat(target_date.replace('Z', '+00:00'))

                time_diff_days = abs((target_dt - source_dt).days)

                # 基于时间差判断类型
                if time_diff_days > 365:  # 超过1年，可能是跨版本
                    return "cross_version"
                elif time_diff_days > 30:  # 超过1个月，可能是跨分支
                    return "cross_branch"
                else:
                    return "same_version"
            except:
                pass

        # 默认返回same_repo（因为已经确认是同一仓库）
        return "same_repo"
    
    def _parse_repo_url(self, repo_url: str) -> Tuple[Optional[str], Optional[str]]:
        """解析仓库URL"""
        match = re.match(r'https://github\.com/([^/]+)/([^/]+)', repo_url)
        if match:
            return match.groups()
        return None, None
    
    def _get_commit_date(self, owner: str, repo: str, commit_hash: str) -> Optional[str]:
        """获取commit的提交日期"""
        cache_key = f"{owner}/{repo}/{commit_hash}_date"

        if cache_key in self.commit_branch_cache:  # 复用缓存
            return self.commit_branch_cache[cache_key]

        try:
            # 获取commit详情
            url = f"{self.github_api_base}/repos/{owner}/{repo}/commits/{commit_hash}"
            headers = {
                'Accept': 'application/vnd.github.v3+json',
                'User-Agent': 'CVE-Patch-Collector/1.0'
            }
            if self.github_token:
                headers['Authorization'] = f'Bearer {self.github_token}'

            # 添加重试机制
            for attempt in range(3):
                try:
                    response = requests.get(url, headers=headers, proxies=self.proxies, timeout=30)
                    break
                except (requests.exceptions.Timeout, requests.exceptions.ConnectionError) as e:
                    if attempt == 2:
                        raise
                    self.logger.warning(f"获取commit日期重试 {attempt + 1}/3: {str(e)}")
                    time.sleep(2 ** attempt)

            if response.status_code == 200:
                commit_data = response.json()
                commit_date = commit_data.get('commit', {}).get('author', {}).get('date', '')
                self.commit_branch_cache[cache_key] = commit_date
                return commit_date
            else:
                self.logger.warning(f"获取commit日期失败: {cache_key}, status: {response.status_code}")
                return None

        except Exception as e:
            self.logger.warning(f"获取commit日期异常: {cache_key}, error: {str(e)}")
            return None
    
    def _get_commit_version(self, owner: str, repo: str, commit_hash: str) -> Optional[str]:
        """获取commit对应的版本"""
        cache_key = f"{owner}/{repo}/{commit_hash}"
        
        if cache_key in self.commit_version_cache:
            return self.commit_version_cache[cache_key]
        
        try:
            # 获取包含该commit的tags
            url = f"{self.github_api_base}/repos/{owner}/{repo}/commits/{commit_hash}/tags"
            headers = {}
            if self.github_token:
                headers['Authorization'] = f'token {self.github_token}'
            
            response = requests.get(url, headers=headers, proxies=self.proxies, timeout=10)
            
            if response.status_code == 200:
                tags_data = response.json()
                if tags_data:
                    # 返回第一个tag作为版本
                    version = tags_data[0]['name']
                    self.commit_version_cache[cache_key] = version
                    return version
                else:
                    # 没有tag，尝试通过commit时间推断版本
                    version = self._infer_version_by_time(owner, repo, commit_hash)
                    self.commit_version_cache[cache_key] = version
                    return version
            else:
                return None
                
        except Exception as e:
            self.logger.warning(f"获取commit版本异常: {cache_key}, error: {str(e)}")
            return None
    
    def _infer_version_by_time(self, owner: str, repo: str, commit_hash: str) -> Optional[str]:
        """通过时间推断版本"""
        try:
            # 获取commit详情
            url = f"{self.github_api_base}/repos/{owner}/{repo}/commits/{commit_hash}"
            headers = {}
            if self.github_token:
                headers['Authorization'] = f'token {self.github_token}'
            
            response = requests.get(url, headers=headers, proxies=self.proxies, timeout=10)
            
            if response.status_code == 200:
                commit_data = response.json()
                commit_date = commit_data.get('commit', {}).get('author', {}).get('date', '')
                
                if commit_date:
                    # 简单的版本推断：使用年份.月份
                    from datetime import datetime
                    dt = datetime.fromisoformat(commit_date.replace('Z', '+00:00'))
                    return f"{dt.year}.{dt.month:02d}"
                
            return None
            
        except Exception as e:
            self.logger.warning(f"推断版本失败: {owner}/{repo}/{commit_hash}, error: {str(e)}")
            return None
    
    def update_record_with_backporting_type(self, record: Dict[str, Any]) -> Dict[str, Any]:
        """更新记录的backporting类型"""
        backporting_type = self.analyze_backporting_type(record)
        
        # 更新metadata
        if 'metadata' not in record:
            record['metadata'] = {}
        
        record['metadata']['backporting_type'] = backporting_type
        
        # 添加详细的分析信息
        record['metadata']['backporting_analysis'] = {
            'source_repo': record['projects']['source']['repo'],
            'target_repo': record['projects']['target']['repo'],
            'source_commit': record['versions']['source']['patched']['commit'],
            'target_commit': record['versions']['target']['patched']['commit'],
            'analysis_result': backporting_type
        }
        
        self.logger.info(f"CVE {record['cve_id']}: backporting类型 = {backporting_type}")
        
        return record
    
    def batch_analyze_records(self, records: list) -> list:
        """批量分析记录"""
        self.logger.info(f"开始批量分析 {len(records)} 个记录的backporting类型")
        
        updated_records = []
        
        for i, record in enumerate(records):
            try:
                updated_record = self.update_record_with_backporting_type(record)
                updated_records.append(updated_record)
                
                if (i + 1) % 10 == 0:
                    self.logger.info(f"已处理 {i + 1}/{len(records)} 个记录")
                    
            except Exception as e:
                self.logger.error(f"分析记录失败: {record.get('cve_id', 'unknown')}, error: {str(e)}")
                # 添加默认的backporting类型
                if 'metadata' not in record:
                    record['metadata'] = {}
                record['metadata']['backporting_type'] = 'unknown'
                updated_records.append(record)
        
        # 统计结果
        type_counts = {}
        for record in updated_records:
            bp_type = record.get('metadata', {}).get('backporting_type', 'unknown')
            type_counts[bp_type] = type_counts.get(bp_type, 0) + 1
        
        self.logger.info("Backporting类型统计:")
        for bp_type, count in sorted(type_counts.items()):
            self.logger.info(f"  {bp_type}: {count}")
        
        return updated_records
