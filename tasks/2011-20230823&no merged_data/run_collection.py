#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
生产环境数据收集脚本
处理网络问题，支持后台运行
"""

import os
import sys
import json
import logging
import time
import signal
from datetime import datetime

# 添加路径
sys.path.append('/home/<USER>/2011-20230823&no merged_data/src')

class ProductionCollector:
    """生产环境收集器"""
    
    def __init__(self):
        self.setup_logging()
        self.logger = logging.getLogger(__name__)
        self.running = True
        self.collected_count = 0
        
        # 设置信号处理
        signal.signal(signal.SIGINT, self.signal_handler)
        signal.signal(signal.SIGTERM, self.signal_handler)
        
    def setup_logging(self):
        """设置日志系统"""
        log_dir = "/home/<USER>/2011-20230823&no merged_data/logs"
        os.makedirs(log_dir, exist_ok=True)
        
        log_file = os.path.join(log_dir, f"production_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log")
        
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler()
            ]
        )
        
        self.log_file = log_file
    
    def signal_handler(self, signum, frame):
        """信号处理器"""
        self.logger.info(f"收到信号 {signum}，准备优雅退出...")
        self.running = False
    
    def test_network_connectivity(self):
        """测试网络连接"""
        self.logger.info("测试网络连接...")
        
        import requests
        
        test_urls = [
            "https://httpbin.org/get",  # 简单的HTTP测试
            "https://api.github.com",   # GitHub API
            "https://services.nvd.nist.gov/rest/json/cves/2.0?resultsPerPage=1"  # NVD API
        ]
        
        for url in test_urls:
            try:
                response = requests.get(url, timeout=10)
                if response.status_code == 200:
                    self.logger.info(f"✅ {url}: 连接正常")
                else:
                    self.logger.warning(f"⚠️ {url}: 状态码 {response.status_code}")
            except Exception as e:
                self.logger.error(f"❌ {url}: 连接失败 - {str(e)}")
                return False
        
        return True
    
    def load_api_config(self):
        """加载API配置"""
        try:
            config_path = "/home/<USER>/src/data_collection/config/sources.json"
            with open(config_path, 'r') as f:
                config = json.load(f)
            
            github_token = config.get('sources', {}).get('GitHub', {}).get('token')
            nvd_api_key = config.get('sources', {}).get('NVD', {}).get('api_key')
            
            if github_token:
                self.logger.info(f"✅ GitHub token加载成功: {github_token[:10]}...")
            else:
                self.logger.warning("⚠️ 未找到GitHub token")
            
            if nvd_api_key:
                self.logger.info(f"✅ NVD API key加载成功: {nvd_api_key[:10]}...")
            else:
                self.logger.warning("⚠️ 未找到NVD API key")
            
            return github_token, nvd_api_key
            
        except Exception as e:
            self.logger.error(f"❌ 加载API配置失败: {str(e)}")
            return None, None
    
    def test_github_api(self, github_token):
        """测试GitHub API"""
        if not github_token:
            self.logger.warning("⚠️ 跳过GitHub API测试（无token）")
            return False
        
        import requests
        
        try:
            headers = {
                'Authorization': f'Bearer {github_token}',
                'Accept': 'application/vnd.github.v3+json',
                'User-Agent': 'CVE-Patch-Collector/1.0'
            }
            
            # 测试API限制
            response = requests.get('https://api.github.com/rate_limit', headers=headers, timeout=10)
            
            if response.status_code == 200:
                rate_limit = response.json()
                remaining = rate_limit['rate']['remaining']
                reset_time = rate_limit['rate']['reset']
                
                self.logger.info(f"✅ GitHub API测试成功")
                self.logger.info(f"   剩余请求数: {remaining}")
                self.logger.info(f"   重置时间: {datetime.fromtimestamp(reset_time)}")
                
                return remaining > 100  # 确保有足够的API调用次数
            else:
                self.logger.error(f"❌ GitHub API测试失败: {response.status_code}")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ GitHub API测试异常: {str(e)}")
            return False
    
    def run_small_scale_test(self, github_token, nvd_api_key):
        """运行小规模测试"""
        self.logger.info("开始小规模数据收集测试...")
        
        try:
            from main_collector import CVEDataCollector
            
            # 创建收集器实例
            collector = CVEDataCollector()
            
            # 只收集2023年的少量数据进行测试
            self.logger.info("测试收集2023年1月的CVE数据...")
            
            # 获取少量CVE进行测试
            cves = collector.get_nvd_cves_by_date_range(
                "2023-01-01T00:00:00.000",
                "2023-01-31T23:59:59.999"
            )
            
            if cves:
                self.logger.info(f"✅ 成功获取 {len(cves)} 个CVE")
                
                # 过滤CVE
                filtered_cves = collector.filter_cves_by_requirements(cves[:10])  # 只处理前10个
                
                if filtered_cves:
                    self.logger.info(f"✅ 过滤后剩余 {len(filtered_cves)} 个CVE")
                    
                    # 处理每个CVE
                    for cve_info in filtered_cves[:3]:  # 只处理前3个
                        if not self.running:
                            break
                        
                        collector.process_single_cve(cve_info)
                        self.collected_count += 1
                        
                        # 添加延迟避免API限制
                        time.sleep(2)
                    
                    # 保存测试结果
                    if collector.collected_patches:
                        collector.save_yearly_results(2023)
                        self.logger.info(f"✅ 小规模测试完成，收集到 {len(collector.collected_patches)} 个补丁对")
                        return True
                else:
                    self.logger.warning("⚠️ 过滤后无有效CVE")
            else:
                self.logger.warning("⚠️ 未获取到CVE数据")
                
        except Exception as e:
            self.logger.error(f"❌ 小规模测试失败: {str(e)}")
            
        return False
    
    def run_full_collection(self, github_token, nvd_api_key):
        """运行完整数据收集"""
        self.logger.info("开始完整数据收集...")
        
        try:
            from main_collector import CVEDataCollector
            
            collector = CVEDataCollector()
            
            # 按年份收集数据
            for year in range(2011, 2024):  # 2011-2023
                if not self.running:
                    break
                
                self.logger.info(f"处理 {year} 年数据...")
                
                if year == 2023:
                    # 2023年只到8月23日
                    start_date = f"{year}-01-01T00:00:00.000"
                    end_date = "2023-08-23T23:59:59.999"
                else:
                    start_date = f"{year}-01-01T00:00:00.000"
                    end_date = f"{year}-12-31T23:59:59.999"
                
                # 获取CVE数据
                cves = collector.get_nvd_cves_by_date_range(start_date, end_date)
                
                if cves:
                    # 过滤CVE
                    filtered_cves = collector.filter_cves_by_requirements(cves)
                    
                    # 处理每个CVE
                    for cve_info in filtered_cves:
                        if not self.running:
                            break
                        
                        collector.process_single_cve(cve_info)
                        self.collected_count += 1
                        
                        # 每处理10个CVE记录一次进度
                        if self.collected_count % 10 == 0:
                            self.logger.info(f"已处理 {self.collected_count} 个CVE")
                        
                        # 添加延迟避免API限制
                        time.sleep(1)
                    
                    # 保存年度结果
                    collector.save_yearly_results(year)
                
                # 年度间隔
                time.sleep(5)
            
            self.logger.info(f"✅ 完整数据收集完成，总共处理 {self.collected_count} 个CVE")
            
        except Exception as e:
            self.logger.error(f"❌ 完整数据收集失败: {str(e)}")
    
    def run(self):
        """主运行函数"""
        self.logger.info("=== 生产环境数据收集开始 ===")
        self.logger.info(f"日志文件: {self.log_file}")
        self.logger.info(f"进程ID: {os.getpid()}")
        
        # 1. 测试网络连接
        if not self.test_network_connectivity():
            self.logger.error("❌ 网络连接测试失败，退出")
            return
        
        # 2. 加载API配置
        github_token, nvd_api_key = self.load_api_config()
        
        # 3. 测试GitHub API
        if not self.test_github_api(github_token):
            self.logger.error("❌ GitHub API测试失败，退出")
            return
        
        # 4. 运行小规模测试
        if self.run_small_scale_test(github_token, nvd_api_key):
            self.logger.info("✅ 小规模测试成功，开始完整收集")
            
            # 5. 运行完整数据收集
            self.run_full_collection(github_token, nvd_api_key)
        else:
            self.logger.error("❌ 小规模测试失败，不进行完整收集")
        
        self.logger.info("=== 数据收集结束 ===")

def main():
    """主函数"""
    collector = ProductionCollector()
    collector.run()

if __name__ == "__main__":
    main()
