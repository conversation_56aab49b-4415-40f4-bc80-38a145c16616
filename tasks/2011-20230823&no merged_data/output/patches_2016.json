[{"unified_id": "CVE-2016-2315_git_git_git_git_de1e67d0", "cve_id": "CVE-2016-2315", "patch_type": "same_repo", "projects": {"source": {"name": "git/git", "repo": "https://github.com/git/git", "language": "C"}, "target": {"name": "git/git", "repo": "https://github.com/git/git", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "34fa79a6cde56d6d428ab0d3160cb094ebad3305"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "de1e67d0703894cb6ea782e36abb63976ab07e60"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2016-2315", "year": 2016, "bug_type": "memory overflow", "source_commit_date": "2015-09-24T21:08:19Z", "target_commit_date": "2016-02-11T22:28:36Z", "source_files_changed": ["compat/nedmalloc/nedmalloc.c", "fast-import.c", "revision.c"], "target_files_changed": ["builtin/pack-objects.c", "builtin/rev-list.c", "list-objects.c", "list-objects.h", "pack-bitmap-write.c", "pack-bitmap.c", "reachable.c", "revision.c", "revision.h"], "source_commit_url": "https://github.com/git/git/commit/34fa79a6cde56d6d428ab0d3160cb094ebad3305", "target_commit_url": "https://github.com/git/git/commit/de1e67d0703894cb6ea782e36abb63976ab07e60", "backporting_type": "cross_branch", "backporting_analysis": {"source_repo": "https://github.com/git/git", "target_repo": "https://github.com/git/git", "source_commit": "34fa79a6cde56d6d428ab0d3160cb094ebad3305", "target_commit": "de1e67d0703894cb6ea782e36abb63976ab07e60", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2016-2533_python-pillow_Pillow_python-pillow_Pillow_c581d8ab", "cve_id": "CVE-2016-2533", "patch_type": "same_repo", "projects": {"source": {"name": "python-pillow/Pillow", "repo": "https://github.com/python-pillow/Pillow", "language": "C"}, "target": {"name": "python-pillow/Pillow", "repo": "https://github.com/python-pillow/Pillow", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "5bdf54b5a76b54fb00bd05f2d733e0a4173eefc9"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "c581d8abfbf38e2eeae8e516cd4b99cc366a9574"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2016-2533", "year": 2016, "bug_type": "memory overflow", "source_commit_date": "2016-02-02T13:46:26Z", "target_commit_date": "2016-02-04T12:24:47Z", "source_files_changed": ["libImaging/PcdDecode.c"], "target_files_changed": [], "source_commit_url": "https://github.com/python-pillow/Pillow/commit/5bdf54b5a76b54fb00bd05f2d733e0a4173eefc9#diff-8ff6909c159597e22288ad818938fd6b", "target_commit_url": "https://github.com/python-pillow/Pillow/commit/c581d8abfbf38e2eeae8e516cd4b99cc366a9574", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/python-pillow/Pillow", "target_repo": "https://github.com/python-pillow/Pillow", "source_commit": "5bdf54b5a76b54fb00bd05f2d733e0a4173eefc9", "target_commit": "c581d8abfbf38e2eeae8e516cd4b99cc366a9574", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2016-4009_python-pillow_Pillow_python-pillow_Pillow_95a25a0d", "cve_id": "CVE-2016-4009", "patch_type": "same_repo", "projects": {"source": {"name": "python-pillow/Pillow", "repo": "https://github.com/python-pillow/Pillow", "language": "C"}, "target": {"name": "python-pillow/Pillow", "repo": "https://github.com/python-pillow/Pillow", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "4e0d9b0b9740d258ade40cce248c93777362ac1e"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "95a25a0d82f414a52e174eb5389d485d4b3ddf34"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2016-4009", "year": 2016, "bug_type": "memory overflow", "source_commit_date": "2016-02-04T06:54:12Z", "target_commit_date": "2016-02-04T14:57:57Z", "source_files_changed": ["libImaging/Resample.c"], "target_files_changed": [], "source_commit_url": "https://github.com/python-pillow/Pillow/commit/4e0d9b0b9740d258ade40cce248c93777362ac1e", "target_commit_url": "https://github.com/python-pillow/Pillow/commit/95a25a0d82f414a52e174eb5389d485d4b3ddf34", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/python-pillow/Pillow", "target_repo": "https://github.com/python-pillow/Pillow", "source_commit": "4e0d9b0b9740d258ade40cce248c93777362ac1e", "target_commit": "95a25a0d82f414a52e174eb5389d485d4b3ddf34", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2016-1709_googlei18n_sfntly_googlei18n_sfntly_468cad54", "cve_id": "CVE-2016-1709", "patch_type": "same_repo", "projects": {"source": {"name": "googlei18n/sfntly", "repo": "https://github.com/googlei18n/sfntly", "language": "C"}, "target": {"name": "googlei18n/sfntly", "repo": "https://github.com/googlei18n/sfntly", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "c56b85408bab232efd7e650f0994272a174e3b92"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "468cad540fa1b0027cad60456f53feabecdce2bc"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2016-1709", "year": 2016, "bug_type": "memory overflow", "source_commit_date": "2016-06-10T06:30:26Z", "target_commit_date": "2016-06-10T22:47:52Z", "source_files_changed": ["cpp/src/sfntly/data/byte_array.cc"], "target_files_changed": ["cpp/src/sfntly/data/byte_array.cc"], "source_commit_url": "https://github.com/googlei18n/sfntly/commit/c56b85408bab232efd7e650f0994272a174e3b92", "target_commit_url": "https://github.com/googlei18n/sfntly/commit/468cad540fa1b0027cad60456f53feabecdce2bc", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/googlei18n/sfntly", "target_repo": "https://github.com/googlei18n/sfntly", "source_commit": "c56b85408bab232efd7e650f0994272a174e3b92", "target_commit": "468cad540fa1b0027cad60456f53feabecdce2bc", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2015-8949_perl5-dbi_DBD-mysql_perl5-dbi_DBD-mysql_cf0aa775", "cve_id": "CVE-2015-8949", "patch_type": "same_repo", "projects": {"source": {"name": "perl5-dbi/DBD-mysql", "repo": "https://github.com/perl5-dbi/DBD-mysql", "language": "C"}, "target": {"name": "perl5-dbi/DBD-mysql", "repo": "https://github.com/perl5-dbi/DBD-mysql", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "2e1cbd0034cf0041f832ba81d07c24db886782d8"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "cf0aa7751f6ef8445e9310a64b14dc81460ca156"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2015-8949", "year": 2015, "bug_type": "use-after-free", "source_commit_date": "2015-11-14T22:06:12Z", "target_commit_date": "2015-11-22T14:22:05Z", "source_files_changed": [], "target_files_changed": ["dbdimp.c"], "source_commit_url": "https://github.com/perl5-dbi/DBD-mysql/commit/2e1cbd0034cf0041f832ba81d07c24db886782d8", "target_commit_url": "https://github.com/perl5-dbi/DBD-mysql/commit/cf0aa7751f6ef8445e9310a64b14dc81460ca156", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/perl5-dbi/DBD-mysql", "target_repo": "https://github.com/perl5-dbi/DBD-mysql", "source_commit": "2e1cbd0034cf0041f832ba81d07c24db886782d8", "target_commit": "cf0aa7751f6ef8445e9310a64b14dc81460ca156", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2016-7115_haakonnessjoen_MAC-Telnet_haakonnessjoen_MAC-Telnet_b69d1172", "cve_id": "CVE-2016-7115", "patch_type": "same_repo", "projects": {"source": {"name": "haakonnessjoen/MAC-Telnet", "repo": "https://github.com/haakonnessjoen/MAC-Telnet", "language": "C"}, "target": {"name": "haakonnessjoen/MAC-Telnet", "repo": "https://github.com/haakonnessjoen/MAC-Telnet", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "9d946fa173acadcff7c80171d9b1186fab2e8daf"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "b69d11727d4f0f8cf719c79e3fb700f55ca03e9a"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2016-7115", "year": 2016, "bug_type": "memory overflow", "source_commit_date": "2016-08-29T19:07:50Z", "target_commit_date": "2016-08-30T09:27:39Z", "source_files_changed": [], "target_files_changed": ["mactelnet.c", "mactelnetd.c", "protocol.c"], "source_commit_url": "https://github.com/haakonnessjoen/MAC-Telnet/commit/9d946fa173acadcff7c80171d9b1186fab2e8daf", "target_commit_url": "https://github.com/haakonnessjoen/MAC-Telnet/commit/b69d11727d4f0f8cf719c79e3fb700f55ca03e9a", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/haakonnessjoen/MAC-Telnet", "target_repo": "https://github.com/haakonnessjoen/MAC-Telnet", "source_commit": "9d946fa173acadcff7c80171d9b1186fab2e8daf", "target_commit": "b69d11727d4f0f8cf719c79e3fb700f55ca03e9a", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2016-7568_libgd_libgd_php_php-src_c18263e0", "cve_id": "CVE-2016-7568", "patch_type": "cross_repo", "projects": {"source": {"name": "libgd/libgd", "repo": "https://github.com/libgd/libgd", "language": "C"}, "target": {"name": "php/php-src", "repo": "https://github.com/php/php-src", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "40bec0f38f50e8510f5bb71a82f516d46facde03"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "c18263e0e0769faee96a5d0ee04b750c442783c6"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2016-7568", "year": 2016, "bug_type": "integer overflow", "source_commit_date": "2016-09-16T08:54:34Z", "target_commit_date": "2016-09-16T09:40:36Z", "source_files_changed": ["src/gd_webp.c"], "target_files_changed": ["ext/gd/libgd/gd_webp.c"], "source_commit_url": "https://github.com/libgd/libgd/commit/40bec0f38f50e8510f5bb71a82f516d46facde03", "target_commit_url": "https://github.com/php/php-src/commit/c18263e0e0769faee96a5d0ee04b750c442783c6", "backporting_type": "cross_repo", "backporting_analysis": {"source_repo": "https://github.com/libgd/libgd", "target_repo": "https://github.com/php/php-src", "source_commit": "40bec0f38f50e8510f5bb71a82f516d46facde03", "target_commit": "c18263e0e0769faee96a5d0ee04b750c442783c6", "analysis_result": "cross_repo"}}}, {"unified_id": "CVE-2016-9535_vadz_libtiff_vadz_libtiff_6a984bf7", "cve_id": "CVE-2016-9535", "patch_type": "same_repo", "projects": {"source": {"name": "vadz/libtiff", "repo": "https://github.com/vadz/libtiff", "language": "C"}, "target": {"name": "vadz/libtiff", "repo": "https://github.com/vadz/libtiff", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "3ca657a8793dd011bf869695d72ad31c779c3cc1"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "6a984bf7905c6621281588431f384e79d11a2e33"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2016-9535", "year": 2016, "bug_type": "memory overflow", "source_commit_date": "2016-10-31T17:24:26Z", "target_commit_date": "2016-11-04T09:19:13Z", "source_files_changed": ["libtiff/tif_predict.c", "libtiff/tif_predict.h"], "target_files_changed": ["libtiff/tif_predict.c"], "source_commit_url": "https://github.com/vadz/libtiff/commit/3ca657a8793dd011bf869695d72ad31c779c3cc1", "target_commit_url": "https://github.com/vadz/libtiff/commit/6a984bf7905c6621281588431f384e79d11a2e33", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/vadz/libtiff", "target_repo": "https://github.com/vadz/libtiff", "source_commit": "3ca657a8793dd011bf869695d72ad31c779c3cc1", "target_commit": "6a984bf7905c6621281588431f384e79d11a2e33", "analysis_result": "same_version"}}}]