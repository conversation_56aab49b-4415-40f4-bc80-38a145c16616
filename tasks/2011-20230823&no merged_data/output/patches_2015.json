[{"unified_id": "CVE-2015-4590_bblanchon_<PERSON><PERSON>uino<PERSON>son_bblanchon_ArduinoJson_5e7b9ec6", "cve_id": "CVE-2015-4590", "patch_type": "same_repo", "projects": {"source": {"name": "b<PERSON><PERSON><PERSON>/<PERSON><PERSON>", "repo": "https://github.com/bblanchon/ArduinoJson", "language": "C"}, "target": {"name": "b<PERSON><PERSON><PERSON>/<PERSON><PERSON>", "repo": "https://github.com/bblanchon/ArduinoJson", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "4086db84fdf55e6f79056a53241bf94cc888ce2a"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "5e7b9ec688d79e7b16ec7064e1d37e8481a31e72"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2015-4590", "year": 2015, "bug_type": "memory overflow", "source_commit_date": "2015-06-10T01:31:00Z", "target_commit_date": "2015-06-10T19:31:22Z", "source_files_changed": [], "target_files_changed": ["src/Internals/QuotedString.cpp", "test/QuotedString_ExtractFrom_Tests.cpp"], "source_commit_url": "https://github.com/bblanchon/ArduinoJson/commit/4086db84fdf55e6f79056a53241bf94cc888ce2a", "target_commit_url": "https://github.com/bblanchon/ArduinoJson/commit/5e7b9ec688d79e7b16ec7064e1d37e8481a31e72", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/bblanchon/ArduinoJson", "target_repo": "https://github.com/bblanchon/ArduinoJson", "source_commit": "4086db84fdf55e6f79056a53241bf94cc888ce2a", "target_commit": "5e7b9ec688d79e7b16ec7064e1d37e8481a31e72", "analysis_result": "same_version"}}}]