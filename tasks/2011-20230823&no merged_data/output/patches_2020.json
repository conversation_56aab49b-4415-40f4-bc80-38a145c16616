[{"unified_id": "CVE-2018-14553_libgd_libgd_libgd_libgd_441cbfed", "cve_id": "CVE-2018-14553", "patch_type": "same_repo", "projects": {"source": {"name": "libgd/libgd", "repo": "https://github.com/libgd/libgd", "language": "C"}, "target": {"name": "libgd/libgd", "repo": "https://github.com/libgd/libgd", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "a93eac0e843148dc2d631c3ba80af17e9c8c860f"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "441cbfed60ebf6cb63b8ce120ed0a82b15e7aaf8"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2018-14553", "year": 2018, "bug_type": "NULL pointer dereference", "source_commit_date": "2019-12-20T15:03:33Z", "target_commit_date": "2019-12-20T15:03:33Z", "source_files_changed": ["src/gd.c", "tests/gdimageclone/style.c"], "target_files_changed": [], "source_commit_url": "https://github.com/libgd/libgd/commit/a93eac0e843148dc2d631c3ba80af17e9c8c860f", "target_commit_url": "https://github.com/libgd/libgd/commit/441cbfed60ebf6cb63b8ce120ed0a82b15e7aaf8", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/libgd/libgd", "target_repo": "https://github.com/libgd/libgd", "source_commit": "a93eac0e843148dc2d631c3ba80af17e9c8c860f", "target_commit": "441cbfed60ebf6cb63b8ce120ed0a82b15e7aaf8", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2020-10531_unicode-org_icu_unicode-org_icu_1d824ede", "cve_id": "CVE-2020-10531", "patch_type": "same_repo", "projects": {"source": {"name": "unicode-org/icu", "repo": "https://github.com/unicode-org/icu", "language": "C"}, "target": {"name": "unicode-org/icu", "repo": "https://github.com/unicode-org/icu", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "b7d08bc04a4296982fcef8b6b8a354a9e4e7afca"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "1d824ede675782fcf9367e4bbcba1d998cdc33f1"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2020-10531", "year": 2020, "bug_type": "integer overflow", "source_commit_date": "2020-02-01T02:39:04Z", "target_commit_date": "2020-02-01T02:39:04Z", "source_files_changed": ["icu4c/source/common/unistr.cpp", "icu4c/source/test/intltest/ustrtest.cpp", "icu4c/source/test/intltest/ustrtest.h"], "target_files_changed": [], "source_commit_url": "https://github.com/unicode-org/icu/commit/b7d08bc04a4296982fcef8b6b8a354a9e4e7afca", "target_commit_url": "https://github.com/unicode-org/icu/commit/1d824ede675782fcf9367e4bbcba1d998cdc33f1", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/unicode-org/icu", "target_repo": "https://github.com/unicode-org/icu", "source_commit": "b7d08bc04a4296982fcef8b6b8a354a9e4e7afca", "target_commit": "1d824ede675782fcf9367e4bbcba1d998cdc33f1", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2019-20628_gpac_gpac_gpac_gpac_98b72763", "cve_id": "CVE-2019-20628", "patch_type": "same_repo", "projects": {"source": {"name": "gpac/gpac", "repo": "https://github.com/gpac/gpac", "language": "C"}, "target": {"name": "gpac/gpac", "repo": "https://github.com/gpac/gpac", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "1ab4860609f2e7a35634930571e7d0531297e090"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "98b727637e32d1d4824101d8947e2dbd573d4fc8"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2019-20628", "year": 2019, "bug_type": "use-after-free", "source_commit_date": "2019-07-07T15:42:35Z", "target_commit_date": "2019-07-07T15:54:21Z", "source_files_changed": ["src/media_tools/mpegts.c"], "target_files_changed": ["src/media_tools/mpegts.c"], "source_commit_url": "https://github.com/gpac/gpac/commit/1ab4860609f2e7a35634930571e7d0531297e090", "target_commit_url": "https://github.com/gpac/gpac/commit/98b727637e32d1d4824101d8947e2dbd573d4fc8", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/gpac/gpac", "target_repo": "https://github.com/gpac/gpac", "source_commit": "1ab4860609f2e7a35634930571e7d0531297e090", "target_commit": "98b727637e32d1d4824101d8947e2dbd573d4fc8", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2020-1712_systemd_systemd_systemd_systemd_ea0d0ede", "cve_id": "CVE-2020-1712", "patch_type": "same_repo", "projects": {"source": {"name": "systemd/systemd", "repo": "https://github.com/systemd/systemd", "language": "C"}, "target": {"name": "systemd/systemd", "repo": "https://github.com/systemd/systemd", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "1068447e6954dc6ce52f099ed174c442cb89ed54"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "ea0d0ede03c6f18dbc5036c5e9cccf97e415ccc2"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2020-1712", "year": 2020, "bug_type": "use-after-free", "source_commit_date": "2020-01-22T16:05:17Z", "target_commit_date": "2020-02-05T08:37:07Z", "source_files_changed": ["src/libsystemd/sd-bus/sd-bus.c", "src/systemd/sd-bus.h"], "target_files_changed": ["src/libsystemd/sd-bus/sd-bus.c", "src/shared/bus-polkit.c", "src/systemd/sd-bus.h"], "source_commit_url": "https://github.com/systemd/systemd/commit/1068447e6954dc6ce52f099ed174c442cb89ed54", "target_commit_url": "https://github.com/systemd/systemd/commit/ea0d0ede03c6f18dbc5036c5e9cccf97e415ccc2", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/systemd/systemd", "target_repo": "https://github.com/systemd/systemd", "source_commit": "1068447e6954dc6ce52f099ed174c442cb89ed54", "target_commit": "ea0d0ede03c6f18dbc5036c5e9cccf97e415ccc2", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2020-11945_squid-cache_squid_squid-cache_squid_eeebf0f3", "cve_id": "CVE-2020-11945", "patch_type": "same_repo", "projects": {"source": {"name": "squid-cache/squid", "repo": "https://github.com/squid-cache/squid", "language": "C"}, "target": {"name": "squid-cache/squid", "repo": "https://github.com/squid-cache/squid", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "90ce9ef7397d4cb5d5cb6d7ddd2fd113eeba7b61"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "eeebf0f37a72a2de08348e85ae34b02c34e9a811"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2020-11945", "year": 2020, "bug_type": "integer overflow", "source_commit_date": "2020-03-31T14:31:45Z", "target_commit_date": "2020-04-02T11:16:45Z", "source_files_changed": [], "target_files_changed": ["src/auth/digest/Config.cc", "src/auth/digest/Config.h"], "source_commit_url": "https://github.com/squid-cache/squid/commit/90ce9ef7397d4cb5d5cb6d7ddd2fd113eeba7b61", "target_commit_url": "https://github.com/squid-cache/squid/commit/eeebf0f37a72a2de08348e85ae34b02c34e9a811", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/squid-cache/squid", "target_repo": "https://github.com/squid-cache/squid", "source_commit": "90ce9ef7397d4cb5d5cb6d7ddd2fd113eeba7b61", "target_commit": "eeebf0f37a72a2de08348e85ae34b02c34e9a811", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2020-10060_zephyrproject-rtos_zephyr_zephyrproject-rtos_zephyr_dccbec1f", "cve_id": "CVE-2020-10060", "patch_type": "same_repo", "projects": {"source": {"name": "zephyrproject-rtos/zephyr", "repo": "https://github.com/zephyrproject-rtos/zephyr", "language": "C"}, "target": {"name": "zephyrproject-rtos/zephyr", "repo": "https://github.com/zephyrproject-rtos/zephyr", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "b19e611d40cde8f5210e088368d0dbe4903a55cb"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "dccbec1f400194985ba28ea22d2ada4305b98875"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2020-10060", "year": 2020, "bug_type": "memory overflow", "source_commit_date": "2020-08-28T02:52:53Z", "target_commit_date": "2020-08-30T18:22:05Z", "source_files_changed": [], "target_files_changed": [], "source_commit_url": "https://github.com/zephyrproject-rtos/zephyr/commit/b19e611d40cde8f5210e088368d0dbe4903a55cb", "target_commit_url": "https://github.com/zephyrproject-rtos/zephyr/commit/dccbec1f400194985ba28ea22d2ada4305b98875", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/zephyrproject-rtos/zephyr", "target_repo": "https://github.com/zephyrproject-rtos/zephyr", "source_commit": "b19e611d40cde8f5210e088368d0dbe4903a55cb", "target_commit": "dccbec1f400194985ba28ea22d2ada4305b98875", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2020-10067_zephyrproject-rtos_zephyr_zephyrproject-rtos_zephyr_edf2f0b5", "cve_id": "CVE-2020-10067", "patch_type": "same_repo", "projects": {"source": {"name": "zephyrproject-rtos/zephyr", "repo": "https://github.com/zephyrproject-rtos/zephyr", "language": "C"}, "target": {"name": "zephyrproject-rtos/zephyr", "repo": "https://github.com/zephyrproject-rtos/zephyr", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "eb77c7c7ab84255c418b434d7906166d82fdce08"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "edf2f0b5dfbe01a8f98aff2a748c4de52a470a86"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2020-10067", "year": 2020, "bug_type": "integer overflow", "source_commit_date": "2020-03-06T22:45:16Z", "target_commit_date": "2020-03-20T21:00:43Z", "source_files_changed": [], "target_files_changed": [], "source_commit_url": "https://github.com/zephyrproject-rtos/zephyr/commit/eb77c7c7ab84255c418b434d7906166d82fdce08", "target_commit_url": "https://github.com/zephyrproject-rtos/zephyr/commit/edf2f0b5dfbe01a8f98aff2a748c4de52a470a86", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/zephyrproject-rtos/zephyr", "target_repo": "https://github.com/zephyrproject-rtos/zephyr", "source_commit": "eb77c7c7ab84255c418b434d7906166d82fdce08", "target_commit": "edf2f0b5dfbe01a8f98aff2a748c4de52a470a86", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2020-13775_znc_znc_znc_znc_2390ad11", "cve_id": "CVE-2020-13775", "patch_type": "same_repo", "projects": {"source": {"name": "znc/znc", "repo": "https://github.com/znc/znc", "language": "C"}, "target": {"name": "znc/znc", "repo": "https://github.com/znc/znc", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "d229761821da38d984a9e4098ad96842490dc001"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "2390ad111bde16a78c98ac44572090b33c3bd2d8"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2020-13775", "year": 2020, "bug_type": "NULL pointer dereference", "source_commit_date": "2020-03-29T07:45:10Z", "target_commit_date": "2020-05-31T10:32:04Z", "source_files_changed": ["src/Client.cpp", "test/integration/tests/core.cpp"], "target_files_changed": ["src/Client.cpp", "test/integration/tests/core.cpp"], "source_commit_url": "https://github.com/znc/znc/commit/d229761821da38d984a9e4098ad96842490dc001", "target_commit_url": "https://github.com/znc/znc/commit/2390ad111bde16a78c98ac44572090b33c3bd2d8", "backporting_type": "cross_branch", "backporting_analysis": {"source_repo": "https://github.com/znc/znc", "target_repo": "https://github.com/znc/znc", "source_commit": "d229761821da38d984a9e4098ad96842490dc001", "target_commit": "2390ad111bde16a78c98ac44572090b33c3bd2d8", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2020-11080_nghttp2_nghttp2_nghttp2_nghttp2_f8da73bd", "cve_id": "CVE-2020-11080", "patch_type": "same_repo", "projects": {"source": {"name": "nghttp2/nghttp2", "repo": "https://github.com/nghttp2/nghttp2", "language": "C"}, "target": {"name": "nghttp2/nghttp2", "repo": "https://github.com/nghttp2/nghttp2", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "336a98feb0d56b9ac54e12736b18785c27f75090"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "f8da73bd042f810f34d19f9eae02b46d870af394"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2020-11080", "year": 2020, "bug_type": "denial of service", "source_commit_date": "2020-04-17T23:53:51Z", "target_commit_date": "2020-04-19T16:12:24Z", "source_files_changed": ["lib/includes/nghttp2/nghttp2.h", "lib/nghttp2_helper.c", "lib/nghttp2_option.c", "lib/nghttp2_option.h", "lib/nghttp2_session.c", "lib/nghttp2_session.h", "tests/main.c", "tests/nghttp2_session_test.c", "tests/nghttp2_session_test.h"], "target_files_changed": ["lib/nghttp2_session.c"], "source_commit_url": "https://github.com/nghttp2/nghttp2/commit/336a98feb0d56b9ac54e12736b18785c27f75090", "target_commit_url": "https://github.com/nghttp2/nghttp2/commit/f8da73bd042f810f34d19f9eae02b46d870af394", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/nghttp2/nghttp2", "target_repo": "https://github.com/nghttp2/nghttp2", "source_commit": "336a98feb0d56b9ac54e12736b18785c27f75090", "target_commit": "f8da73bd042f810f34d19f9eae02b46d870af394", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2020-10061_zephyrproject-rtos_zephyr_zephyrproject-rtos_zephyr_245c47a3", "cve_id": "CVE-2020-10061", "patch_type": "same_repo", "projects": {"source": {"name": "zephyrproject-rtos/zephyr", "repo": "https://github.com/zephyrproject-rtos/zephyr", "language": "C"}, "target": {"name": "zephyrproject-rtos/zephyr", "repo": "https://github.com/zephyrproject-rtos/zephyr", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "d77ee1256317807d69873d656d3f45adb272f455"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "245c47a30ca45dbf15b7e5ffae72cd64bb8c7b2c"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2020-10061", "year": 2020, "bug_type": "memory overflow", "source_commit_date": "2020-02-24T08:13:55Z", "target_commit_date": "2020-03-17T06:57:28Z", "source_files_changed": [], "target_files_changed": [], "source_commit_url": "https://github.com/zephyrproject-rtos/zephyr/commit/d77ee1256317807d69873d656d3f45adb272f455", "target_commit_url": "https://github.com/zephyrproject-rtos/zephyr/commit/245c47a30ca45dbf15b7e5ffae72cd64bb8c7b2c", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/zephyrproject-rtos/zephyr", "target_repo": "https://github.com/zephyrproject-rtos/zephyr", "source_commit": "d77ee1256317807d69873d656d3f45adb272f455", "target_commit": "245c47a30ca45dbf15b7e5ffae72cd64bb8c7b2c", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2020-10063_zephyrproject-rtos_zephyr_zephyrproject-rtos_zephyr_e50bcd71", "cve_id": "CVE-2020-10063", "patch_type": "same_repo", "projects": {"source": {"name": "zephyrproject-rtos/zephyr", "repo": "https://github.com/zephyrproject-rtos/zephyr", "language": "C"}, "target": {"name": "zephyrproject-rtos/zephyr", "repo": "https://github.com/zephyrproject-rtos/zephyr", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "c2f28d7960c415f8c20f7300ddde75878f6bd2c9"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "e50bcd71189d5ee1f589f1eb862fee49e8520a4f"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2020-10063", "year": 2020, "bug_type": "integer overflow", "source_commit_date": "2020-04-17T05:51:04Z", "target_commit_date": "2020-04-17T05:51:04Z", "source_files_changed": [], "target_files_changed": [], "source_commit_url": "https://github.com/zephyrproject-rtos/zephyr/commit/c2f28d7960c415f8c20f7300ddde75878f6bd2c9", "target_commit_url": "https://github.com/zephyrproject-rtos/zephyr/commit/e50bcd71189d5ee1f589f1eb862fee49e8520a4f", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/zephyrproject-rtos/zephyr", "target_repo": "https://github.com/zephyrproject-rtos/zephyr", "source_commit": "c2f28d7960c415f8c20f7300ddde75878f6bd2c9", "target_commit": "e50bcd71189d5ee1f589f1eb862fee49e8520a4f", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2020-12887_m<PERSON><PERSON><PERSON>_mbed-coap_ARMmbed_mbed-coap_efbec0df", "cve_id": "CVE-2020-12887", "patch_type": "cross_repo", "projects": {"source": {"name": "mju<PERSON><PERSON>/mbed-coap", "repo": "https://github.com/mjurczak/mbed-coap", "language": "C"}, "target": {"name": "ARMmbed/mbed-coap", "repo": "https://github.com/ARMmbed/mbed-coap", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "4647a68e364401e81dbd370728127d844f221d93"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "efbec0df4db5f5eafacd439c6af42ce11d286e21"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2020-12887", "year": 2020, "bug_type": "integer overflow", "source_commit_date": "2020-05-11T20:46:09Z", "target_commit_date": "2020-06-18T04:38:47Z", "source_files_changed": ["source/sn_coap_parser.c"], "target_files_changed": [], "source_commit_url": "https://github.com/mju<PERSON>zak/mbed-coap/commit/4647a68e364401e81dbd370728127d844f221d93", "target_commit_url": "https://github.com/ARMmbed/mbed-coap/commit/efbec0df4db5f5eafacd439c6af42ce11d286e21", "backporting_type": "cross_repo", "backporting_analysis": {"source_repo": "https://github.com/mjurczak/mbed-coap", "target_repo": "https://github.com/ARMmbed/mbed-coap", "source_commit": "4647a68e364401e81dbd370728127d844f221d93", "target_commit": "efbec0df4db5f5eafacd439c6af42ce11d286e21", "analysis_result": "cross_repo"}}}, {"unified_id": "CVE-2020-7689_kelektiv_node.bcrypt.js_kelektiv_node.bcrypt.js_f28e916f", "cve_id": "CVE-2020-7689", "patch_type": "same_repo", "projects": {"source": {"name": "kelektiv/node.bcrypt.js", "repo": "https://github.com/kelektiv/node.bcrypt.js", "language": "C"}, "target": {"name": "kelektiv/node.bcrypt.js", "repo": "https://github.com/kelektiv/node.bcrypt.js", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "9548df54e93f7aacfed88c5fb439a76788d92667"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "f28e916fc4de51bf7afcd9d5e48c9c6ff2659eac"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2020-7689", "year": 2020, "bug_type": "integer overflow", "source_commit_date": "2020-05-31T17:16:09Z", "target_commit_date": "2020-06-01T18:11:26Z", "source_files_changed": [], "target_files_changed": [], "source_commit_url": "https://github.com/kelektiv/node.bcrypt.js/commit/9548df54e93f7aacfed88c5fb439a76788d92667", "target_commit_url": "https://github.com/kelektiv/node.bcrypt.js/commit/f28e916fc4de51bf7afcd9d5e48c9c6ff2659eac", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/kelektiv/node.bcrypt.js", "target_repo": "https://github.com/kelektiv/node.bcrypt.js", "source_commit": "9548df54e93f7aacfed88c5fb439a76788d92667", "target_commit": "f28e916fc4de51bf7afcd9d5e48c9c6ff2659eac", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2020-15888_lua_lua_lua_lua_eb419994", "cve_id": "CVE-2020-15888", "patch_type": "same_repo", "projects": {"source": {"name": "lua/lua", "repo": "https://github.com/lua/lua", "language": "C"}, "target": {"name": "lua/lua", "repo": "https://github.com/lua/lua", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "6298903e35217ab69c279056f925fb72900ce0b7"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "eb41999461b6f428186c55abd95f4ce1a76217d5"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2020-15888", "year": 2020, "bug_type": "use-after-free", "source_commit_date": "2020-07-06T15:11:54Z", "target_commit_date": "2020-07-07T21:03:48Z", "source_files_changed": ["ldo.c"], "target_files_changed": ["ldo.c", "ldo.h", "ltm.c", "lvm.c"], "source_commit_url": "https://github.com/lua/lua/commit/6298903e35217ab69c279056f925fb72900ce0b7", "target_commit_url": "https://github.com/lua/lua/commit/eb41999461b6f428186c55abd95f4ce1a76217d5", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/lua/lua", "target_repo": "https://github.com/lua/lua", "source_commit": "6298903e35217ab69c279056f925fb72900ce0b7", "target_commit": "eb41999461b6f428186c55abd95f4ce1a76217d5", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2019-20917_inspircd_inspircd_inspircd_inspircd_8745660f", "cve_id": "CVE-2019-20917", "patch_type": "same_repo", "projects": {"source": {"name": "inspircd/inspircd", "repo": "https://github.com/inspircd/inspircd", "language": "C"}, "target": {"name": "inspircd/inspircd", "repo": "https://github.com/inspircd/inspircd", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "2cc35d8625b7ea5cbd1d1ebb116aff86c5280162"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "8745660fcdac7c1b80c94cfc0ff60928cd4dd4b7"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2019-20917", "year": 2019, "bug_type": "NULL pointer dereference", "source_commit_date": "2019-08-20T15:17:18Z", "target_commit_date": "2019-08-20T15:17:18Z", "source_files_changed": ["src/modules/extra/m_mysql.cpp"], "target_files_changed": ["src/modules/extra/m_mysql.cpp"], "source_commit_url": "https://github.com/inspircd/inspircd/commit/2cc35d8625b7ea5cbd1d1ebb116aff86c5280162", "target_commit_url": "https://github.com/inspircd/inspircd/commit/8745660fcdac7c1b80c94cfc0ff60928cd4dd4b7", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/inspircd/inspircd", "target_repo": "https://github.com/inspircd/inspircd", "source_commit": "2cc35d8625b7ea5cbd1d1ebb116aff86c5280162", "target_commit": "8745660fcdac7c1b80c94cfc0ff60928cd4dd4b7", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2019-20918_inspircd_inspircd_inspircd_inspircd_7b47de3c", "cve_id": "CVE-2019-20918", "patch_type": "same_repo", "projects": {"source": {"name": "inspircd/inspircd", "repo": "https://github.com/inspircd/inspircd", "language": "C"}, "target": {"name": "inspircd/inspircd", "repo": "https://github.com/inspircd/inspircd", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "bcd65de1ec4bb71591ae417fee649d7ecd37cd57"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "7b47de3c194f239c5fea09a0e49696c9af017d51"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2019-20918", "year": 2019, "bug_type": "use-after-free", "source_commit_date": "2019-04-18T16:57:20Z", "target_commit_date": "2019-05-15T08:37:30Z", "source_files_changed": ["src/modules/m_silence.cpp"], "target_files_changed": ["src/modules/m_silence.cpp"], "source_commit_url": "https://github.com/inspircd/inspircd/commit/bcd65de1ec4bb71591ae417fee649d7ecd37cd57", "target_commit_url": "https://github.com/inspircd/inspircd/commit/7b47de3c194f239c5fea09a0e49696c9af017d51", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/inspircd/inspircd", "target_repo": "https://github.com/inspircd/inspircd", "source_commit": "bcd65de1ec4bb71591ae417fee649d7ecd37cd57", "target_commit": "7b47de3c194f239c5fea09a0e49696c9af017d51", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2020-15166_zeromq_libzmq_zeromq_libzmq_350b4b34", "cve_id": "CVE-2020-15166", "patch_type": "same_repo", "projects": {"source": {"name": "zeromq/libzmq", "repo": "https://github.com/zeromq/libzmq", "language": "C"}, "target": {"name": "zeromq/libzmq", "repo": "https://github.com/zeromq/libzmq", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "ad3b36ab9598650887ff1ee08b8a96f6fa983500"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "350b4b34f460b91b8fa8f692cf6bc30d561a5711"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2020-15166", "year": 2020, "bug_type": "denial of service", "source_commit_date": "2020-05-13T20:02:47Z", "target_commit_date": "2020-06-28T12:36:57Z", "source_files_changed": [], "target_files_changed": [], "source_commit_url": "https://github.com/zeromq/libzmq/commit/ad3b36ab9598650887ff1ee08b8a96f6fa983500", "target_commit_url": "https://github.com/zeromq/libzmq/commit/350b4b34f460b91b8fa8f692cf6bc30d561a5711", "backporting_type": "cross_branch", "backporting_analysis": {"source_repo": "https://github.com/zeromq/libzmq", "target_repo": "https://github.com/zeromq/libzmq", "source_commit": "ad3b36ab9598650887ff1ee08b8a96f6fa983500", "target_commit": "350b4b34f460b91b8fa8f692cf6bc30d561a5711", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2020-26164_KDE_kdeconnect-kde_KDE_kdeconnect-kde_ce0f00fc", "cve_id": "CVE-2020-26164", "patch_type": "same_repo", "projects": {"source": {"name": "KDE/kdeconnect-kde", "repo": "https://github.com/KDE/kdeconnect-kde", "language": "C"}, "target": {"name": "KDE/kdeconnect-kde", "repo": "https://github.com/KDE/kdeconnect-kde", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "8112729eb0f13e6947984416118531078e65580d"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "ce0f00fc2d3eccb51d0af4eba61a4f60de086a59"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2020-26164", "year": 2020, "bug_type": "denial of service", "source_commit_date": "2020-09-16T00:27:13Z", "target_commit_date": "2020-09-24T15:18:06Z", "source_files_changed": ["core/backends/lan/socketlinereader.cpp", "tests/testsocketlinereader.cpp"], "target_files_changed": ["core/backends/lan/lanlinkprovider.cpp"], "source_commit_url": "https://github.com/KDE/kdeconnect-kde/commit/8112729eb0f13e6947984416118531078e65580d", "target_commit_url": "https://github.com/KDE/kdeconnect-kde/commit/ce0f00fc2d3eccb51d0af4eba61a4f60de086a59", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/KDE/kdeconnect-kde", "target_repo": "https://github.com/KDE/kdeconnect-kde", "source_commit": "8112729eb0f13e6947984416118531078e65580d", "target_commit": "ce0f00fc2d3eccb51d0af4eba61a4f60de086a59", "analysis_result": "same_version"}}}]