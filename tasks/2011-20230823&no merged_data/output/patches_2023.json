[{"unified_id": "CVE-2021-32843_moby_hyperkit_moby_hyperkit_af5eba23", "cve_id": "CVE-2021-32843", "patch_type": "same_repo", "projects": {"source": {"name": "moby/hyperkit", "repo": "https://github.com/moby/hyperkit", "language": "C"}, "target": {"name": "moby/hyperkit", "repo": "https://github.com/moby/hyperkit", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "df0e46c7dbfd81a957d85e449ba41b52f6f7beb4"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "af5eba2360a7351c08dfd9767d9be863a50ebaba"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-32843", "year": 2021, "bug_type": "NULL pointer dereference", "source_commit_date": "2021-06-02T09:58:27Z", "target_commit_date": "2021-06-02T10:13:56Z", "source_files_changed": ["src/lib/virtio.c"], "target_files_changed": [], "source_commit_url": "https://github.com/moby/hyperkit/commit/df0e46c7dbfd81a957d85e449ba41b52f6f7beb4", "target_commit_url": "https://github.com/moby/hyperkit/commit/af5eba2360a7351c08dfd9767d9be863a50ebaba", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/moby/hyperkit", "target_repo": "https://github.com/moby/hyperkit", "source_commit": "df0e46c7dbfd81a957d85e449ba41b52f6f7beb4", "target_commit": "af5eba2360a7351c08dfd9767d9be863a50ebaba", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2021-32844_moby_hyperkit_moby_hyperkit_451558fe", "cve_id": "CVE-2021-32844", "patch_type": "same_repo", "projects": {"source": {"name": "moby/hyperkit", "repo": "https://github.com/moby/hyperkit", "language": "C"}, "target": {"name": "moby/hyperkit", "repo": "https://github.com/moby/hyperkit", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "af5eba2360a7351c08dfd9767d9be863a50ebaba"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "451558fe8aaa8b24e02e34106e3bb9fe41d7ad13"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-32844", "year": 2021, "bug_type": "NULL pointer dereference", "source_commit_date": "2021-06-02T10:13:56Z", "target_commit_date": "2021-06-24T08:09:30Z", "source_files_changed": [], "target_files_changed": ["src/lib/virtio.c"], "source_commit_url": "https://github.com/moby/hyperkit/commit/af5eba2360a7351c08dfd9767d9be863a50ebaba", "target_commit_url": "https://github.com/moby/hyperkit/commit/451558fe8aaa8b24e02e34106e3bb9fe41d7ad13", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/moby/hyperkit", "target_repo": "https://github.com/moby/hyperkit", "source_commit": "af5eba2360a7351c08dfd9767d9be863a50ebaba", "target_commit": "451558fe8aaa8b24e02e34106e3bb9fe41d7ad13", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2021-34125_PX4_PX4-Autopilot_apache_incubator-nuttx_01687378", "cve_id": "CVE-2021-34125", "patch_type": "cross_repo", "projects": {"source": {"name": "PX4/PX4-Autopilot", "repo": "https://github.com/PX4/PX4-Autopilot", "language": "C"}, "target": {"name": "apache/incubator-nuttx", "repo": "https://github.com/apache/incubator-nuttx", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "555f900cf52c0057e4c429ff3699c91911a21cab"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "016873788280ca815ba886195535bbe601de6e48"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-34125", "year": 2021, "bug_type": "information leakage", "source_commit_date": "2021-03-29T15:17:30Z", "target_commit_date": "2021-04-03T13:03:39Z", "source_files_changed": [], "target_files_changed": [], "source_commit_url": "https://github.com/PX4/PX4-Autopilot/commit/555f900cf52c0057e4c429ff3699c91911a21cab", "target_commit_url": "https://github.com/apache/incubator-nuttx/commit/016873788280ca815ba886195535bbe601de6e48", "backporting_type": "cross_repo", "backporting_analysis": {"source_repo": "https://github.com/PX4/PX4-Autopilot", "target_repo": "https://github.com/apache/incubator-nuttx", "source_commit": "555f900cf52c0057e4c429ff3699c91911a21cab", "target_commit": "016873788280ca815ba886195535bbe601de6e48", "analysis_result": "cross_repo"}}}, {"unified_id": "CVE-2023-27590_rizinorg_rizin_rizinorg_rizin_731b55bd", "cve_id": "CVE-2023-27590", "patch_type": "same_repo", "projects": {"source": {"name": "rizinorg/rizin", "repo": "https://github.com/rizinorg/rizin", "language": "C"}, "target": {"name": "rizinorg/rizin", "repo": "https://github.com/rizinorg/rizin", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "d6196703d89c84467b600ba2692534579dc25ed4"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "731b55bd5e085f1bbc1d24fb305046e4713bc40b"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2023-27590", "year": 2023, "bug_type": "stack overflow", "source_commit_date": "2023-03-13T17:53:58Z", "target_commit_date": "2023-03-14T10:16:30Z", "source_files_changed": ["librz/debug/p/debug_gdb.c", "librz/debug/p/debug_io.c", "librz/include/rz_types_base.h", "librz/reg/profile.c"], "target_files_changed": [], "source_commit_url": "https://github.com/rizinorg/rizin/commit/d6196703d89c84467b600ba2692534579dc25ed4", "target_commit_url": "https://github.com/rizinorg/rizin/commit/731b55bd5e085f1bbc1d24fb305046e4713bc40b", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/rizinorg/rizin", "target_repo": "https://github.com/rizinorg/rizin", "source_commit": "d6196703d89c84467b600ba2692534579dc25ed4", "target_commit": "731b55bd5e085f1bbc1d24fb305046e4713bc40b", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2023-1906_ImageMagick_ImageMagick6_ImageMagick_ImageMagick_d7a8bdd7", "cve_id": "CVE-2023-1906", "patch_type": "cross_repo", "projects": {"source": {"name": "ImageMagick/ImageMagick6", "repo": "https://github.com/ImageMagick/ImageMagick6", "language": "C"}, "target": {"name": "ImageMagick/ImageMagick", "repo": "https://github.com/ImageMagick/ImageMagick", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "e30c693b37c3b41723f1469d1226a2c814ca443d"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "d7a8bdd7bb33cf8e58bc01b4a4f2ea5466f8c6b3"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2023-1906", "year": 2023, "bug_type": "heap overflow", "source_commit_date": "2023-04-01T11:32:01Z", "target_commit_date": "2023-04-01T11:33:24Z", "source_files_changed": ["coders/tiff.c"], "target_files_changed": ["coders/tiff.c"], "source_commit_url": "https://github.com/ImageMagick/ImageMagick6/commit/e30c693b37c3b41723f1469d1226a2c814ca443d", "target_commit_url": "https://github.com/ImageMagick/ImageMagick/commit/d7a8bdd7bb33cf8e58bc01b4a4f2ea5466f8c6b3", "backporting_type": "cross_repo", "backporting_analysis": {"source_repo": "https://github.com/ImageMagick/ImageMagick6", "target_repo": "https://github.com/ImageMagick/ImageMagick", "source_commit": "e30c693b37c3b41723f1469d1226a2c814ca443d", "target_commit": "d7a8bdd7bb33cf8e58bc01b4a4f2ea5466f8c6b3", "analysis_result": "cross_repo"}}}, {"unified_id": "CVE-2022-48468_protobuf-c_protobuf-c_protobuf-c_protobuf-c_ec3d9000", "cve_id": "CVE-2022-48468", "patch_type": "same_repo", "projects": {"source": {"name": "protobuf-c/protobuf-c", "repo": "https://github.com/protobuf-c/protobuf-c", "language": "C"}, "target": {"name": "protobuf-c/protobuf-c", "repo": "https://github.com/protobuf-c/protobuf-c", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "0d1fd124a4e0a07b524989f6e64410ff648fba61"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "ec3d900001a13ccdaa8aef996b34c61159c76217"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-48468", "year": 2022, "bug_type": "integer overflow", "source_commit_date": "2022-06-09T13:34:55Z", "target_commit_date": "2022-07-11T00:37:15Z", "source_files_changed": [], "target_files_changed": ["protobuf-c/protobuf-c.c"], "source_commit_url": "https://github.com/protobuf-c/protobuf-c/commit/0d1fd124a4e0a07b524989f6e64410ff648fba61", "target_commit_url": "https://github.com/protobuf-c/protobuf-c/commit/ec3d900001a13ccdaa8aef996b34c61159c76217", "backporting_type": "cross_branch", "backporting_analysis": {"source_repo": "https://github.com/protobuf-c/protobuf-c", "target_repo": "https://github.com/protobuf-c/protobuf-c", "source_commit": "0d1fd124a4e0a07b524989f6e64410ff648fba61", "target_commit": "ec3d900001a13ccdaa8aef996b34c61159c76217", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2023-24818_RIOT-OS_RIOT_RIOT-OS_RIOT_e82cab6a", "cve_id": "CVE-2023-24818", "patch_type": "same_repo", "projects": {"source": {"name": "RIOT-OS/RIOT", "repo": "https://github.com/RIOT-OS/RIOT", "language": "C"}, "target": {"name": "RIOT-OS/RIOT", "repo": "https://github.com/RIOT-OS/RIOT", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "3c7c9fefd61a57e25a08d6566ded64ca8c5326ae"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "e82cab6a94fa7865162bb7ac3705d5a461b1207d"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2023-24818", "year": 2023, "bug_type": "NULL pointer dereference", "source_commit_date": "2022-10-28T14:42:26Z", "target_commit_date": "2022-10-28T14:42:26Z", "source_files_changed": [], "target_files_changed": [], "source_commit_url": "https://github.com/RIOT-OS/RIOT/commit/3c7c9fefd61a57e25a08d6566ded64ca8c5326ae", "target_commit_url": "https://github.com/RIOT-OS/RIOT/commit/e82cab6a94fa7865162bb7ac3705d5a461b1207d", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/RIOT-OS/RIOT", "target_repo": "https://github.com/RIOT-OS/RIOT", "source_commit": "3c7c9fefd61a57e25a08d6566ded64ca8c5326ae", "target_commit": "e82cab6a94fa7865162bb7ac3705d5a461b1207d", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2023-24822_RIOT-OS_RIOT_RIOT-OS_RIOT_e82cab6a", "cve_id": "CVE-2023-24822", "patch_type": "same_repo", "projects": {"source": {"name": "RIOT-OS/RIOT", "repo": "https://github.com/RIOT-OS/RIOT", "language": "C"}, "target": {"name": "RIOT-OS/RIOT", "repo": "https://github.com/RIOT-OS/RIOT", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "3c7c9fefd61a57e25a08d6566ded64ca8c5326ae"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "e82cab6a94fa7865162bb7ac3705d5a461b1207d"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2023-24822", "year": 2023, "bug_type": "NULL pointer dereference", "source_commit_date": "2022-10-28T14:42:26Z", "target_commit_date": "2022-10-28T14:42:26Z", "source_files_changed": [], "target_files_changed": [], "source_commit_url": "https://github.com/RIOT-OS/RIOT/commit/3c7c9fefd61a57e25a08d6566ded64ca8c5326ae", "target_commit_url": "https://github.com/RIOT-OS/RIOT/commit/e82cab6a94fa7865162bb7ac3705d5a461b1207d", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/RIOT-OS/RIOT", "target_repo": "https://github.com/RIOT-OS/RIOT", "source_commit": "3c7c9fefd61a57e25a08d6566ded64ca8c5326ae", "target_commit": "e82cab6a94fa7865162bb7ac3705d5a461b1207d", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2023-30549_torvalds_linux_torvalds_linux_2220eaf9", "cve_id": "CVE-2023-30549", "patch_type": "same_repo", "projects": {"source": {"name": "torvalds/linux", "repo": "https://github.com/torvalds/linux", "language": "C"}, "target": {"name": "torvalds/linux", "repo": "https://github.com/torvalds/linux", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "4f04351888a83e595571de672e0a4a8b74f"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "2220eaf90992c11d888fe771055d4de3303"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2023-30549", "year": 2023, "bug_type": "use-after-free", "source_commit_date": "2023-05-04T12:15:25Z", "target_commit_date": "2023-05-12T19:11:02Z", "source_files_changed": ["fs/ext4/super.c"], "target_files_changed": ["fs/ext4/inline.c"], "source_commit_url": "https://github.com/torvalds/linux/commit/4f04351888a83e595571de672e0a4a8b74f", "target_commit_url": "https://github.com/torvalds/linux/commit/2220eaf90992c11d888fe771055d4de3303", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/torvalds/linux", "target_repo": "https://github.com/torvalds/linux", "source_commit": "4f04351888a83e595571de672e0a4a8b74f", "target_commit": "2220eaf90992c11d888fe771055d4de3303", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2023-3195_ImageMagick_ImageMagick6_ImageMagick_ImageMagick_f6203409", "cve_id": "CVE-2023-3195", "patch_type": "cross_repo", "projects": {"source": {"name": "ImageMagick/ImageMagick6", "repo": "https://github.com/ImageMagick/ImageMagick6", "language": "C"}, "target": {"name": "ImageMagick/ImageMagick", "repo": "https://github.com/ImageMagick/ImageMagick", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "85a370c79afeb45a97842b0959366af5236e9023"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "f620340935777b28fa3f7b0ed7ed6bd86946934c"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2023-3195", "year": 2023, "bug_type": "stack overflow", "source_commit_date": "2021-10-19T18:53:41Z", "target_commit_date": "2021-10-19T18:53:54Z", "source_files_changed": ["coders/tiff.c"], "target_files_changed": ["coders/tiff.c"], "source_commit_url": "https://github.com/ImageMagick/ImageMagick6/commit/85a370c79afeb45a97842b0959366af5236e9023", "target_commit_url": "https://github.com/ImageMagick/ImageMagick/commit/f620340935777b28fa3f7b0ed7ed6bd86946934c", "backporting_type": "cross_repo", "backporting_analysis": {"source_repo": "https://github.com/ImageMagick/ImageMagick6", "target_repo": "https://github.com/ImageMagick/ImageMagick", "source_commit": "85a370c79afeb45a97842b0959366af5236e9023", "target_commit": "f620340935777b28fa3f7b0ed7ed6bd86946934c", "analysis_result": "cross_repo"}}}]