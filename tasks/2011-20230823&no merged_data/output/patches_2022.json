[{"unified_id": "CVE-2022-23567_tensorflow_tensorflow_tensorflow_tensorflow_e952a89b", "cve_id": "CVE-2022-23567", "patch_type": "same_repo", "projects": {"source": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}, "target": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "1b54cadd19391b60b6fcccd8d076426f7221d5e8"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "e952a89b7026b98fe8cbe626514a93ed68b7c510"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-23567", "year": 2022, "bug_type": "integer overflow", "source_commit_date": "2021-12-10T17:46:39Z", "target_commit_date": "2021-12-10T17:46:48Z", "source_files_changed": ["tensorflow/core/kernels/sparse_dense_binary_op_shared.cc"], "target_files_changed": ["tensorflow/core/kernels/sparse_dense_binary_op_shared.cc"], "source_commit_url": "https://github.com/tensorflow/tensorflow/commit/1b54cadd19391b60b6fcccd8d076426f7221d5e8", "target_commit_url": "https://github.com/tensorflow/tensorflow/commit/e952a89b7026b98fe8cbe626514a93ed68b7c510", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "source_commit": "1b54cadd19391b60b6fcccd8d076426f7221d5e8", "target_commit": "e952a89b7026b98fe8cbe626514a93ed68b7c510", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2022-23568_tensorflow_tensorflow_tensorflow_tensorflow_a68f6806", "cve_id": "CVE-2022-23568", "patch_type": "same_repo", "projects": {"source": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}, "target": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "b51b82fe65ebace4475e3c54eb089c18a4403f1c"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "a68f68061e263a88321c104a6c911fe5598050a8"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-23568", "year": 2022, "bug_type": "integer overflow", "source_commit_date": "2021-12-09T22:32:48Z", "target_commit_date": "2021-12-10T00:17:26Z", "source_files_changed": ["tensorflow/core/kernels/sparse_tensors_map_ops.cc"], "target_files_changed": ["tensorflow/core/kernels/sparse_tensors_map_ops.cc"], "source_commit_url": "https://github.com/tensorflow/tensorflow/commit/b51b82fe65ebace4475e3c54eb089c18a4403f1c", "target_commit_url": "https://github.com/tensorflow/tensorflow/commit/a68f68061e263a88321c104a6c911fe5598050a8", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "source_commit": "b51b82fe65ebace4475e3c54eb089c18a4403f1c", "target_commit": "a68f68061e263a88321c104a6c911fe5598050a8", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2022-23559_tensorflow_tensorflow_tensorflow_tensorflow_a4e401da", "cve_id": "CVE-2022-23559", "patch_type": "same_repo", "projects": {"source": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}, "target": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "f19be71717c497723ba0cea0379e84f061a75e01"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "a4e401da71458d253b05e41f28637b65baf64be4"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-23559", "year": 2022, "bug_type": "integer overflow", "source_commit_date": "2021-12-16T22:32:07Z", "target_commit_date": "2021-12-21T21:06:51Z", "source_files_changed": ["tensorflow/lite/core/subgraph.cc", "tensorflow/lite/util.cc", "tensorflow/lite/util.h", "tensorflow/lite/util_test.cc"], "target_files_changed": ["tensorflow/lite/kernels/embedding_lookup_sparse.cc"], "source_commit_url": "https://github.com/tensorflow/tensorflow/commit/f19be71717c497723ba0cea0379e84f061a75e01", "target_commit_url": "https://github.com/tensorflow/tensorflow/commit/a4e401da71458d253b05e41f28637b65baf64be4", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "source_commit": "f19be71717c497723ba0cea0379e84f061a75e01", "target_commit": "a4e401da71458d253b05e41f28637b65baf64be4", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2022-23562_tensorflow_tensorflow_tensorflow_tensorflow_f0147751", "cve_id": "CVE-2022-23562", "patch_type": "same_repo", "projects": {"source": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}, "target": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "ab5a569b93ae1f7a3f3d1147fb347a2e7f65b5c3"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "f0147751fd5d2ff23251149ebad9af9f03010732"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-23562", "year": 2022, "bug_type": "integer overflow", "source_commit_date": "2021-08-28T17:15:23Z", "target_commit_date": "2021-12-17T03:26:39Z", "source_files_changed": [], "target_files_changed": ["tensorflow/core/kernels/sequence_ops.cc", "tensorflow/core/ops/math_ops.cc"], "source_commit_url": "https://github.com/tensorflow/tensorflow/commit/ab5a569b93ae1f7a3f3d1147fb347a2e7f65b5c3", "target_commit_url": "https://github.com/tensorflow/tensorflow/commit/f0147751fd5d2ff23251149ebad9af9f03010732", "backporting_type": "cross_branch", "backporting_analysis": {"source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "source_commit": "ab5a569b93ae1f7a3f3d1147fb347a2e7f65b5c3", "target_commit": "f0147751fd5d2ff23251149ebad9af9f03010732", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-23589_tensorflow_tensorflow_tensorflow_tensorflow_045deec1", "cve_id": "CVE-2022-23589", "patch_type": "same_repo", "projects": {"source": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}, "target": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "0a365c029e437be0349c31f8d4c9926b69fa3fa1"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "045deec1cbdebb27d817008ad5df94d96a08b1bf"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-23589", "year": 2022, "bug_type": "NULL pointer dereference", "source_commit_date": "2021-11-13T18:05:59Z", "target_commit_date": "2021-11-13T18:12:22Z", "source_files_changed": ["tensorflow/core/grappler/optimizers/constant_folding.cc"], "target_files_changed": ["tensorflow/core/grappler/mutable_graph_view.cc"], "source_commit_url": "https://github.com/tensorflow/tensorflow/commit/0a365c029e437be0349c31f8d4c9926b69fa3fa1", "target_commit_url": "https://github.com/tensorflow/tensorflow/commit/045deec1cbdebb27d817008ad5df94d96a08b1bf", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "source_commit": "0a365c029e437be0349c31f8d4c9926b69fa3fa1", "target_commit": "045deec1cbdebb27d817008ad5df94d96a08b1bf", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2021-46389_ruven_iipsrv_ruven_iipsrv_882925b2", "cve_id": "CVE-2021-46389", "patch_type": "same_repo", "projects": {"source": {"name": "ruven/iipsrv", "repo": "https://github.com/ruven/iipsrv", "language": "C"}, "target": {"name": "ruven/iipsrv", "repo": "https://github.com/ruven/iipsrv", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "4ed59265fbbd636dc2fbbf325f8ea37ed300a6d9"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "882925b295a80ec992063deffc2a3b0d803c3195"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-46389", "year": 2021, "bug_type": "integer overflow", "source_commit_date": "2022-01-14T22:22:09Z", "target_commit_date": "2022-01-16T13:34:07Z", "source_files_changed": ["src/JTL.cc", "src/SPECTRA.cc"], "target_files_changed": ["src/KakaduImage.cc", "src/OpenJPEGImage.cc", "src/RawTile.h", "src/TileManager.cc", "src/Transforms.cc"], "source_commit_url": "https://github.com/ruven/iipsrv/commit/4ed59265fbbd636dc2fbbf325f8ea37ed300a6d9", "target_commit_url": "https://github.com/ruven/iipsrv/commit/882925b295a80ec992063deffc2a3b0d803c3195", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/ruven/iipsrv", "target_repo": "https://github.com/ruven/iipsrv", "source_commit": "4ed59265fbbd636dc2fbbf325f8ea37ed300a6d9", "target_commit": "882925b295a80ec992063deffc2a3b0d803c3195", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2021-4076_latchset_tang_latchset_tang_e82459fd", "cve_id": "CVE-2021-4076", "patch_type": "same_repo", "projects": {"source": {"name": "latchset/tang", "repo": "https://github.com/latchset/tang", "language": "C"}, "target": {"name": "latchset/tang", "repo": "https://github.com/latchset/tang", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "3503270a0b85e16ccd4a6f078a65d62d192df127"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "e82459fda10f0630c3414ed2afbc6320bb9ea7c9"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-4076", "year": 2021, "bug_type": "information leakage", "source_commit_date": "2021-12-07T10:26:27Z", "target_commit_date": "2021-12-14T11:18:41Z", "source_files_changed": [], "target_files_changed": ["src/keys.c"], "source_commit_url": "https://github.com/latchset/tang/commit/3503270a0b85e16ccd4a6f078a65d62d192df127", "target_commit_url": "https://github.com/latchset/tang/commit/e82459fda10f0630c3414ed2afbc6320bb9ea7c9", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/latchset/tang", "target_repo": "https://github.com/latchset/tang", "source_commit": "3503270a0b85e16ccd4a6f078a65d62d192df127", "target_commit": "e82459fda10f0630c3414ed2afbc6320bb9ea7c9", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2021-42781_OpenSC_OpenSC_OpenSC_OpenSC_cae5c71f", "cve_id": "CVE-2021-42781", "patch_type": "same_repo", "projects": {"source": {"name": "OpenSC/OpenSC", "repo": "https://github.com/OpenSC/OpenSC", "language": "C"}, "target": {"name": "OpenSC/OpenSC", "repo": "https://github.com/OpenSC/OpenSC", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "17d8980c"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "cae5c71f"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-42781", "year": 2021, "bug_type": "memory overflow", "source_commit_date": "2021-02-03T20:46:15Z", "target_commit_date": "2021-04-06T10:45:24Z", "source_files_changed": ["src/libopensc/pkcs15-oberthur.c"], "target_files_changed": ["src/libopensc/pkcs15-oberthur.c"], "source_commit_url": "https://github.com/OpenSC/OpenSC/commit/17d8980c", "target_commit_url": "https://github.com/OpenSC/OpenSC/commit/cae5c71f", "backporting_type": "cross_branch", "backporting_analysis": {"source_repo": "https://github.com/OpenSC/OpenSC", "target_repo": "https://github.com/OpenSC/OpenSC", "source_commit": "17d8980c", "target_commit": "cae5c71f", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2021-42782_OpenSC_OpenSC_OpenSC_OpenSC_456ac566", "cve_id": "CVE-2021-42782", "patch_type": "same_repo", "projects": {"source": {"name": "OpenSC/OpenSC", "repo": "https://github.com/OpenSC/OpenSC", "language": "C"}, "target": {"name": "OpenSC/OpenSC", "repo": "https://github.com/OpenSC/OpenSC", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "78cdab94"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "456ac566"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-42782", "year": 2021, "bug_type": "memory overflow", "source_commit_date": "2020-11-30T16:43:03Z", "target_commit_date": "2021-07-14T16:15:10Z", "source_files_changed": ["src/libopensc/pkcs15-tcos.c"], "target_files_changed": ["src/libopensc/card-piv.c"], "source_commit_url": "https://github.com/OpenSC/OpenSC/commit/78cdab94", "target_commit_url": "https://github.com/OpenSC/OpenSC/commit/456ac566", "backporting_type": "cross_branch", "backporting_analysis": {"source_repo": "https://github.com/OpenSC/OpenSC", "target_repo": "https://github.com/OpenSC/OpenSC", "source_commit": "78cdab94", "target_commit": "456ac566", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-29694_unicorn-engine_unicorn_unicorn-engine_unicorn_3d3deac5", "cve_id": "CVE-2022-29694", "patch_type": "same_repo", "projects": {"source": {"name": "unicorn-engine/unicorn", "repo": "https://github.com/unicorn-engine/unicorn", "language": "C"}, "target": {"name": "unicorn-engine/unicorn", "repo": "https://github.com/unicorn-engine/unicorn", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "31389e59457f304be3809f9679f91a42daa7ebaa"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "3d3deac5e6d38602b689c4fef5dac004f07a2e63"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-29694", "year": 2022, "bug_type": "NULL pointer dereference", "source_commit_date": "2022-04-14T10:06:58Z", "target_commit_date": "2022-04-16T17:17:41Z", "source_files_changed": [], "target_files_changed": ["qemu/exec.c", "qemu/include/qemu/atomic.h", "qemu/include/qemu/rcu_queue.h", "qemu/softmmu/memory.c", "tests/unit/test_mem.c"], "source_commit_url": "https://github.com/unicorn-engine/unicorn/commit/31389e59457f304be3809f9679f91a42daa7ebaa", "target_commit_url": "https://github.com/unicorn-engine/unicorn/commit/3d3deac5e6d38602b689c4fef5dac004f07a2e63", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/unicorn-engine/unicorn", "target_repo": "https://github.com/unicorn-engine/unicorn", "source_commit": "31389e59457f304be3809f9679f91a42daa7ebaa", "target_commit": "3d3deac5e6d38602b689c4fef5dac004f07a2e63", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2022-32545_ImageMagick_ImageMagick_ImageMagick_ImageMagick6_450949ed", "cve_id": "CVE-2022-32545", "patch_type": "cross_repo", "projects": {"source": {"name": "ImageMagick/ImageMagick", "repo": "https://github.com/ImageMagick/ImageMagick", "language": "C"}, "target": {"name": "ImageMagick/ImageMagick6", "repo": "https://github.com/ImageMagick/ImageMagick6", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "9c9a84cec4ab28ee0b57c2b9266d6fbe68183512"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "450949ed017f009b399c937cf362f0058eacc5fa"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-32545", "year": 2022, "bug_type": "integer overflow", "source_commit_date": "2022-03-19T10:50:38Z", "target_commit_date": "2022-03-19T11:01:57Z", "source_files_changed": ["coders/psd.c"], "target_files_changed": ["coders/emf.c", "coders/psd.c", "magick/widget.c", "wand/animate.c", "wand/display.c"], "source_commit_url": "https://github.com/ImageMagick/ImageMagick/commit/9c9a84cec4ab28ee0b57c2b9266d6fbe68183512", "target_commit_url": "https://github.com/ImageMagick/ImageMagick6/commit/450949ed017f009b399c937cf362f0058eacc5fa", "backporting_type": "cross_repo", "backporting_analysis": {"source_repo": "https://github.com/ImageMagick/ImageMagick", "target_repo": "https://github.com/ImageMagick/ImageMagick6", "source_commit": "9c9a84cec4ab28ee0b57c2b9266d6fbe68183512", "target_commit": "450949ed017f009b399c937cf362f0058eacc5fa", "analysis_result": "cross_repo"}}}, {"unified_id": "CVE-2022-32546_ImageMagick_ImageMagick_ImageMagick_ImageMagick6_29c8abce", "cve_id": "CVE-2022-32546", "patch_type": "cross_repo", "projects": {"source": {"name": "ImageMagick/ImageMagick", "repo": "https://github.com/ImageMagick/ImageMagick", "language": "C"}, "target": {"name": "ImageMagick/ImageMagick6", "repo": "https://github.com/ImageMagick/ImageMagick6", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "f221ea0fa3171f0f4fdf74ac9d81b203b9534c23"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "29c8abce0da56b536542f76a9ddfebdaab5b2943"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-32546", "year": 2022, "bug_type": "integer overflow", "source_commit_date": "2022-03-24T15:34:47Z", "target_commit_date": "2022-03-24T15:38:59Z", "source_files_changed": ["coders/pcl.c"], "target_files_changed": ["coders/pcl.c"], "source_commit_url": "https://github.com/ImageMagick/ImageMagick/commit/f221ea0fa3171f0f4fdf74ac9d81b203b9534c23", "target_commit_url": "https://github.com/ImageMagick/ImageMagick6/commit/29c8abce0da56b536542f76a9ddfebdaab5b2943", "backporting_type": "cross_repo", "backporting_analysis": {"source_repo": "https://github.com/ImageMagick/ImageMagick", "target_repo": "https://github.com/ImageMagick/ImageMagick6", "source_commit": "f221ea0fa3171f0f4fdf74ac9d81b203b9534c23", "target_commit": "29c8abce0da56b536542f76a9ddfebdaab5b2943", "analysis_result": "cross_repo"}}}, {"unified_id": "CVE-2022-2522_vim_vim_vim_vim_5fa9f23a", "cve_id": "CVE-2022-2522", "patch_type": "same_repo", "projects": {"source": {"name": "vim/vim", "repo": "https://github.com/vim/vim", "language": "C"}, "target": {"name": "vim/vim", "repo": "https://github.com/vim/vim", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "b9e717367c395490149495cf375911b5d9de889e"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "5fa9f23a63651a8abdb074b4fc2ec9b1adc6b089"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-2522", "year": 2022, "bug_type": "heap overflow", "source_commit_date": "2022-07-23T05:53:08Z", "target_commit_date": "2022-07-23T08:06:48Z", "source_files_changed": ["src/insexpand.c", "src/version.c"], "target_files_changed": ["src/autocmd.c", "src/version.c", "src/window.c"], "source_commit_url": "https://github.com/vim/vim/commit/b9e717367c395490149495cf375911b5d9de889e", "target_commit_url": "https://github.com/vim/vim/commit/5fa9f23a63651a8abdb074b4fc2ec9b1adc6b089", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/vim/vim", "target_repo": "https://github.com/vim/vim", "source_commit": "b9e717367c395490149495cf375911b5d9de889e", "target_commit": "5fa9f23a63651a8abdb074b4fc2ec9b1adc6b089", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2021-20298_AcademySoftwareFoundation_openexr_AcademySoftwareFoundation_openexr_85fd638a", "cve_id": "CVE-2021-20298", "patch_type": "same_repo", "projects": {"source": {"name": "AcademySoftwareFoundation/openexr", "repo": "https://github.com/AcademySoftwareFoundation/openexr", "language": "C"}, "target": {"name": "AcademySoftwareFoundation/openexr", "repo": "https://github.com/AcademySoftwareFoundation/openexr", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "bb90c30a5acebb2f65a097e3be57ef15d9db4e81"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "85fd638ae0d5fa132434f4cbf32590261c1dba97"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-20298", "year": 2021, "bug_type": "denial of service", "source_commit_date": "2020-10-06T23:17:59Z", "target_commit_date": "2020-10-07T01:38:59Z", "source_files_changed": [], "target_files_changed": ["OpenEXR/IlmImf/ImfB44Compressor.cpp"], "source_commit_url": "https://github.com/AcademySoftwareFoundation/openexr/commit/bb90c30a5acebb2f65a097e3be57ef15d9db4e81", "target_commit_url": "https://github.com/AcademySoftwareFoundation/openexr/commit/85fd638ae0d5fa132434f4cbf32590261c1dba97", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/AcademySoftwareFoundation/openexr", "target_repo": "https://github.com/AcademySoftwareFoundation/openexr", "source_commit": "bb90c30a5acebb2f65a097e3be57ef15d9db4e81", "target_commit": "85fd638ae0d5fa132434f4cbf32590261c1dba97", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2021-20304_AcademySoftwareFoundation_openexr_AcademySoftwareFoundation_openexr_51a92d67", "cve_id": "CVE-2021-20304", "patch_type": "same_repo", "projects": {"source": {"name": "AcademySoftwareFoundation/openexr", "repo": "https://github.com/AcademySoftwareFoundation/openexr", "language": "C"}, "target": {"name": "AcademySoftwareFoundation/openexr", "repo": "https://github.com/AcademySoftwareFoundation/openexr", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "04bac6d7aff0070cb0ff74153068f54fe77e3258"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "51a92d67f53c08230734e74564c807043cbfe41e"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-20304", "year": 2021, "bug_type": "integer overflow", "source_commit_date": "2020-10-10T03:33:37Z", "target_commit_date": "2020-10-12T19:03:57Z", "source_files_changed": [], "target_files_changed": ["OpenEXR/IlmImf/ImfHuf.cpp", "OpenEXR/IlmImfTest/testHuf.cpp"], "source_commit_url": "https://github.com/AcademySoftwareFoundation/openexr/commit/04bac6d7aff0070cb0ff74153068f54fe77e3258", "target_commit_url": "https://github.com/AcademySoftwareFoundation/openexr/commit/51a92d67f53c08230734e74564c807043cbfe41e", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/AcademySoftwareFoundation/openexr", "target_repo": "https://github.com/AcademySoftwareFoundation/openexr", "source_commit": "04bac6d7aff0070cb0ff74153068f54fe77e3258", "target_commit": "51a92d67f53c08230734e74564c807043cbfe41e", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2021-3798_opencryptoki_opencryptoki_opencryptoki_opencryptoki_814e4d0a", "cve_id": "CVE-2021-3798", "patch_type": "same_repo", "projects": {"source": {"name": "opencryptoki/opencryptoki", "repo": "https://github.com/opencryptoki/opencryptoki", "language": "C"}, "target": {"name": "opencryptoki/opencryptoki", "repo": "https://github.com/opencryptoki/opencryptoki", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "4e3b43c3d8844402c04a66b55c6c940f965109f0"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "814e4d0ab9a72b536e8f6694af4e26280e0e96bd"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-3798", "year": 2021, "bug_type": "information leakage", "source_commit_date": "2021-05-03T08:05:07Z", "target_commit_date": "2021-05-03T08:05:07Z", "source_files_changed": ["usr/lib/soft_stdll/soft_specific.c"], "target_files_changed": [], "source_commit_url": "https://github.com/opencryptoki/opencryptoki/commit/4e3b43c3d8844402c04a66b55c6c940f965109f0", "target_commit_url": "https://github.com/opencryptoki/opencryptoki/commit/814e4d0ab9a72b536e8f6694af4e26280e0e96bd", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/opencryptoki/opencryptoki", "target_repo": "https://github.com/opencryptoki/opencryptoki", "source_commit": "4e3b43c3d8844402c04a66b55c6c940f965109f0", "target_commit": "814e4d0ab9a72b536e8f6694af4e26280e0e96bd", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2021-20224_ImageMagick_ImageMagick_ImageMagick_ImageMagick6_553054c1", "cve_id": "CVE-2021-20224", "patch_type": "cross_repo", "projects": {"source": {"name": "ImageMagick/ImageMagick", "repo": "https://github.com/ImageMagick/ImageMagick", "language": "C"}, "target": {"name": "ImageMagick/ImageMagick6", "repo": "https://github.com/ImageMagick/ImageMagick6", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "9583e97bfe0a80ebf321e05399a9e6490747f397"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "553054c1cb1e4e05ec86237afef76a32cd7c464d"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-20224", "year": 2021, "bug_type": "integer overflow", "source_commit_date": "2021-01-06T15:39:26Z", "target_commit_date": "2021-01-06T23:34:31Z", "source_files_changed": [], "target_files_changed": ["magick/quantum-export.c"], "source_commit_url": "https://github.com/ImageMagick/ImageMagick/commit/9583e97bfe0a80ebf321e05399a9e6490747f397", "target_commit_url": "https://github.com/ImageMagick/ImageMagick6/commit/553054c1cb1e4e05ec86237afef76a32cd7c464d", "backporting_type": "cross_repo", "backporting_analysis": {"source_repo": "https://github.com/ImageMagick/ImageMagick", "target_repo": "https://github.com/ImageMagick/ImageMagick6", "source_commit": "9583e97bfe0a80ebf321e05399a9e6490747f397", "target_commit": "553054c1cb1e4e05ec86237afef76a32cd7c464d", "analysis_result": "cross_repo"}}}, {"unified_id": "CVE-2022-0496_openscad_openscad_openscad_openscad_00a46929", "cve_id": "CVE-2022-0496", "patch_type": "same_repo", "projects": {"source": {"name": "openscad/openscad", "repo": "https://github.com/openscad/openscad", "language": "C"}, "target": {"name": "openscad/openscad", "repo": "https://github.com/openscad/openscad", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "770e3234cbfe66edbc0333f796b46d36a74aa652"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "00a4692989c4e2f191525f73f24ad8727bacdf41"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-0496", "year": 2022, "bug_type": "memory overflow", "source_commit_date": "2022-01-16T03:40:09Z", "target_commit_date": "2022-02-05T17:38:31Z", "source_files_changed": ["src/dxfdata.cc"], "target_files_changed": ["src/dxfdata.cc"], "source_commit_url": "https://github.com/openscad/openscad/commit/770e3234cbfe66edbc0333f796b46d36a74aa652", "target_commit_url": "https://github.com/openscad/openscad/commit/00a4692989c4e2f191525f73f24ad8727bacdf41", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/openscad/openscad", "target_repo": "https://github.com/openscad/openscad", "source_commit": "770e3234cbfe66edbc0333f796b46d36a74aa652", "target_commit": "00a4692989c4e2f191525f73f24ad8727bacdf41", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2022-1115_ImageMagick_ImageMagick6_ImageMagick_ImageMagick_c8718305", "cve_id": "CVE-2022-1115", "patch_type": "cross_repo", "projects": {"source": {"name": "ImageMagick/ImageMagick6", "repo": "https://github.com/ImageMagick/ImageMagick6", "language": "C"}, "target": {"name": "ImageMagick/ImageMagick", "repo": "https://github.com/ImageMagick/ImageMagick", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "1f860f52bd8d58737ad883072203391096b30b51"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "c8718305f120293d8bf13724f12eed885d830b09"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-1115", "year": 2022, "bug_type": "memory overflow", "source_commit_date": "2022-03-23T00:11:27Z", "target_commit_date": "2022-03-23T00:11:47Z", "source_files_changed": ["coders/tiff.c"], "target_files_changed": ["coders/tiff.c"], "source_commit_url": "https://github.com/ImageMagick/ImageMagick6/commit/1f860f52bd8d58737ad883072203391096b30b51", "target_commit_url": "https://github.com/ImageMagick/ImageMagick/commit/c8718305f120293d8bf13724f12eed885d830b09", "backporting_type": "cross_repo", "backporting_analysis": {"source_repo": "https://github.com/ImageMagick/ImageMagick6", "target_repo": "https://github.com/ImageMagick/ImageMagick", "source_commit": "1f860f52bd8d58737ad883072203391096b30b51", "target_commit": "c8718305f120293d8bf13724f12eed885d830b09", "analysis_result": "cross_repo"}}}, {"unified_id": "CVE-2022-1325_GreycLab_CImg_GreycLab_CImg_37cf0c1e", "cve_id": "CVE-2022-1325", "patch_type": "same_repo", "projects": {"source": {"name": "GreycLab/CImg", "repo": "https://github.com/GreycLab/CImg", "language": "C"}, "target": {"name": "GreycLab/CImg", "repo": "https://github.com/GreycLab/CImg", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "619cb58dd90b4e03ac68286c70ed98acbefd1c90"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "37cf0c1e5eeafb5b759c1a36423eb3dae27dbee8"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-1325", "year": 2022, "bug_type": "denial of service", "source_commit_date": "2022-04-07T10:43:39Z", "target_commit_date": "2022-04-25T17:32:11Z", "source_files_changed": ["CImg.h"], "target_files_changed": [], "source_commit_url": "https://github.com/GreycLab/CImg/commit/619cb58dd90b4e03ac68286c70ed98acbefd1c90", "target_commit_url": "https://github.com/GreycLab/CImg/commit/37cf0c1e5eeafb5b759c1a36423eb3dae27dbee8", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/GreycLab/CImg", "target_repo": "https://github.com/GreycLab/CImg", "source_commit": "619cb58dd90b4e03ac68286c70ed98acbefd1c90", "target_commit": "37cf0c1e5eeafb5b759c1a36423eb3dae27dbee8", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2022-40674_libexpat_libexpat_libexpat_libexpat_721169ee", "cve_id": "CVE-2022-40674", "patch_type": "same_repo", "projects": {"source": {"name": "libexpat/libexpat", "repo": "https://github.com/libexpat/libexpat", "language": "C"}, "target": {"name": "libexpat/libexpat", "repo": "https://github.com/libexpat/libexpat", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "4a32da87e931ba54393d465bb77c40b5c33d343b"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "721169eeca509ba93be6c73d0abe1caf5d218b9c"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-40674", "year": 2022, "bug_type": "use-after-free", "source_commit_date": "2022-08-17T17:26:18Z", "target_commit_date": "2022-09-11T18:38:09Z", "source_files_changed": [], "target_files_changed": [], "source_commit_url": "https://github.com/libexpat/libexpat/commit/4a32da87e931ba54393d465bb77c40b5c33d343b", "target_commit_url": "https://github.com/libexpat/libexpat/commit/721169eeca509ba93be6c73d0abe1caf5d218b9c", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/libexpat/libexpat", "target_repo": "https://github.com/libexpat/libexpat", "source_commit": "4a32da87e931ba54393d465bb77c40b5c33d343b", "target_commit": "721169eeca509ba93be6c73d0abe1caf5d218b9c", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2022-36014_tensorflow_tensorflow_tensorflow_tensorflow_a0f0b9a2", "cve_id": "CVE-2022-36014", "patch_type": "same_repo", "projects": {"source": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}, "target": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "3a754740d5414e362512ee981eefba41561a63a6"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "a0f0b9a21c9270930457095092f558fbad4c03e5"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-36014", "year": 2022, "bug_type": "NULL pointer dereference", "source_commit_date": "2022-05-20T03:00:39Z", "target_commit_date": "2022-06-08T17:27:21Z", "source_files_changed": ["tensorflow/core/ir/importexport/graphdef_import.cc"], "target_files_changed": ["tensorflow/core/ir/importexport/graphdef_import.cc"], "source_commit_url": "https://github.com/tensorflow/tensorflow/commit/3a754740d5414e362512ee981eefba41561a63a6", "target_commit_url": "https://github.com/tensorflow/tensorflow/commit/a0f0b9a21c9270930457095092f558fbad4c03e5", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "source_commit": "3a754740d5414e362512ee981eefba41561a63a6", "target_commit": "a0f0b9a21c9270930457095092f558fbad4c03e5", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2022-3213_ImageMagick_ImageMagick6_ImageMagick_ImageMagick_30ccf9a0", "cve_id": "CVE-2022-3213", "patch_type": "cross_repo", "projects": {"source": {"name": "ImageMagick/ImageMagick6", "repo": "https://github.com/ImageMagick/ImageMagick6", "language": "C"}, "target": {"name": "ImageMagick/ImageMagick", "repo": "https://github.com/ImageMagick/ImageMagick", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "1aea203eb36409ce6903b9e41fe7cb70030e8750"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "30ccf9a0da1f47161b5935a95be854fe84e6c2a2"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-3213", "year": 2022, "bug_type": "memory overflow", "source_commit_date": "2022-08-27T12:38:18Z", "target_commit_date": "2022-08-27T12:38:57Z", "source_files_changed": ["coders/tiff.c"], "target_files_changed": ["coders/tiff.c"], "source_commit_url": "https://github.com/ImageMagick/ImageMagick6/commit/1aea203eb36409ce6903b9e41fe7cb70030e8750", "target_commit_url": "https://github.com/ImageMagick/ImageMagick/commit/30ccf9a0da1f47161b5935a95be854fe84e6c2a2", "backporting_type": "cross_repo", "backporting_analysis": {"source_repo": "https://github.com/ImageMagick/ImageMagick6", "target_repo": "https://github.com/ImageMagick/ImageMagick", "source_commit": "1aea203eb36409ce6903b9e41fe7cb70030e8750", "target_commit": "30ccf9a0da1f47161b5935a95be854fe84e6c2a2", "analysis_result": "cross_repo"}}}, {"unified_id": "CVE-2022-43680_libexpat_libexpat_libexpat_libexpat_eedc5f6d", "cve_id": "CVE-2022-43680", "patch_type": "same_repo", "projects": {"source": {"name": "libexpat/libexpat", "repo": "https://github.com/libexpat/libexpat", "language": "C"}, "target": {"name": "libexpat/libexpat", "repo": "https://github.com/libexpat/libexpat", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "05c6788b364816f3c584f47e9819119138d151b0"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "eedc5f6de8e219130032c8ff2ff17580e18bd0c1"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-43680", "year": 2022, "bug_type": "use-after-free", "source_commit_date": "2022-07-25T08:12:19Z", "target_commit_date": "2022-09-21T01:32:26Z", "source_files_changed": [], "target_files_changed": [], "source_commit_url": "https://github.com/libexpat/libexpat/commit/05c6788b364816f3c584f47e9819119138d151b0", "target_commit_url": "https://github.com/libexpat/libexpat/commit/eedc5f6de8e219130032c8ff2ff17580e18bd0c1", "backporting_type": "cross_branch", "backporting_analysis": {"source_repo": "https://github.com/libexpat/libexpat", "target_repo": "https://github.com/libexpat/libexpat", "source_commit": "05c6788b364816f3c584f47e9819119138d151b0", "target_commit": "eedc5f6de8e219130032c8ff2ff17580e18bd0c1", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2022-41909_tensorflow_tensorflow_tensorflow_tensorflow_bf594d08", "cve_id": "CVE-2022-41909", "patch_type": "same_repo", "projects": {"source": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}, "target": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "660ce5a89eb6766834bdc303d2ab3902aef99d3d"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "bf594d08d377dc6a3354d9fdb494b32d45f91971"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-41909", "year": 2022, "bug_type": "NULL pointer dereference", "source_commit_date": "2022-09-14T21:54:10Z", "target_commit_date": "2022-09-15T17:18:37Z", "source_files_changed": ["tensorflow/core/kernels/composite_tensor_ops.cc"], "target_files_changed": ["tensorflow/core/kernels/composite_tensor_ops.cc"], "source_commit_url": "https://github.com/tensorflow/tensorflow/commit/660ce5a89eb6766834bdc303d2ab3902aef99d3d", "target_commit_url": "https://github.com/tensorflow/tensorflow/commit/bf594d08d377dc6a3354d9fdb494b32d45f91971", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "source_commit": "660ce5a89eb6766834bdc303d2ab3902aef99d3d", "target_commit": "bf594d08d377dc6a3354d9fdb494b32d45f91971", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2022-45873_systemd_systemd_systemd_systemd_076b807b", "cve_id": "CVE-2022-45873", "patch_type": "same_repo", "projects": {"source": {"name": "systemd/systemd", "repo": "https://github.com/systemd/systemd", "language": "C"}, "target": {"name": "systemd/systemd", "repo": "https://github.com/systemd/systemd", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "de76643b3fbd49dbd3713e59d59e206fc28cc3f0"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "076b807be472630692c5348c60d0c2b7b28ad437"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-45873", "year": 2022, "bug_type": "denial of service", "source_commit_date": "2022-09-30T12:14:33Z", "target_commit_date": "2022-10-18T16:23:53Z", "source_files_changed": [], "target_files_changed": [], "source_commit_url": "https://github.com/systemd/systemd/commit/de76643b3fbd49dbd3713e59d59e206fc28cc3f0", "target_commit_url": "https://github.com/systemd/systemd/commit/076b807be472630692c5348c60d0c2b7b28ad437", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/systemd/systemd", "target_repo": "https://github.com/systemd/systemd", "source_commit": "de76643b3fbd49dbd3713e59d59e206fc28cc3f0", "target_commit": "076b807be472630692c5348c60d0c2b7b28ad437", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2022-23476_sparklemotion_nokogiri_sparklemotion_nokogiri_85410e38", "cve_id": "CVE-2022-23476", "patch_type": "same_repo", "projects": {"source": {"name": "sparklemotion/nokogiri", "repo": "https://github.com/sparklemotion/nokogiri", "language": "C"}, "target": {"name": "sparklemotion/nokogiri", "repo": "https://github.com/sparklemotion/nokogiri", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "9fe0761c47c0d4270d1a5220cfd25de080350d50"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "85410e38410f670cbbc8c5b00d07b843caee88ce"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-23476", "year": 2022, "bug_type": "NULL pointer dereference", "source_commit_date": "2022-12-06T06:51:37Z", "target_commit_date": "2022-12-07T21:25:07Z", "source_files_changed": ["ext/nokogiri/xml_reader.c"], "target_files_changed": ["ext/nokogiri/xml_reader.c"], "source_commit_url": "https://github.com/sparklemotion/nokogiri/commit/9fe0761c47c0d4270d1a5220cfd25de080350d50", "target_commit_url": "https://github.com/sparklemotion/nokogiri/commit/85410e38410f670cbbc8c5b00d07b843caee88ce", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/sparklemotion/nokogiri", "target_repo": "https://github.com/sparklemotion/nokogiri", "source_commit": "9fe0761c47c0d4270d1a5220cfd25de080350d50", "target_commit": "85410e38410f670cbbc8c5b00d07b843caee88ce", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2020-36619_EliasOenal_multimon-ng_EliasOenal_multimon-ng_50025846", "cve_id": "CVE-2020-36619", "patch_type": "same_repo", "projects": {"source": {"name": "EliasOenal/multimon-ng", "repo": "https://github.com/EliasOenal/multimon-ng", "language": "C"}, "target": {"name": "EliasOenal/multimon-ng", "repo": "https://github.com/EliasOenal/multimon-ng", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "e5a51c508ef952e81a6da25b43034dd1ed023c07"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "500258464f7fc8ee04e9b70b0f0727320f7bde1e"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2020-36619", "year": 2020, "bug_type": "memory overflow", "source_commit_date": "2020-05-31T22:24:00Z", "target_commit_date": "2020-05-31T22:28:18Z", "source_files_changed": ["demod_flex.c"], "target_files_changed": [], "source_commit_url": "https://github.com/EliasOenal/multimon-ng/commit/e5a51c508ef952e81a6da25b43034dd1ed023c07", "target_commit_url": "https://github.com/EliasOenal/multimon-ng/commit/500258464f7fc8ee04e9b70b0f0727320f7bde1e", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/EliasOenal/multimon-ng", "target_repo": "https://github.com/EliasOenal/multimon-ng", "source_commit": "e5a51c508ef952e81a6da25b43034dd1ed023c07", "target_commit": "500258464f7fc8ee04e9b70b0f0727320f7bde1e", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2022-47932_brave_brave-core_brave_brave-core_e7330966", "cve_id": "CVE-2022-47932", "patch_type": "same_repo", "projects": {"source": {"name": "brave/brave-core", "repo": "https://github.com/brave/brave-core", "language": "C"}, "target": {"name": "brave/brave-core", "repo": "https://github.com/brave/brave-core", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "498ade8c00de9a3393698deee17f7e89b4d9ac39"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "e73309665508c17e48a67e302d3ab02a38d3ef50"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-47932", "year": 2022, "bug_type": "denial of service", "source_commit_date": "2022-07-18T07:48:25Z", "target_commit_date": "2022-07-19T14:04:56Z", "source_files_changed": [], "target_files_changed": ["browser/net/ipfs_redirect_network_delegate_helper.cc", "browser/net/ipfs_redirect_network_delegate_helper_unittest.cc"], "source_commit_url": "https://github.com/brave/brave-core/commit/498ade8c00de9a3393698deee17f7e89b4d9ac39", "target_commit_url": "https://github.com/brave/brave-core/commit/e73309665508c17e48a67e302d3ab02a38d3ef50", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/brave/brave-core", "target_repo": "https://github.com/brave/brave-core", "source_commit": "498ade8c00de9a3393698deee17f7e89b4d9ac39", "target_commit": "e73309665508c17e48a67e302d3ab02a38d3ef50", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2022-47934_brave_brave-core_brave_brave-core_82d8e390", "cve_id": "CVE-2022-47934", "patch_type": "same_repo", "projects": {"source": {"name": "brave/brave-core", "repo": "https://github.com/brave/brave-core", "language": "C"}, "target": {"name": "brave/brave-core", "repo": "https://github.com/brave/brave-core", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "73304545a3e1f7897114258867996dd02d26fc51"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "82d8e39043e691e0492519126437275511ee87e8"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2022-47934", "year": 2022, "bug_type": "denial of service", "source_commit_date": "2022-08-01T14:29:44Z", "target_commit_date": "2022-08-08T19:22:25Z", "source_files_changed": [], "target_files_changed": ["browser/brave_content_browser_client.cc", "browser/ipfs/ipfs_subframe_navigation_throttle.cc", "browser/ipfs/ipfs_subframe_navigation_throttle.h", "browser/ipfs/test/ipfs_service_browsertest.cc", "browser/net/ipfs_redirect_network_delegate_helper.cc", "browser/net/ipfs_redirect_network_delegate_helper_unittest.cc"], "source_commit_url": "https://github.com/brave/brave-core/commit/73304545a3e1f7897114258867996dd02d26fc51", "target_commit_url": "https://github.com/brave/brave-core/commit/82d8e39043e691e0492519126437275511ee87e8", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/brave/brave-core", "target_repo": "https://github.com/brave/brave-core", "source_commit": "73304545a3e1f7897114258867996dd02d26fc51", "target_commit": "82d8e39043e691e0492519126437275511ee87e8", "analysis_result": "same_version"}}}]