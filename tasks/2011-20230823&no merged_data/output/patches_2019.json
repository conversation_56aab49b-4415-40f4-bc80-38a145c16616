[{"unified_id": "CVE-2019-9199_jjanku_podofo_mksdev_podofo_1400a9aa", "cve_id": "CVE-2019-9199", "patch_type": "cross_repo", "projects": {"source": {"name": "jjanku/podofo", "repo": "https://github.com/jjanku/podofo", "language": "C"}, "target": {"name": "mksdev/podofo", "repo": "https://github.com/mksdev/podofo", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "ada821df68fb0bf673840ed525daf4ec709dbfd9"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "1400a9aaf611299b9a56aa2abeb158918b9743c8"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2019-9199", "year": 2019, "bug_type": "NULL pointer dereference", "source_commit_date": "2019-03-09T18:29:29Z", "target_commit_date": "2019-03-09T18:29:29Z", "source_files_changed": ["tools/podofoimpose/pdftranslator.cpp"], "target_files_changed": ["tools/podofoimpose/pdftranslator.cpp"], "source_commit_url": "https://github.com/jjanku/podofo/commit/ada821df68fb0bf673840ed525daf4ec709dbfd9", "target_commit_url": "https://github.com/mksdev/podofo/commit/1400a9aaf611299b9a56aa2abeb158918b9743c8", "backporting_type": "cross_repo", "backporting_analysis": {"source_repo": "https://github.com/jjanku/podofo", "target_repo": "https://github.com/mksdev/podofo", "source_commit": "ada821df68fb0bf673840ed525daf4ec709dbfd9", "target_commit": "1400a9aaf611299b9a56aa2abeb158918b9743c8", "analysis_result": "cross_repo"}}}, {"unified_id": "CVE-2019-12108_miniupnp_miniupnp_miniupnp_miniupnp_86030db8", "cve_id": "CVE-2019-12108", "patch_type": "same_repo", "projects": {"source": {"name": "miniupnp/miniupnp", "repo": "https://github.com/miniupnp/miniupnp", "language": "C"}, "target": {"name": "miniupnp/miniupnp", "repo": "https://github.com/miniupnp/miniupnp", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "13585f15c7f7dc28bbbba1661efb280d530d114c"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "86030db849260dd8fb2ed975b9890aef1b62b692"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2019-12108", "year": 2019, "bug_type": "NULL pointer dereference", "source_commit_date": "2018-12-18T21:54:51Z", "target_commit_date": "2018-12-18T22:47:54Z", "source_files_changed": ["miniupnpd/upnpsoap.c"], "target_files_changed": ["miniupnpd/upnpsoap.c"], "source_commit_url": "https://github.com/miniupnp/miniupnp/commit/13585f15c7f7dc28bbbba1661efb280d530d114c", "target_commit_url": "https://github.com/miniupnp/miniupnp/commit/86030db849260dd8fb2ed975b9890aef1b62b692", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/miniupnp/miniupnp", "target_repo": "https://github.com/miniupnp/miniupnp", "source_commit": "13585f15c7f7dc28bbbba1661efb280d530d114c", "target_commit": "86030db849260dd8fb2ed975b9890aef1b62b692", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2019-12109_miniupnp_miniupnp_miniupnp_miniupnp_86030db8", "cve_id": "CVE-2019-12109", "patch_type": "same_repo", "projects": {"source": {"name": "miniupnp/miniupnp", "repo": "https://github.com/miniupnp/miniupnp", "language": "C"}, "target": {"name": "miniupnp/miniupnp", "repo": "https://github.com/miniupnp/miniupnp", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "13585f15c7f7dc28bbbba1661efb280d530d114c"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "86030db849260dd8fb2ed975b9890aef1b62b692"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2019-12109", "year": 2019, "bug_type": "NULL pointer dereference", "source_commit_date": "2018-12-18T21:54:51Z", "target_commit_date": "2018-12-18T22:47:54Z", "source_files_changed": ["miniupnpd/upnpsoap.c"], "target_files_changed": ["miniupnpd/upnpsoap.c"], "source_commit_url": "https://github.com/miniupnp/miniupnp/commit/13585f15c7f7dc28bbbba1661efb280d530d114c", "target_commit_url": "https://github.com/miniupnp/miniupnp/commit/86030db849260dd8fb2ed975b9890aef1b62b692", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/miniupnp/miniupnp", "target_repo": "https://github.com/miniupnp/miniupnp", "source_commit": "13585f15c7f7dc28bbbba1661efb280d530d114c", "target_commit": "86030db849260dd8fb2ed975b9890aef1b62b692", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2018-18839_netdata_netdata_netdata_netdata_92327c9e", "cve_id": "CVE-2018-18839", "patch_type": "same_repo", "projects": {"source": {"name": "netdata/netdata", "repo": "https://github.com/netdata/netdata", "language": "C"}, "target": {"name": "netdata/netdata", "repo": "https://github.com/netdata/netdata", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "1e7eeb83e02620534845b35a09c4578244c8959a"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "92327c9ec211bd1616315abcb255861b130b97ca"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2018-18839", "year": 2018, "bug_type": "information leakage", "source_commit_date": "2018-10-30T00:10:22Z", "target_commit_date": "2018-10-30T00:35:57Z", "source_files_changed": [], "target_files_changed": ["libnetdata/url/url.c", "web/api/web_api_v1.c"], "source_commit_url": "https://github.com/netdata/netdata/commit/1e7eeb83e02620534845b35a09c4578244c8959a", "target_commit_url": "https://github.com/netdata/netdata/commit/92327c9ec211bd1616315abcb255861b130b97ca", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/netdata/netdata", "target_repo": "https://github.com/netdata/netdata", "source_commit": "1e7eeb83e02620534845b35a09c4578244c8959a", "target_commit": "92327c9ec211bd1616315abcb255861b130b97ca", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2019-13454_ImageMagick_ImageMagick_ImageMagick_ImageMagick6_4f31d787", "cve_id": "CVE-2019-13454", "patch_type": "cross_repo", "projects": {"source": {"name": "ImageMagick/ImageMagick", "repo": "https://github.com/ImageMagick/ImageMagick", "language": "C"}, "target": {"name": "ImageMagick/ImageMagick6", "repo": "https://github.com/ImageMagick/ImageMagick6", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "1ddcf2e4f28029a888cadef2e757509ef5047ad8"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "4f31d78716ac94c85c244efcea368fea202e2ed4"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2019-13454", "year": 2019, "bug_type": "divide by zero", "source_commit_date": "2019-07-08T10:14:34Z", "target_commit_date": "2019-07-08T10:21:03Z", "source_files_changed": ["MagickCore/layer.c"], "target_files_changed": ["magick/layer.c"], "source_commit_url": "https://github.com/ImageMagick/ImageMagick/commit/1ddcf2e4f28029a888cadef2e757509ef5047ad8", "target_commit_url": "https://github.com/ImageMagick/ImageMagick6/commit/4f31d78716ac94c85c244efcea368fea202e2ed4", "backporting_type": "cross_repo", "backporting_analysis": {"source_repo": "https://github.com/ImageMagick/ImageMagick", "target_repo": "https://github.com/ImageMagick/ImageMagick6", "source_commit": "1ddcf2e4f28029a888cadef2e757509ef5047ad8", "target_commit": "4f31d78716ac94c85c244efcea368fea202e2ed4", "analysis_result": "cross_repo"}}}, {"unified_id": "CVE-2019-14980_ImageMagick_ImageMagick6_ImageMagick_ImageMagick_c5d012a4", "cve_id": "CVE-2019-14980", "patch_type": "cross_repo", "projects": {"source": {"name": "ImageMagick/ImageMagick6", "repo": "https://github.com/ImageMagick/ImageMagick6", "language": "C"}, "target": {"name": "ImageMagick/ImageMagick", "repo": "https://github.com/ImageMagick/ImageMagick", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "614a257295bdcdeda347086761062ac7658b6830"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "c5d012a46ae22be9444326aa37969a3f75daa3ba"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2019-14980", "year": 2019, "bug_type": "use-after-free", "source_commit_date": "2019-04-22T16:42:50Z", "target_commit_date": "2019-04-22T16:43:09Z", "source_files_changed": ["magick/blob.c"], "target_files_changed": ["MagickCore/blob.c"], "source_commit_url": "https://github.com/ImageMagick/ImageMagick6/commit/614a257295bdcdeda347086761062ac7658b6830", "target_commit_url": "https://github.com/ImageMagick/ImageMagick/commit/c5d012a46ae22be9444326aa37969a3f75daa3ba", "backporting_type": "cross_repo", "backporting_analysis": {"source_repo": "https://github.com/ImageMagick/ImageMagick6", "target_repo": "https://github.com/ImageMagick/ImageMagick", "source_commit": "614a257295bdcdeda347086761062ac7658b6830", "target_commit": "c5d012a46ae22be9444326aa37969a3f75daa3ba", "analysis_result": "cross_repo"}}}, {"unified_id": "CVE-2019-14981_ImageMagick_ImageMagick_ImageMagick_ImageMagick6_b522d2d8", "cve_id": "CVE-2019-14981", "patch_type": "cross_repo", "projects": {"source": {"name": "ImageMagick/ImageMagick", "repo": "https://github.com/ImageMagick/ImageMagick", "language": "C"}, "target": {"name": "ImageMagick/ImageMagick6", "repo": "https://github.com/ImageMagick/ImageMagick6", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "a77d8d97f5a7bced0468f0b08798c83fb67427bc"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "b522d2d857d2f75b659936b59b0da9df1682c256"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2019-14981", "year": 2019, "bug_type": "divide by zero", "source_commit_date": "2019-04-18T23:55:30Z", "target_commit_date": "2019-04-18T23:55:44Z", "source_files_changed": ["MagickCore/feature.c"], "target_files_changed": ["magick/feature.c"], "source_commit_url": "https://github.com/ImageMagick/ImageMagick/commit/a77d8d97f5a7bced0468f0b08798c83fb67427bc", "target_commit_url": "https://github.com/ImageMagick/ImageMagick6/commit/b522d2d857d2f75b659936b59b0da9df1682c256", "backporting_type": "cross_repo", "backporting_analysis": {"source_repo": "https://github.com/ImageMagick/ImageMagick", "target_repo": "https://github.com/ImageMagick/ImageMagick6", "source_commit": "a77d8d97f5a7bced0468f0b08798c83fb67427bc", "target_commit": "b522d2d857d2f75b659936b59b0da9df1682c256", "analysis_result": "cross_repo"}}}, {"unified_id": "CVE-2019-15785_fontforge_fontforge_fontforge_fontforge_4775d6d8", "cve_id": "CVE-2019-15785", "patch_type": "same_repo", "projects": {"source": {"name": "fontforge/fontforge", "repo": "https://github.com/fontforge/fontforge", "language": "C"}, "target": {"name": "fontforge/fontforge", "repo": "https://github.com/fontforge/fontforge", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "626f751752875a0ddd74b9e217b6f4828713573c"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "4775d6d8b77e4b28dedd521abbed1b5820e52582"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2019-15785", "year": 2019, "bug_type": "memory overflow", "source_commit_date": "2019-08-13T11:09:59Z", "target_commit_date": "2019-08-20T05:38:37Z", "source_files_changed": ["fontforge/views.h", "fontforgeexe/fontview.c", "fontforgeexe/prefs.c", "fontforgeexe/scriptingdlg.c", "gdraw/gtextfield.c", "inc/gdraw.h", "inc/ggadget.h"], "target_files_changed": [], "source_commit_url": "https://github.com/fontforge/fontforge/commit/626f751752875a0ddd74b9e217b6f4828713573c", "target_commit_url": "https://github.com/fontforge/fontforge/commit/4775d6d8b77e4b28dedd521abbed1b5820e52582", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/fontforge/fontforge", "target_repo": "https://github.com/fontforge/fontforge", "source_commit": "626f751752875a0ddd74b9e217b6f4828713573c", "target_commit": "4775d6d8b77e4b28dedd521abbed1b5820e52582", "analysis_result": "same_version"}}}]