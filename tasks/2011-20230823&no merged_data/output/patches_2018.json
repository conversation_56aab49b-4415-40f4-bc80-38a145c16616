[{"unified_id": "CVE-2018-8098_libgit2_libgit2_libgit2_libgit2_3db1af1f", "cve_id": "CVE-2018-8098", "patch_type": "same_repo", "projects": {"source": {"name": "libgit2/libgit2", "repo": "https://github.com/libgit2/libgit2", "language": "C"}, "target": {"name": "libgit2/libgit2", "repo": "https://github.com/libgit2/libgit2", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "3207ddb0103543da8ad2139ec6539f590f9900c1"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "3db1af1f370295ad5355b8f64b865a2a357bcac0"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2018-8098", "year": 2018, "bug_type": "integer overflow", "source_commit_date": "2018-03-08T12:00:27Z", "target_commit_date": "2018-03-08T12:36:46Z", "source_files_changed": ["src/index.c"], "target_files_changed": ["src/index.c"], "source_commit_url": "https://github.com/libgit2/libgit2/commit/3207ddb0103543da8ad2139ec6539f590f9900c1", "target_commit_url": "https://github.com/libgit2/libgit2/commit/3db1af1f370295ad5355b8f64b865a2a357bcac0", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/libgit2/libgit2", "target_repo": "https://github.com/libgit2/libgit2", "source_commit": "3207ddb0103543da8ad2139ec6539f590f9900c1", "target_commit": "3db1af1f370295ad5355b8f64b865a2a357bcac0", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2018-11219_antirez_redis_antirez_redis_e89086e0", "cve_id": "CVE-2018-11219", "patch_type": "same_repo", "projects": {"source": {"name": "antirez/redis", "repo": "https://github.com/antirez/redis", "language": "C"}, "target": {"name": "antirez/redis", "repo": "https://github.com/antirez/redis", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "1eb08bcd4634ae42ec45e8284923ac048beaa4c3"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "e89086e09a38cc6713bcd4b9c29abf92cf393936"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2018-11219", "year": 2018, "bug_type": "integer overflow", "source_commit_date": "2018-05-14T15:49:06Z", "target_commit_date": "2018-05-15T11:13:49Z", "source_files_changed": ["deps/lua/src/lua_struct.c"], "target_files_changed": ["deps/lua/src/lua_struct.c"], "source_commit_url": "https://github.com/antirez/redis/commit/1eb08bcd4634ae42ec45e8284923ac048beaa4c3", "target_commit_url": "https://github.com/antirez/redis/commit/e89086e09a38cc6713bcd4b9c29abf92cf393936", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/antirez/redis", "target_repo": "https://github.com/antirez/redis", "source_commit": "1eb08bcd4634ae42ec45e8284923ac048beaa4c3", "target_commit": "e89086e09a38cc6713bcd4b9c29abf92cf393936", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2018-1000524_fatcerberus_minisphere_fatcerberus_minisphere_252c1ca1", "cve_id": "CVE-2018-1000524", "patch_type": "same_repo", "projects": {"source": {"name": "fatcerberus/minisphere", "repo": "https://github.com/fatcerberus/minisphere", "language": "C"}, "target": {"name": "fatcerberus/minisphere", "repo": "https://github.com/fatcerberus/minisphere", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "fd1180fac718f890424726408a4c2585307b3a6b"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "252c1ca184cb38e1acb917aa0e451c5f08519996"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2018-1000524", "year": 2018, "bug_type": "integer overflow", "source_commit_date": "2018-06-13T02:45:32Z", "target_commit_date": "2018-06-13T05:12:57Z", "source_files_changed": [], "target_files_changed": ["src/minisphere/map_engine.c"], "source_commit_url": "https://github.com/fatcerberus/minisphere/commit/fd1180fac718f890424726408a4c2585307b3a6b", "target_commit_url": "https://github.com/fatcerberus/minisphere/commit/252c1ca184cb38e1acb917aa0e451c5f08519996", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/fatcerberus/minisphere", "target_repo": "https://github.com/fatcerberus/minisphere", "source_commit": "fd1180fac718f890424726408a4c2585307b3a6b", "target_commit": "252c1ca184cb38e1acb917aa0e451c5f08519996", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2018-10887_libgit2_libgit2_libgit2_libgit2_c1577110", "cve_id": "CVE-2018-10887", "patch_type": "same_repo", "projects": {"source": {"name": "libgit2/libgit2", "repo": "https://github.com/libgit2/libgit2", "language": "C"}, "target": {"name": "libgit2/libgit2", "repo": "https://github.com/libgit2/libgit2", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "3f461902dc1072acb8b7607ee65d0a0458ffac2a"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "c1577110467b701dcbcf9439ac225ea851b47d22"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2018-10887", "year": 2018, "bug_type": "integer overflow", "source_commit_date": "2018-06-29T05:45:18Z", "target_commit_date": "2018-07-05T11:30:46Z", "source_files_changed": ["src/delta.c", "tests/delta/apply.c", "tests/diff/binary.c"], "target_files_changed": ["src/delta.c"], "source_commit_url": "https://github.com/libgit2/libgit2/commit/3f461902dc1072acb8b7607ee65d0a0458ffac2a", "target_commit_url": "https://github.com/libgit2/libgit2/commit/c1577110467b701dcbcf9439ac225ea851b47d22", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/libgit2/libgit2", "target_repo": "https://github.com/libgit2/libgit2", "source_commit": "3f461902dc1072acb8b7607ee65d0a0458ffac2a", "target_commit": "c1577110467b701dcbcf9439ac225ea851b47d22", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2016-9583_mdad<PERSON>_jasper_mdadams_jasper_f25486c3", "cve_id": "CVE-2016-9583", "patch_type": "same_repo", "projects": {"source": {"name": "mdadams/jasper", "repo": "https://github.com/mdadams/jasper", "language": "C"}, "target": {"name": "mdadams/jasper", "repo": "https://github.com/mdadams/jasper", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "aa0b0f79ade5eef8b0e7a214c03f5af54b36ba7d"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "f25486c3d4aa472fec79150f2c41ed4333395d3d"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2016-9583", "year": 2016, "bug_type": "integer overflow", "source_commit_date": "2016-11-27T01:14:09Z", "target_commit_date": "2016-11-27T04:54:24Z", "source_files_changed": ["src/libjasper/include/jasper/jas_types.h", "src/libjasper/jpc/jpc_t2cod.c", "src/libjasper/jpc/jpc_t2dec.c"], "target_files_changed": ["src/libjasper/jpc/jpc_t2cod.c"], "source_commit_url": "https://github.com/mdadams/jasper/commit/aa0b0f79ade5eef8b0e7a214c03f5af54b36ba7d", "target_commit_url": "https://github.com/mdadams/jasper/commit/f25486c3d4aa472fec79150f2c41ed4333395d3d", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/mdadams/jasper", "target_repo": "https://github.com/mdadams/jasper", "source_commit": "aa0b0f79ade5eef8b0e7a214c03f5af54b36ba7d", "target_commit": "f25486c3d4aa472fec79150f2c41ed4333395d3d", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2018-8005_apache_trafficserver_apache_trafficserver_015c9bc5", "cve_id": "CVE-2018-8005", "patch_type": "same_repo", "projects": {"source": {"name": "apache/trafficserver", "repo": "https://github.com/apache/trafficserver", "language": "C"}, "target": {"name": "apache/trafficserver", "repo": "https://github.com/apache/trafficserver", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "6d248026b04d69e5c5049709c17ea671328ea4ea"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "015c9bc58d5ebf733060e6b23f54bfb143155f9d"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2018-8005", "year": 2018, "bug_type": "denial of service", "source_commit_date": "2018-02-06T21:28:30Z", "target_commit_date": "2018-02-14T23:32:29Z", "source_files_changed": [], "target_files_changed": [], "source_commit_url": "https://github.com/apache/trafficserver/commit/6d248026b04d69e5c5049709c17ea671328ea4ea", "target_commit_url": "https://github.com/apache/trafficserver/commit/015c9bc58d5ebf733060e6b23f54bfb143155f9d", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/apache/trafficserver", "target_repo": "https://github.com/apache/trafficserver", "source_commit": "6d248026b04d69e5c5049709c17ea671328ea4ea", "target_commit": "015c9bc58d5ebf733060e6b23f54bfb143155f9d", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2018-17568_viabtc_viabtc_exchange_server_viabtc_viabtc_exchange_server_4a7c27bf", "cve_id": "CVE-2018-17568", "patch_type": "same_repo", "projects": {"source": {"name": "viabtc/viabtc_exchange_server", "repo": "https://github.com/viabtc/viabtc_exchange_server", "language": "C"}, "target": {"name": "viabtc/viabtc_exchange_server", "repo": "https://github.com/viabtc/viabtc_exchange_server", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "5c2dc2b7856a66338e2f113540fd2e8fcb847f9e"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "4a7c27bfe98f409623d4d857894d017ff0672cc9"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2018-17568", "year": 2018, "bug_type": "integer overflow", "source_commit_date": "2018-08-08T03:10:45Z", "target_commit_date": "2018-08-21T06:50:19Z", "source_files_changed": [], "target_files_changed": ["network/nw_buf.c", "utils/ut_rpc.c", "utils/ut_rpc.h", "utils/ut_ws_svr.c", "utils/ut_ws_svr.h"], "source_commit_url": "https://github.com/viabtc/viabtc_exchange_server/commit/5c2dc2b7856a66338e2f113540fd2e8fcb847f9e", "target_commit_url": "https://github.com/viabtc/viabtc_exchange_server/commit/4a7c27bfe98f409623d4d857894d017ff0672cc9#diff-0c23effa84a7b85053bac7981a8580c8", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/viabtc/viabtc_exchange_server", "target_repo": "https://github.com/viabtc/viabtc_exchange_server", "source_commit": "5c2dc2b7856a66338e2f113540fd2e8fcb847f9e", "target_commit": "4a7c27bfe98f409623d4d857894d017ff0672cc9", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2018-17569_viabtc_viabtc_exchange_server_viabtc_viabtc_exchange_server_4a7c27bf", "cve_id": "CVE-2018-17569", "patch_type": "same_repo", "projects": {"source": {"name": "viabtc/viabtc_exchange_server", "repo": "https://github.com/viabtc/viabtc_exchange_server", "language": "C"}, "target": {"name": "viabtc/viabtc_exchange_server", "repo": "https://github.com/viabtc/viabtc_exchange_server", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "5c2dc2b7856a66338e2f113540fd2e8fcb847f9e"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "4a7c27bfe98f409623d4d857894d017ff0672cc9"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2018-17569", "year": 2018, "bug_type": "integer overflow", "source_commit_date": "2018-08-08T03:10:45Z", "target_commit_date": "2018-08-21T06:50:19Z", "source_files_changed": [], "target_files_changed": ["network/nw_buf.c", "utils/ut_rpc.c", "utils/ut_rpc.h", "utils/ut_ws_svr.c", "utils/ut_ws_svr.h"], "source_commit_url": "https://github.com/viabtc/viabtc_exchange_server/commit/5c2dc2b7856a66338e2f113540fd2e8fcb847f9e", "target_commit_url": "https://github.com/viabtc/viabtc_exchange_server/commit/4a7c27bfe98f409623d4d857894d017ff0672cc9#diff-9fabc53ea796ec492aef432594298baa", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/viabtc/viabtc_exchange_server", "target_repo": "https://github.com/viabtc/viabtc_exchange_server", "source_commit": "5c2dc2b7856a66338e2f113540fd2e8fcb847f9e", "target_commit": "4a7c27bfe98f409623d4d857894d017ff0672cc9", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2018-17570_viabtc_viabtc_exchange_server_viabtc_viabtc_exchange_server_4a7c27bf", "cve_id": "CVE-2018-17570", "patch_type": "same_repo", "projects": {"source": {"name": "viabtc/viabtc_exchange_server", "repo": "https://github.com/viabtc/viabtc_exchange_server", "language": "C"}, "target": {"name": "viabtc/viabtc_exchange_server", "repo": "https://github.com/viabtc/viabtc_exchange_server", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "5c2dc2b7856a66338e2f113540fd2e8fcb847f9e"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "4a7c27bfe98f409623d4d857894d017ff0672cc9"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2018-17570", "year": 2018, "bug_type": "integer overflow", "source_commit_date": "2018-08-08T03:10:45Z", "target_commit_date": "2018-08-21T06:50:19Z", "source_files_changed": [], "target_files_changed": ["network/nw_buf.c", "utils/ut_rpc.c", "utils/ut_rpc.h", "utils/ut_ws_svr.c", "utils/ut_ws_svr.h"], "source_commit_url": "https://github.com/viabtc/viabtc_exchange_server/commit/5c2dc2b7856a66338e2f113540fd2e8fcb847f9e", "target_commit_url": "https://github.com/viabtc/viabtc_exchange_server/commit/4a7c27bfe98f409623d4d857894d017ff0672cc9#diff-515c81af848352583bff286d6224875f", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/viabtc/viabtc_exchange_server", "target_repo": "https://github.com/viabtc/viabtc_exchange_server", "source_commit": "5c2dc2b7856a66338e2f113540fd2e8fcb847f9e", "target_commit": "4a7c27bfe98f409623d4d857894d017ff0672cc9", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2018-19045_acassen_keepalived_acassen_keepalived_5241e4d7", "cve_id": "CVE-2018-19045", "patch_type": "same_repo", "projects": {"source": {"name": "acassen/keepalived", "repo": "https://github.com/acassen/keepalived", "language": "C"}, "target": {"name": "acassen/keepalived", "repo": "https://github.com/acassen/keepalived", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "c6247a9ef2c7b33244ab1d3aa5d629ec49f0a067"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "5241e4d7b177d0b6f073cfc9ed5444bf51ec89d6"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2018-19045", "year": 2018, "bug_type": "information leakage", "source_commit_date": "2018-10-31T17:08:51Z", "target_commit_date": "2018-10-31T21:57:02Z", "source_files_changed": ["keepalived/core/global_data.c", "keepalived/core/global_parser.c", "keepalived/core/main.c", "keepalived/include/global_data.h", "keepalived/include/main.h"], "target_files_changed": ["keepalived/core/main.c"], "source_commit_url": "https://github.com/acassen/keepalived/commit/c6247a9ef2c7b33244ab1d3aa5d629ec49f0a067", "target_commit_url": "https://github.com/acassen/keepalived/commit/5241e4d7b177d0b6f073cfc9ed5444bf51ec89d6", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/acassen/keepalived", "target_repo": "https://github.com/acassen/keepalived", "source_commit": "c6247a9ef2c7b33244ab1d3aa5d629ec49f0a067", "target_commit": "5241e4d7b177d0b6f073cfc9ed5444bf51ec89d6", "analysis_result": "same_version"}}}]