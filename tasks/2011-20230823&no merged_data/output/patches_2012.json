[{"unified_id": "CVE-2012-1571_glensc_file_glensc_file_1aec04db", "cve_id": "CVE-2012-1571", "patch_type": "same_repo", "projects": {"source": {"name": "glensc/file", "repo": "https://github.com/glensc/file", "language": "C"}, "target": {"name": "glensc/file", "repo": "https://github.com/glensc/file", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "1859fdb4e67c49c463c4e0078054335cd46ba295"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "1aec04dbf8a24b8a6ba64c4f74efa0628e36db0b"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2012-1571", "year": 2012, "bug_type": "memory overflow", "source_commit_date": "2012-02-17T04:23:30Z", "target_commit_date": "2012-02-20T22:35:29Z", "source_files_changed": ["src/cdf.c"], "target_files_changed": ["src/cdf.c"], "source_commit_url": "https://github.com/glensc/file/commit/1859fdb4e67c49c463c4e0078054335cd46ba295", "target_commit_url": "https://github.com/glensc/file/commit/1aec04dbf8a24b8a6ba64c4f74efa0628e36db0b", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/glensc/file", "target_repo": "https://github.com/glensc/file", "source_commit": "1859fdb4e67c49c463c4e0078054335cd46ba295", "target_commit": "1aec04dbf8a24b8a6ba64c4f74efa0628e36db0b", "analysis_result": "same_version"}}}]