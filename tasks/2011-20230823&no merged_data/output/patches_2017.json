[{"unified_id": "CVE-2016-9933_libgd_libgd_php_php-src_863d37ea", "cve_id": "CVE-2016-9933", "patch_type": "cross_repo", "projects": {"source": {"name": "libgd/libgd", "repo": "https://github.com/libgd/libgd", "language": "C"}, "target": {"name": "php/php-src", "repo": "https://github.com/php/php-src", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "77f619d48259383628c3ec4654b1ad578e9eb40e"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "863d37ea66d5c960db08d6f4a2cbd2518f0f80d1"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2016-9933", "year": 2016, "bug_type": "memory overflow", "source_commit_date": "2016-06-04T16:09:01Z", "target_commit_date": "2016-10-25T11:23:16Z", "source_files_changed": ["src/gd.c"], "target_files_changed": ["ext/gd/libgd/gd.c"], "source_commit_url": "https://github.com/libgd/libgd/commit/77f619d48259383628c3ec4654b1ad578e9eb40e", "target_commit_url": "https://github.com/php/php-src/commit/863d37ea66d5c960db08d6f4a2cbd2518f0f80d1", "backporting_type": "cross_repo", "backporting_analysis": {"source_repo": "https://github.com/libgd/libgd", "target_repo": "https://github.com/php/php-src", "source_commit": "77f619d48259383628c3ec4654b1ad578e9eb40e", "target_commit": "863d37ea66d5c960db08d6f4a2cbd2518f0f80d1", "analysis_result": "cross_repo"}}}, {"unified_id": "CVE-2016-10156_systemd_systemd_systemd_systemd_06eeacb6", "cve_id": "CVE-2016-10156", "patch_type": "same_repo", "projects": {"source": {"name": "systemd/systemd", "repo": "https://github.com/systemd/systemd", "language": "C"}, "target": {"name": "systemd/systemd", "repo": "https://github.com/systemd/systemd", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "ee735086f8670be1591fa9593e80dd60163a7a2f"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "06eeacb6fe029804f296b065b3ce91e796e1cd0e"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2016-10156", "year": 2016, "bug_type": "privilege escalation", "source_commit_date": "2015-11-11T21:54:56Z", "target_commit_date": "2016-01-29T21:36:08Z", "source_files_changed": ["src/basic/fs-util.c", "src/core/timer.c", "src/test/test-conf-files.c"], "target_files_changed": ["src/basic/fs-util.c"], "source_commit_url": "https://github.com/systemd/systemd/commit/ee735086f8670be1591fa9593e80dd60163a7a2f", "target_commit_url": "https://github.com/systemd/systemd/commit/06eeacb6fe029804f296b065b3ce91e796e1cd0e", "backporting_type": "cross_branch", "backporting_analysis": {"source_repo": "https://github.com/systemd/systemd", "target_repo": "https://github.com/systemd/systemd", "source_commit": "ee735086f8670be1591fa9593e80dd60163a7a2f", "target_commit": "06eeacb6fe029804f296b065b3ce91e796e1cd0e", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2016-6190_inverse-inc_sogo_inverse-inc_sogo_875a4aca", "cve_id": "CVE-2016-6190", "patch_type": "same_repo", "projects": {"source": {"name": "inverse-inc/sogo", "repo": "https://github.com/inverse-inc/sogo", "language": "C"}, "target": {"name": "inverse-inc/sogo", "repo": "https://github.com/inverse-inc/sogo", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "717f45f640a2866b76a8984139391fae64339225"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "875a4aca3218340fd4d3141950c82c2ff45b343d"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2016-6190", "year": 2016, "bug_type": "information leakage", "source_commit_date": "2016-05-27T14:53:16Z", "target_commit_date": "2016-05-27T14:53:16Z", "source_files_changed": ["SoObjects/SOGo/SOGoUserSettings.h"], "target_files_changed": ["SoObjects/SOGo/SOGoUserSettings.h"], "source_commit_url": "https://github.com/inverse-inc/sogo/commit/717f45f640a2866b76a8984139391fae64339225", "target_commit_url": "https://github.com/inverse-inc/sogo/commit/875a4aca3218340fd4d3141950c82c2ff45b343d", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/inverse-inc/sogo", "target_repo": "https://github.com/inverse-inc/sogo", "source_commit": "717f45f640a2866b76a8984139391fae64339225", "target_commit": "875a4aca3218340fd4d3141950c82c2ff45b343d", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2016-10128_libgit2_libgit2_libgit2_libgit2_66e3774d", "cve_id": "CVE-2016-10128", "patch_type": "same_repo", "projects": {"source": {"name": "libgit2/libgit2", "repo": "https://github.com/libgit2/libgit2", "language": "C"}, "target": {"name": "libgit2/libgit2", "repo": "https://github.com/libgit2/libgit2", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "4ac39c76c0153d1ee6889a0984c39e97731684b2"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "66e3774d279672ee51c3b54545a79d20d1ada834"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2016-10128", "year": 2016, "bug_type": "memory overflow", "source_commit_date": "2016-11-15T10:36:27Z", "target_commit_date": "2016-11-15T10:36:27Z", "source_files_changed": ["src/transports/smart_pkt.c"], "target_files_changed": ["src/transports/smart_pkt.c"], "source_commit_url": "https://github.com/libgit2/libgit2/commit/4ac39c76c0153d1ee6889a0984c39e97731684b2", "target_commit_url": "https://github.com/libgit2/libgit2/commit/66e3774d279672ee51c3b54545a79d20d1ada834", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/libgit2/libgit2", "target_repo": "https://github.com/libgit2/libgit2", "source_commit": "4ac39c76c0153d1ee6889a0984c39e97731684b2", "target_commit": "66e3774d279672ee51c3b54545a79d20d1ada834", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2016-10129_libgit2_libgit2_libgit2_libgit2_84d30d56", "cve_id": "CVE-2016-10129", "patch_type": "same_repo", "projects": {"source": {"name": "libgit2/libgit2", "repo": "https://github.com/libgit2/libgit2", "language": "C"}, "target": {"name": "libgit2/libgit2", "repo": "https://github.com/libgit2/libgit2", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "2fdef641fd0dd2828bd948234ae86de75221a11a"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "84d30d569ada986f3eef527cbdb932643c2dd037"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2016-10129", "year": 2016, "bug_type": "NULL pointer dereference", "source_commit_date": "2016-11-15T10:44:51Z", "target_commit_date": "2016-11-15T10:44:51Z", "source_files_changed": ["src/transports/smart_pkt.c", "src/transports/smart_protocol.c"], "target_files_changed": ["src/transports/smart_pkt.c", "src/transports/smart_protocol.c"], "source_commit_url": "https://github.com/libgit2/libgit2/commit/2fdef641fd0dd2828bd948234ae86de75221a11a", "target_commit_url": "https://github.com/libgit2/libgit2/commit/84d30d569ada986f3eef527cbdb932643c2dd037", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/libgit2/libgit2", "target_repo": "https://github.com/libgit2/libgit2", "source_commit": "2fdef641fd0dd2828bd948234ae86de75221a11a", "target_commit": "84d30d569ada986f3eef527cbdb932643c2dd037", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2017-7586_erikd_libsndfile_erikd_libsndfile_f457b7b5", "cve_id": "CVE-2017-7586", "patch_type": "same_repo", "projects": {"source": {"name": "erikd/libsndfile", "repo": "https://github.com/erikd/libsndfile", "language": "C"}, "target": {"name": "erikd/libsndfile", "repo": "https://github.com/erikd/libsndfile", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "708e996c87c5fae77b104ccfeb8f6db784c32074"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "f457b7b5ecfe91697ed01cfc825772c4d8de1236"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2017-7586", "year": 2017, "bug_type": "memory overflow", "source_commit_date": "2016-11-27T05:12:46Z", "target_commit_date": "2017-03-26T00:41:11Z", "source_files_changed": ["src/common.c", "src/common.h", "src/sndfile.c"], "target_files_changed": ["src/id3.c"], "source_commit_url": "https://github.com/erikd/libsndfile/commit/708e996c87c5fae77b104ccfeb8f6db784c32074", "target_commit_url": "https://github.com/erikd/libsndfile/commit/f457b7b5ecfe91697ed01cfc825772c4d8de1236", "backporting_type": "cross_branch", "backporting_analysis": {"source_repo": "https://github.com/erikd/libsndfile", "target_repo": "https://github.com/erikd/libsndfile", "source_commit": "708e996c87c5fae77b104ccfeb8f6db784c32074", "target_commit": "f457b7b5ecfe91697ed01cfc825772c4d8de1236", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2017-7185_cesanta_mongoose-os_cesanta_mongoose_b8402ed0", "cve_id": "CVE-2017-7185", "patch_type": "cross_repo", "projects": {"source": {"name": "cesanta/mongoose-os", "repo": "https://github.com/cesanta/mongoose-os", "language": "C"}, "target": {"name": "cesanta/mongoose", "repo": "https://github.com/cesanta/mongoose", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "042eb437973a202d00589b13d628181c6de5cf5b"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "b8402ed0733e3f244588b61ad5fedd093e3cf9cc"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2017-7185", "year": 2017, "bug_type": "use-after-free", "source_commit_date": "2017-04-03T09:14:16Z", "target_commit_date": "2017-04-03T09:14:16Z", "source_files_changed": ["mongoose/mongoose.c"], "target_files_changed": ["mongoose.c"], "source_commit_url": "https://github.com/cesanta/mongoose-os/commit/042eb437973a202d00589b13d628181c6de5cf5b", "target_commit_url": "https://github.com/cesanta/mongoose/commit/b8402ed0733e3f244588b61ad5fedd093e3cf9cc", "backporting_type": "cross_repo", "backporting_analysis": {"source_repo": "https://github.com/cesanta/mongoose-os", "target_repo": "https://github.com/cesanta/mongoose", "source_commit": "042eb437973a202d00589b13d628181c6de5cf5b", "target_commit": "b8402ed0733e3f244588b61ad5fedd093e3cf9cc", "analysis_result": "cross_repo"}}}, {"unified_id": "CVE-2017-8289_RIOT-OS_RIOT_RIOT-OS_RIOT_59a7e01b", "cve_id": "CVE-2017-8289", "patch_type": "same_repo", "projects": {"source": {"name": "RIOT-OS/RIOT", "repo": "https://github.com/RIOT-OS/RIOT", "language": "C"}, "target": {"name": "RIOT-OS/RIOT", "repo": "https://github.com/RIOT-OS/RIOT", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "eef90c06fb36ed08f82b1176bf6d1c9c8615be86"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "59a7e01bbfa3b8870bedb7c6820723ee69cd8ca2"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2017-8289", "year": 2017, "bug_type": "memory overflow", "source_commit_date": "2017-04-19T11:51:00Z", "target_commit_date": "2017-04-19T11:51:00Z", "source_files_changed": [], "target_files_changed": [], "source_commit_url": "https://github.com/RIOT-OS/RIOT/commit/eef90c06fb36ed08f82b1176bf6d1c9c8615be86", "target_commit_url": "https://github.com/RIOT-OS/RIOT/commit/59a7e01bbfa3b8870bedb7c6820723ee69cd8ca2", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/RIOT-OS/RIOT", "target_repo": "https://github.com/RIOT-OS/RIOT", "source_commit": "eef90c06fb36ed08f82b1176bf6d1c9c8615be86", "target_commit": "59a7e01bbfa3b8870bedb7c6820723ee69cd8ca2", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2015-4054_pgbouncer_pgbouncer_pgbouncer_pgbouncer_74d6e5f7", "cve_id": "CVE-2015-4054", "patch_type": "same_repo", "projects": {"source": {"name": "pgbouncer/pgbouncer", "repo": "https://github.com/pgbouncer/pgbouncer", "language": "C"}, "target": {"name": "pgbouncer/pgbouncer", "repo": "https://github.com/pgbouncer/pgbouncer", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "edab5be6665b9e8de66c25ba527509b229468573"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "74d6e5f7de5ec736f71204b7b422af7380c19ac5"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2015-4054", "year": 2015, "bug_type": "NULL pointer dereference", "source_commit_date": "2015-04-08T06:22:55Z", "target_commit_date": "2015-04-08T10:44:07Z", "source_files_changed": ["src/client.c"], "target_files_changed": ["src/client.c"], "source_commit_url": "https://github.com/pgbouncer/pgbouncer/commit/edab5be6665b9e8de66c25ba527509b229468573", "target_commit_url": "https://github.com/pgbouncer/pgbouncer/commit/74d6e5f7de5ec736f71204b7b422af7380c19ac5", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/pgbouncer/pgbouncer", "target_repo": "https://github.com/pgbouncer/pgbouncer", "source_commit": "edab5be6665b9e8de66c25ba527509b229468573", "target_commit": "74d6e5f7de5ec736f71204b7b422af7380c19ac5", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2017-9250_zherczeg_jerryscript_jerryscript-project_jerryscript_e58f2880", "cve_id": "CVE-2017-9250", "patch_type": "cross_repo", "projects": {"source": {"name": "zherczeg/jerryscript", "repo": "https://github.com/zherczeg/jerryscript", "language": "C"}, "target": {"name": "jerryscript-project/jerryscript", "repo": "https://github.com/jerryscript-project/jerryscript", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "03a8c630f015f63268639d3ed3bf82cff6fa77d8"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "e58f2880df608652aff7fd35c45b242467ec0e79"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2017-9250", "year": 2017, "bug_type": "NULL pointer dereference", "source_commit_date": "2017-05-22T10:47:42Z", "target_commit_date": "2017-05-22T11:17:08Z", "source_files_changed": ["jerry-core/parser/js/js-lexer.c"], "target_files_changed": ["jerry-core/parser/js/js-lexer.c"], "source_commit_url": "https://github.com/zherczeg/jerryscript/commit/03a8c630f015f63268639d3ed3bf82cff6fa77d8", "target_commit_url": "https://github.com/jerryscript-project/jerryscript/commit/e58f2880df608652aff7fd35c45b242467ec0e79", "backporting_type": "cross_repo", "backporting_analysis": {"source_repo": "https://github.com/zherczeg/jerryscript", "target_repo": "https://github.com/jerryscript-project/jerryscript", "source_commit": "03a8c630f015f63268639d3ed3bf82cff6fa77d8", "target_commit": "e58f2880df608652aff7fd35c45b242467ec0e79", "analysis_result": "cross_repo"}}}, {"unified_id": "CVE-2017-1000249_file_file_file_file_35c94dc6", "cve_id": "CVE-2017-1000249", "patch_type": "same_repo", "projects": {"source": {"name": "file/file", "repo": "https://github.com/file/file", "language": "C"}, "target": {"name": "file/file", "repo": "https://github.com/file/file", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "9611f31313a93aa036389c5f3b15eea53510d4d"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "35c94dc6acc418f1ad7f6241a6680e5327495793"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2017-1000249", "year": 2017, "bug_type": "memory overflow", "source_commit_date": "2016-10-04T21:43:10Z", "target_commit_date": "2017-08-27T07:55:02Z", "source_files_changed": ["src/readelf.c"], "target_files_changed": ["src/readelf.c"], "source_commit_url": "https://github.com/file/file/commit/9611f31313a93aa036389c5f3b15eea53510d4d", "target_commit_url": "https://github.com/file/file/commit/35c94dc6acc418f1ad7f6241a6680e5327495793", "backporting_type": "cross_branch", "backporting_analysis": {"source_repo": "https://github.com/file/file", "target_repo": "https://github.com/file/file", "source_commit": "9611f31313a93aa036389c5f3b15eea53510d4d", "target_commit": "35c94dc6acc418f1ad7f6241a6680e5327495793", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2015-3400_zfsonlinux_zfs_FransUrbo_zfs_99aa4d2b", "cve_id": "CVE-2015-3400", "patch_type": "cross_repo", "projects": {"source": {"name": "zfsonlinux/zfs", "repo": "https://github.com/zfsonlinux/zfs", "language": "C"}, "target": {"name": "FransUrbo/zfs", "repo": "https://github.com/FransUrbo/zfs", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "7d08880f1f9897e545a8e21d087edc63c03e593f"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "99aa4d2b4fd12c6bef62d02ffd1b375ddd42fcf4"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2015-3400", "year": 2015, "bug_type": "information leakage", "source_commit_date": "2014-10-11T10:40:24Z", "target_commit_date": "2014-10-26T13:29:11Z", "source_files_changed": [], "target_files_changed": ["lib/libshare/libshare.c", "lib/libshare/libshare_impl.h", "lib/libshare/nfs.c"], "source_commit_url": "https://github.com/zfsonlinux/zfs/commit/7d08880f1f9897e545a8e21d087edc63c03e593f", "target_commit_url": "https://github.com/FransUrbo/zfs/commit/99aa4d2b4fd12c6bef62d02ffd1b375ddd42fcf4", "backporting_type": "cross_repo", "backporting_analysis": {"source_repo": "https://github.com/zfsonlinux/zfs", "target_repo": "https://github.com/FransUrbo/zfs", "source_commit": "7d08880f1f9897e545a8e21d087edc63c03e593f", "target_commit": "99aa4d2b4fd12c6bef62d02ffd1b375ddd42fcf4", "analysis_result": "cross_repo"}}}, {"unified_id": "CVE-2017-16359_radare_radare2_radare_radare2_fbaf24bc", "cve_id": "CVE-2017-16359", "patch_type": "same_repo", "projects": {"source": {"name": "radare/radare2", "repo": "https://github.com/radare/radare2", "language": "C"}, "target": {"name": "radare/radare2", "repo": "https://github.com/radare/radare2", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "62e39f34b2705131a2d08aff0c2e542c6a52cf0e"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "fbaf24bce7ea4211e4608b3ab6c1b45702cb243d"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2017-16359", "year": 2017, "bug_type": "NULL pointer dereference", "source_commit_date": "2017-10-31T15:35:41Z", "target_commit_date": "2017-11-01T12:44:46Z", "source_files_changed": ["libr/bin/format/elf/elf.c"], "target_files_changed": ["libr/bin/format/elf/elf.c"], "source_commit_url": "https://github.com/radare/radare2/commit/62e39f34b2705131a2d08aff0c2e542c6a52cf0e", "target_commit_url": "https://github.com/radare/radare2/commit/fbaf24bce7ea4211e4608b3ab6c1b45702cb243d", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/radare/radare2", "target_repo": "https://github.com/radare/radare2", "source_commit": "62e39f34b2705131a2d08aff0c2e542c6a52cf0e", "target_commit": "fbaf24bce7ea4211e4608b3ab6c1b45702cb243d", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2017-8807_varnishcache_varnish-cache_varnishcache_varnish-cache_176f8a07", "cve_id": "CVE-2017-8807", "patch_type": "same_repo", "projects": {"source": {"name": "varnishcache/varnish-cache", "repo": "https://github.com/varnishcache/varnish-cache", "language": "C"}, "target": {"name": "varnishcache/varnish-cache", "repo": "https://github.com/varnishcache/varnish-cache", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "e3a88038c245e40907b21392ef5febfae4454e7b"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "176f8a075a963ffbfa56f1c460c15f6a1a6af5a7"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2017-8807", "year": 2017, "bug_type": "memory overflow", "source_commit_date": "2017-09-18T08:18:14Z", "target_commit_date": "2017-09-18T14:04:53Z", "source_files_changed": [], "target_files_changed": ["bin/varnishd/cache/cache_fetch.c"], "source_commit_url": "https://github.com/varnishcache/varnish-cache/commit/e3a88038c245e40907b21392ef5febfae4454e7b", "target_commit_url": "https://github.com/varnishcache/varnish-cache/commit/176f8a075a963ffbfa56f1c460c15f6a1a6af5a7", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/varnishcache/varnish-cache", "target_repo": "https://github.com/varnishcache/varnish-cache", "source_commit": "e3a88038c245e40907b21392ef5febfae4454e7b", "target_commit": "176f8a075a963ffbfa56f1c460c15f6a1a6af5a7", "analysis_result": "same_version"}}}]