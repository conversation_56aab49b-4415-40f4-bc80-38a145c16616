[{"unified_id": "CVE-2013-6412_hercules-team_augeas_hercules-team_augeas_f4f9fa61", "cve_id": "CVE-2013-6412", "patch_type": "same_repo", "projects": {"source": {"name": "hercules-team/augeas", "repo": "https://github.com/hercules-team/augeas", "language": "C"}, "target": {"name": "hercules-team/augeas", "repo": "https://github.com/hercules-team/augeas", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "f5b4fc0c"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "f4f9fa61c0e0e5a10e19e0f48df31022e842dfcc"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2013-6412", "year": 2013, "bug_type": "privilege escalation", "source_commit_date": "2013-12-02T17:49:35Z", "target_commit_date": "2013-12-02T17:49:35Z", "source_files_changed": ["src/transform.c", "tests/test-save.c"], "target_files_changed": [], "source_commit_url": "https://github.com/hercules-team/augeas/commit/f5b4fc0c", "target_commit_url": "https://github.com/hercules-team/augeas/commit/f4f9fa61c0e0e5a10e19e0f48df31022e842dfcc", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/hercules-team/augeas", "target_repo": "https://github.com/hercules-team/augeas", "source_commit": "f5b4fc0c", "target_commit": "f4f9fa61c0e0e5a10e19e0f48df31022e842dfcc", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2011-3346_bonzini_qemu_bonzini_qemu_7285477a", "cve_id": "CVE-2011-3346", "patch_type": "same_repo", "projects": {"source": {"name": "bonzini/qemu", "repo": "https://github.com/bonzini/qemu", "language": "C"}, "target": {"name": "bonzini/qemu", "repo": "https://github.com/bonzini/qemu", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "103b40f51e4012b3b0ad20f615562a1806d7f49a"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "7285477ab11831b1cf56e45878a89170dd06d9b9"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2011-3346", "year": 2011, "bug_type": "memory overflow", "source_commit_date": "2011-09-16T14:40:03Z", "target_commit_date": "2011-09-16T14:40:04Z", "source_files_changed": ["hw/scsi-disk.c"], "target_files_changed": ["hw/scsi-disk.c"], "source_commit_url": "https://github.com/bonzini/qemu/commit/103b40f51e4012b3b0ad20f615562a1806d7f49a", "target_commit_url": "https://github.com/bonzini/qemu/commit/7285477ab11831b1cf56e45878a89170dd06d9b9", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/bonzini/qemu", "target_repo": "https://github.com/bonzini/qemu", "source_commit": "103b40f51e4012b3b0ad20f615562a1806d7f49a", "target_commit": "7285477ab11831b1cf56e45878a89170dd06d9b9", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2014-0749_adaptivecomputing_torque_adaptivecomputing_torque_3ed74926", "cve_id": "CVE-2014-0749", "patch_type": "same_repo", "projects": {"source": {"name": "adaptivecomputing/torque", "repo": "https://github.com/adaptivecomputing/torque", "language": "C"}, "target": {"name": "adaptivecomputing/torque", "repo": "https://github.com/adaptivecomputing/torque", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "b447fe5988bd6f226199dc6228fec8a65ecc2f1f"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "3ed749263abe3d69fa3626d142a5789dcb5a5684"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2014-0749", "year": 2014, "bug_type": "memory overflow", "source_commit_date": "2013-08-22T19:51:10Z", "target_commit_date": "2013-08-23T21:53:09Z", "source_files_changed": [], "target_files_changed": ["src/lib/Libdis/disrsi_.c"], "source_commit_url": "https://github.com/adaptivecomputing/torque/commit/b447fe5988bd6f226199dc6228fec8a65ecc2f1f", "target_commit_url": "https://github.com/adaptivecomputing/torque/commit/3ed749263abe3d69fa3626d142a5789dcb5a5684", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/adaptivecomputing/torque", "target_repo": "https://github.com/adaptivecomputing/torque", "source_commit": "b447fe5988bd6f226199dc6228fec8a65ecc2f1f", "target_commit": "3ed749263abe3d69fa3626d142a5789dcb5a5684", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2014-4501_luke-jr_bfgminer_c<PERSON><PERSON><PERSON>_cgminer_e1c50507", "cve_id": "CVE-2014-4501", "patch_type": "cross_repo", "projects": {"source": {"name": "luke-jr/bfgminer", "repo": "https://github.com/luke-jr/bfgminer", "language": "C"}, "target": {"name": "c<PERSON><PERSON><PERSON>/cgminer", "repo": "https://github.com/ckolivas/cgminer", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "c80ad8548251eb0e15329fc240c89070640c9d79"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "e1c5050734123973b99d181c45e74b2cbb00272e"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2014-4501", "year": 2014, "bug_type": "memory overflow", "source_commit_date": "2014-06-05T17:05:12Z", "target_commit_date": "2014-06-10T01:36:22Z", "source_files_changed": ["util.c"], "target_files_changed": ["util.c"], "source_commit_url": "https://github.com/luke-jr/bfgminer/commit/c80ad8548251eb0e15329fc240c89070640c9d79", "target_commit_url": "https://github.com/ckolivas/cgminer/commit/e1c5050734123973b99d181c45e74b2cbb00272e", "backporting_type": "cross_repo", "backporting_analysis": {"source_repo": "https://github.com/luke-jr/bfgminer", "target_repo": "https://github.com/ckolivas/cgminer", "source_commit": "c80ad8548251eb0e15329fc240c89070640c9d79", "target_commit": "e1c5050734123973b99d181c45e74b2cbb00272e", "analysis_result": "cross_repo"}}}, {"unified_id": "CVE-2014-4502_luke-jr_bfgminer_c<PERSON><PERSON><PERSON>_cgminer_e1c50507", "cve_id": "CVE-2014-4502", "patch_type": "cross_repo", "projects": {"source": {"name": "luke-jr/bfgminer", "repo": "https://github.com/luke-jr/bfgminer", "language": "C"}, "target": {"name": "c<PERSON><PERSON><PERSON>/cgminer", "repo": "https://github.com/ckolivas/cgminer", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "ff7f30129f15f7a2213f8ced0cd65c9a331493d9"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "e1c5050734123973b99d181c45e74b2cbb00272e"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2014-4502", "year": 2014, "bug_type": "memory overflow", "source_commit_date": "2014-06-05T17:10:43Z", "target_commit_date": "2014-06-10T01:36:22Z", "source_files_changed": ["util.c"], "target_files_changed": ["util.c"], "source_commit_url": "https://github.com/luke-jr/bfgminer/commit/ff7f30129f15f7a2213f8ced0cd65c9a331493d9", "target_commit_url": "https://github.com/ckolivas/cgminer/commit/e1c5050734123973b99d181c45e74b2cbb00272e", "backporting_type": "cross_repo", "backporting_analysis": {"source_repo": "https://github.com/luke-jr/bfgminer", "target_repo": "https://github.com/ckolivas/cgminer", "source_commit": "ff7f30129f15f7a2213f8ced0cd65c9a331493d9", "target_commit": "e1c5050734123973b99d181c45e74b2cbb00272e", "analysis_result": "cross_repo"}}}, {"unified_id": "CVE-2014-9273_libguestfs_hivex_libguestfs_hivex_4bbdf555", "cve_id": "CVE-2014-9273", "patch_type": "same_repo", "projects": {"source": {"name": "libguestfs/hivex", "repo": "https://github.com/libguestfs/hivex", "language": "C"}, "target": {"name": "libguestfs/hivex", "repo": "https://github.com/libguestfs/hivex", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "357f26fa64fd1d9ccac2331fe174a8ee9c607adb"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "4bbdf555f88baeae0fa804a369a81a83908bd705"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2014-9273", "year": 2014, "bug_type": "memory overflow", "source_commit_date": "2014-10-30T13:50:39Z", "target_commit_date": "2014-10-30T14:02:25Z", "source_files_changed": ["lib/handle.c"], "target_files_changed": ["lib/handle.c"], "source_commit_url": "https://github.com/libguestfs/hivex/commit/357f26fa64fd1d9ccac2331fe174a8ee9c607adb", "target_commit_url": "https://github.com/libguestfs/hivex/commit/4bbdf555f88baeae0fa804a369a81a83908bd705", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/libguestfs/hivex", "target_repo": "https://github.com/libguestfs/hivex", "source_commit": "357f26fa64fd1d9ccac2331fe174a8ee9c607adb", "target_commit": "4bbdf555f88baeae0fa804a369a81a83908bd705", "analysis_result": "same_version"}}}]