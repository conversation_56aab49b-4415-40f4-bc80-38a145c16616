[{"unified_id": "CVE-2021-27186_fluent_fluent-bit_fluent_fluent-bit_4deb051c", "cve_id": "CVE-2021-27186", "patch_type": "same_repo", "projects": {"source": {"name": "fluent/fluent-bit", "repo": "https://github.com/fluent/fluent-bit", "language": "C"}, "target": {"name": "fluent/fluent-bit", "repo": "https://github.com/fluent/fluent-bit", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "a5d1721c8e3e62c30eb05609ece152fa689dbc35"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "4deb051cb0277d32ab402da4f941f6e502518388"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-27186", "year": 2021, "bug_type": "NULL pointer dereference", "source_commit_date": "2021-02-09T15:15:37Z", "target_commit_date": "2021-02-09T19:57:42Z", "source_files_changed": [], "target_files_changed": [], "source_commit_url": "https://github.com/fluent/fluent-bit/commit/a5d1721c8e3e62c30eb05609ece152fa689dbc35", "target_commit_url": "https://github.com/fluent/fluent-bit/commit/4deb051cb0277d32ab402da4f941f6e502518388", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/fluent/fluent-bit", "target_repo": "https://github.com/fluent/fluent-bit", "source_commit": "a5d1721c8e3e62c30eb05609ece152fa689dbc35", "target_commit": "4deb051cb0277d32ab402da4f941f6e502518388", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2021-21323_brave_brave-core_brave_brave-core_12fe321e", "cve_id": "CVE-2021-21323", "patch_type": "same_repo", "projects": {"source": {"name": "brave/brave-core", "repo": "https://github.com/brave/brave-core", "language": "C"}, "target": {"name": "brave/brave-core", "repo": "https://github.com/brave/brave-core", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "a148c43124434ca5440c9fef1a99b9b712c966a0"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "12fe321eaad8acc1cbd1d70b4128f687777bcf15"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-21323", "year": 2021, "bug_type": "information leakage", "source_commit_date": "2021-02-03T22:09:56Z", "target_commit_date": "2021-02-04T21:32:48Z", "source_files_changed": [], "target_files_changed": ["browser/net/brave_ad_block_tp_network_delegate_helper.cc"], "source_commit_url": "https://github.com/brave/brave-core/commit/a148c43124434ca5440c9fef1a99b9b712c966a0", "target_commit_url": "https://github.com/brave/brave-core/commit/12fe321eaad8acc1cbd1d70b4128f687777bcf15", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/brave/brave-core", "target_repo": "https://github.com/brave/brave-core", "source_commit": "a148c43124434ca5440c9fef1a99b9b712c966a0", "target_commit": "12fe321eaad8acc1cbd1d70b4128f687777bcf15", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2020-35518_389ds_389-ds-base_389ds_389-ds-base_b6aae4d8", "cve_id": "CVE-2020-35518", "patch_type": "same_repo", "projects": {"source": {"name": "389ds/389-ds-base", "repo": "https://github.com/389ds/389-ds-base", "language": "C"}, "target": {"name": "389ds/389-ds-base", "repo": "https://github.com/389ds/389-ds-base", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "cc0f69283abc082488824702dae485b8eae938bc"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "b6aae4d8e7c8a6ddd21646f94fef1bf7f22c3f32"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2020-35518", "year": 2020, "bug_type": "information leakage", "source_commit_date": "2020-12-16T15:30:28Z", "target_commit_date": "2021-02-09T19:02:59Z", "source_files_changed": ["ldap/servers/slapd/back-ldbm/ldbm_config.c", "ldap/servers/slapd/result.c"], "target_files_changed": ["ldap/servers/slapd/back-ldbm/ldbm_bind.c", "ldap/servers/slapd/dse.c"], "source_commit_url": "https://github.com/389ds/389-ds-base/commit/cc0f69283abc082488824702dae485b8eae938bc", "target_commit_url": "https://github.com/389ds/389-ds-base/commit/b6aae4d8e7c8a6ddd21646f94fef1bf7f22c3f32", "backporting_type": "cross_branch", "backporting_analysis": {"source_repo": "https://github.com/389ds/389-ds-base", "target_repo": "https://github.com/389ds/389-ds-base", "source_commit": "cc0f69283abc082488824702dae485b8eae938bc", "target_commit": "b6aae4d8e7c8a6ddd21646f94fef1bf7f22c3f32", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2021-27815_libexif_exif_libexif_exif_eb84b0e3", "cve_id": "CVE-2021-27815", "patch_type": "same_repo", "projects": {"source": {"name": "libexif/exif", "repo": "https://github.com/libexif/exif", "language": "C"}, "target": {"name": "libexif/exif", "repo": "https://github.com/libexif/exif", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "f6334d9d32437ef13dc902f0a88a2be0063d9d1c"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "eb84b0e3c5f2a86013b6fcfb800d187896a648fa"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-27815", "year": 2021, "bug_type": "NULL pointer dereference", "source_commit_date": "2021-02-25T07:31:53Z", "target_commit_date": "2021-02-25T08:45:36Z", "source_files_changed": ["exif/actions.c"], "target_files_changed": ["exif/actions.c"], "source_commit_url": "https://github.com/libexif/exif/commit/f6334d9d32437ef13dc902f0a88a2be0063d9d1c", "target_commit_url": "https://github.com/libexif/exif/commit/eb84b0e3c5f2a86013b6fcfb800d187896a648fa", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/libexif/exif", "target_repo": "https://github.com/libexif/exif", "source_commit": "f6334d9d32437ef13dc902f0a88a2be0063d9d1c", "target_commit": "eb84b0e3c5f2a86013b6fcfb800d187896a648fa", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2021-32613_radareorg_radare2_radareorg_radare2_5e16e2d1", "cve_id": "CVE-2021-32613", "patch_type": "same_repo", "projects": {"source": {"name": "radareorg/radare2", "repo": "https://github.com/radareorg/radare2", "language": "C"}, "target": {"name": "radareorg/radare2", "repo": "https://github.com/radareorg/radare2", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "a07dedb804a82bc01c07072861942dd80c6b6d62"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "5e16e2d1c9fe245e4c17005d779fde91ec0b9c05"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-32613", "year": 2021, "bug_type": "use-after-free", "source_commit_date": "2021-05-07T16:44:49Z", "target_commit_date": "2021-05-07T19:09:59Z", "source_files_changed": ["libr/bin/p/bin_mach0.c"], "target_files_changed": ["libr/bin/format/pyc/marshal.c"], "source_commit_url": "https://github.com/radareorg/radare2/commit/a07dedb804a82bc01c07072861942dd80c6b6d62", "target_commit_url": "https://github.com/radareorg/radare2/commit/5e16e2d1c9fe245e4c17005d779fde91ec0b9c05", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/radareorg/radare2", "target_repo": "https://github.com/radareorg/radare2", "source_commit": "a07dedb804a82bc01c07072861942dd80c6b6d62", "target_commit": "5e16e2d1c9fe245e4c17005d779fde91ec0b9c05", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2021-29609_tensorflow_tensorflow_tensorflow_tensorflow_41727ff0", "cve_id": "CVE-2021-29609", "patch_type": "same_repo", "projects": {"source": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}, "target": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "6fd02f44810754ae7481838b6a67c5df7f909ca3"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "41727ff06111117bdf86b37db198217fd7a143cc"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-29609", "year": 2021, "bug_type": "NULL pointer dereference", "source_commit_date": "2021-04-26T23:40:49Z", "target_commit_date": "2021-05-11T22:41:51Z", "source_files_changed": ["tensorflow/core/kernels/sparse_add_op.cc"], "target_files_changed": ["tensorflow/core/kernels/sparse_add_op.cc"], "source_commit_url": "https://github.com/tensorflow/tensorflow/commit/6fd02f44810754ae7481838b6a67c5df7f909ca3", "target_commit_url": "https://github.com/tensorflow/tensorflow/commit/41727ff06111117bdf86b37db198217fd7a143cc", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "source_commit": "6fd02f44810754ae7481838b6a67c5df7f909ca3", "target_commit": "41727ff06111117bdf86b37db198217fd7a143cc", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2021-36980_openvswitch_ovs_openvswitch_ovs_9926637a", "cve_id": "CVE-2021-36980", "patch_type": "same_repo", "projects": {"source": {"name": "openvswitch/ovs", "repo": "https://github.com/openvswitch/ovs", "language": "C"}, "target": {"name": "openvswitch/ovs", "repo": "https://github.com/openvswitch/ovs", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "38744b1bcb022c611712527f039722115300f58f"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "9926637a80d0d243dbf9c49761046895e9d1a8e2"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-36980", "year": 2021, "bug_type": "use-after-free", "source_commit_date": "2021-02-16T22:27:30Z", "target_commit_date": "2021-02-16T22:27:30Z", "source_files_changed": ["lib/ofp-actions.c"], "target_files_changed": ["lib/ofp-actions.c"], "source_commit_url": "https://github.com/openvswitch/ovs/commit/38744b1bcb022c611712527f039722115300f58f", "target_commit_url": "https://github.com/openvswitch/ovs/commit/9926637a80d0d243dbf9c49761046895e9d1a8e2", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/openvswitch/ovs", "target_repo": "https://github.com/openvswitch/ovs", "source_commit": "38744b1bcb022c611712527f039722115300f58f", "target_commit": "9926637a80d0d243dbf9c49761046895e9d1a8e2", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2021-36692_libjxl_libjxl_libjxl_libjxl_7dfa400d", "cve_id": "CVE-2021-36692", "patch_type": "same_repo", "projects": {"source": {"name": "libjxl/libjxl", "repo": "https://github.com/libjxl/libjxl", "language": "C"}, "target": {"name": "libjxl/libjxl", "repo": "https://github.com/libjxl/libjxl", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "0ec6526b2d9bba861f70c3c72ad6e543ec38ba38"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "7dfa400ded53919d986c5d3d23446a09e0cf481b"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-36692", "year": 2021, "bug_type": "divide by zero", "source_commit_date": "2021-07-10T04:22:21Z", "target_commit_date": "2021-07-15T10:34:08Z", "source_files_changed": [], "target_files_changed": ["lib/extras/codec_apng.cc"], "source_commit_url": "https://github.com/libjxl/libjxl/commit/0ec6526b2d9bba861f70c3c72ad6e543ec38ba38", "target_commit_url": "https://github.com/libjxl/libjxl/commit/7dfa400ded53919d986c5d3d23446a09e0cf481b", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/libjxl/libjxl", "target_repo": "https://github.com/libjxl/libjxl", "source_commit": "0ec6526b2d9bba861f70c3c72ad6e543ec38ba38", "target_commit": "7dfa400ded53919d986c5d3d23446a09e0cf481b", "analysis_result": "same_version"}}}, {"unified_id": "CVE-2021-41195_tensorflow_tensorflow_tensorflow_tensorflow_e9c81c1e", "cve_id": "CVE-2021-41195", "patch_type": "same_repo", "projects": {"source": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}, "target": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "ab5a569b93ae1f7a3f3d1147fb347a2e7f65b5c3"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "e9c81c1e1a9cd8dd31f4e83676cab61b60658429"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-41195", "year": 2021, "bug_type": "integer overflow", "source_commit_date": "2021-08-28T17:15:23Z", "target_commit_date": "2021-10-28T00:36:34Z", "source_files_changed": [], "target_files_changed": ["tensorflow/core/kernels/segment_reduction_ops_impl.h"], "source_commit_url": "https://github.com/tensorflow/tensorflow/commit/ab5a569b93ae1f7a3f3d1147fb347a2e7f65b5c3", "target_commit_url": "https://github.com/tensorflow/tensorflow/commit/e9c81c1e1a9cd8dd31f4e83676cab61b60658429", "backporting_type": "cross_branch", "backporting_analysis": {"source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "source_commit": "ab5a569b93ae1f7a3f3d1147fb347a2e7f65b5c3", "target_commit": "e9c81c1e1a9cd8dd31f4e83676cab61b60658429", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2021-41197_tensorflow_tensorflow_tensorflow_tensorflow_a871989d", "cve_id": "CVE-2021-41197", "patch_type": "same_repo", "projects": {"source": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}, "target": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "7c1692bd417eb4f9b33ead749a41166d6080af85"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "a871989d7b6c18cdebf2fb4f0e5c5b62fbc19edf"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-41197", "year": 2021, "bug_type": "integer overflow", "source_commit_date": "2021-08-31T23:23:54Z", "target_commit_date": "2021-10-06T15:19:47Z", "source_files_changed": ["tensorflow/core/kernels/image/crop_and_resize_op.cc"], "target_files_changed": ["tensorflow/core/kernels/image/attention_ops.cc"], "source_commit_url": "https://github.com/tensorflow/tensorflow/commit/7c1692bd417eb4f9b33ead749a41166d6080af85", "target_commit_url": "https://github.com/tensorflow/tensorflow/commit/a871989d7b6c18cdebf2fb4f0e5c5b62fbc19edf", "backporting_type": "cross_branch", "backporting_analysis": {"source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "source_commit": "7c1692bd417eb4f9b33ead749a41166d6080af85", "target_commit": "a871989d7b6c18cdebf2fb4f0e5c5b62fbc19edf", "analysis_result": "cross_branch"}}}, {"unified_id": "CVE-2021-41203_tensorflow_tensorflow_tensorflow_tensorflow_368af875", "cve_id": "CVE-2021-41203", "patch_type": "same_repo", "projects": {"source": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}, "target": {"name": "tensorflow/tensorflow", "repo": "https://github.com/tensorflow/tensorflow", "language": "C"}}, "versions": {"source": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "abcced051cb1bd8fb05046ac3b6023a7ebcc4578"}}, "target": {"vulnerable": {"version": "unknown", "commit": "unknown"}, "patched": {"version": "unknown", "commit": "368af875869a204b4ac552b9ddda59f6a46a56ec"}}}, "source_dataset": "CustomEquivalentPatchCrawler", "metadata": {"cve_id": "CVE-2021-41203", "year": 2021, "bug_type": "integer overflow", "source_commit_date": "2021-08-24T17:31:40Z", "target_commit_date": "2021-08-25T20:45:01Z", "source_files_changed": ["tensorflow/core/framework/tensor.cc", "tensorflow/core/framework/tensor.h", "tensorflow/core/util/tensor_slice_reader.cc", "tensorflow/core/util/tensor_slice_reader_test.cc"], "target_files_changed": ["tensorflow/core/util/saved_tensor_slice_util.h", "tensorflow/core/util/tensor_slice_reader.h", "tensorflow/core/util/tensor_slice_reader_test.cc"], "source_commit_url": "https://github.com/tensorflow/tensorflow/commit/abcced051cb1bd8fb05046ac3b6023a7ebcc4578", "target_commit_url": "https://github.com/tensorflow/tensorflow/commit/368af875869a204b4ac552b9ddda59f6a46a56ec", "backporting_type": "same_version", "backporting_analysis": {"source_repo": "https://github.com/tensorflow/tensorflow", "target_repo": "https://github.com/tensorflow/tensorflow", "source_commit": "abcced051cb1bd8fb05046ac3b6023a7ebcc4578", "target_commit": "368af875869a204b4ac552b9ddda59f6a46a56ec", "analysis_result": "same_version"}}}]