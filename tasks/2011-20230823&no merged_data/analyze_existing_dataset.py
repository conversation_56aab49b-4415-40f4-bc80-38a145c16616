#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
分析现有的merged_dataset.json，提取CVE编号、仓库和CWE类型信息
用于确保新数据集不重复
"""

import json
import sys
import os
from collections import defaultdict, Counter

def analyze_merged_dataset():
    """分析merged_dataset.json文件"""
    dataset_file = "/home/<USER>/2011-20230823&no merged_data/data/merged_dataset.json"
    
    print("正在分析merged_dataset.json...")
    
    with open(dataset_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    print(f"总记录数: {len(data)}")
    
    # 提取CVE编号
    existing_cves = set()
    existing_repos = set()
    cwe_types = set()
    years = []
    
    for record in data:
        cve_id = record.get('cve_id', '')
        if cve_id:
            existing_cves.add(cve_id)
            # 提取年份
            if cve_id.startswith('CVE-'):
                try:
                    year = int(cve_id.split('-')[1])
                    years.append(year)
                except:
                    pass
        
        # 提取仓库信息
        projects = record.get('projects', {})
        source_repo = projects.get('source', {}).get('repo', '')
        target_repo = projects.get('target', {}).get('repo', '')
        
        if source_repo:
            existing_repos.add(source_repo)
        if target_repo:
            existing_repos.add(target_repo)
        
        # 提取CWE类型（从metadata中）
        metadata = record.get('metadata', {})
        bug_type = metadata.get('bug_type', '')
        if bug_type:
            cwe_types.add(bug_type)
    
    # 统计信息
    year_dist = Counter(years)
    
    print(f"\n=== 现有数据集分析结果 ===")
    print(f"唯一CVE数量: {len(existing_cves)}")
    print(f"涉及仓库数量: {len(existing_repos)}")
    print(f"CWE类型数量: {len(cwe_types)}")
    
    print(f"\n=== 年份分布 ===")
    for year, count in sorted(year_dist.items()):
        print(f"{year}: {count} 个CVE")
    
    print(f"\n=== 前20个仓库 ===")
    repo_counter = Counter()
    for record in data:
        projects = record.get('projects', {})
        source_repo = projects.get('source', {}).get('repo', '')
        target_repo = projects.get('target', {}).get('repo', '')
        if source_repo:
            repo_counter[source_repo] += 1
        if target_repo:
            repo_counter[target_repo] += 1
    
    for repo, count in repo_counter.most_common(20):
        print(f"{repo}: {count}")
    
    print(f"\n=== CWE类型 ===")
    for cwe_type in sorted(cwe_types):
        print(f"- {cwe_type}")
    
    # 保存分析结果
    analysis_result = {
        'existing_cves': list(existing_cves),
        'existing_repos': list(existing_repos),
        'cwe_types': list(cwe_types),
        'year_distribution': dict(year_dist),
        'total_records': len(data),
        'date_range': {
            'min_year': min(years) if years else None,
            'max_year': max(years) if years else None
        }
    }
    
    output_file = "/home/<USER>/2011-20230823&no merged_data/data/existing_dataset_analysis.json"
    with open(output_file, 'w', encoding='utf-8') as f:
        json.dump(analysis_result, f, indent=2, ensure_ascii=False)
    
    print(f"\n分析结果已保存到: {output_file}")
    
    return analysis_result

if __name__ == "__main__":
    analyze_merged_dataset()
