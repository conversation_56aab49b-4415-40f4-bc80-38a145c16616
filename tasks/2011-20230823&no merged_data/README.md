# CVE等价补丁数据收集系统

## 项目概述

本项目实现了一个完整的CVE等价补丁数据收集系统，用于收集2011年至2023年8月23日期间的CVE等价补丁对，满足与现有merged_dataset.json不重复的要求。

## 需求满足情况

### ✅ 已完成的需求

1. **数据格式一致性** - 完全按照merged_dataset.json的格式设计
2. **时间范围控制** - 支持2011年至2023年8月23日的数据收集
3. **去重机制** - 避免与现有数据集的CVE编号和仓库重复
4. **CWE类型匹配** - 使用与现有数据集相同的CWE类型
5. **C/C++项目过滤** - 使用GitHub API验证项目语言
6. **Mystique标准** - 实现了按时间戳排序选择最早和最新commit的逻辑
7. **Backporting类型分析** - 支持cross_version、cross_repo、cross_branch分类

## 系统架构

```
/home/<USER>/2011-20230823&no merged_data/
├── src/                          # 源代码目录
│   ├── main_collector.py         # 主数据收集器
│   ├── equivalent_patch_collector.py  # 等价补丁收集器
│   └── backporting_analyzer.py   # Backporting类型分析器
├── data/                         # 数据目录
│   ├── merged_dataset.json       # 现有数据集
│   └── existing_dataset_analysis.json  # 现有数据集分析结果
├── output/                       # 输出目录
│   ├── sample_dataset.json       # 示例数据集
│   └── patches_YYYY.json         # 按年份输出的补丁对
├── logs/                         # 日志目录
├── test_*.py                     # 测试脚本
└── README.md                     # 项目文档
```

## 核心组件

### 1. 主数据收集器 (main_collector.py)

- **功能**: 协调整个数据收集流程
- **特性**:
  - 从NVD API获取CVE数据
  - 按年份分批处理
  - 集成等价补丁收集和backporting分析
  - 支持断点续传

### 2. 等价补丁收集器 (equivalent_patch_collector.py)

- **功能**: 实现mystique标准的等价补丁筛选
- **特性**:
  - GitHub API集成，验证项目语言
  - C/C++文件过滤
  - 按时间戳排序选择最早和最新commit
  - 支持commit和pull request URL解析

### 3. Backporting类型分析器 (backporting_analyzer.py)

- **功能**: 分析补丁对的backporting类型
- **特性**:
  - cross_repo: 不同仓库
  - cross_version: 同仓库不同版本（基于时间差判断）
  - cross_branch: 同仓库不同分支
  - 智能缓存机制

## 数据格式

输出的数据完全符合需求中指定的格式：

```json
{
  "unified_id": "{cve_id}_{source_project}_{target_project}_{target_version_hash}",
  "cve_id": "CVE-2018-1118",
  "patch_type": "cross_repo|same_repo",
  "projects": {
    "source": {
      "name": "Linux Kernel",
      "repo": "https://github.com/torvalds/linux",
      "language": "C"
    },
    "target": {
      "name": "Linux Kernel", 
      "repo": "https://github.com/torvalds/linux",
      "language": "C"
    }
  },
  "versions": {
    "source": {
      "vulnerable": {"version": "unknown", "commit": "unknown"},
      "patched": {"version": "unknown", "commit": "commit_hash"}
    },
    "target": {
      "vulnerable": {"version": "unknown", "commit": "unknown"},
      "patched": {"version": "unknown", "commit": "commit_hash"}
    }
  },
  "source_dataset": "CustomEquivalentPatchCrawler",
  "metadata": {
    "cve_id": "CVE-2018-1118",
    "year": 2018,
    "bug_type": "use-after-free",
    "source_commit_date": "2023-01-15T10:30:00Z",
    "target_commit_date": "2023-02-20T14:45:00Z",
    "source_files_changed": ["file1.c", "file2.h"],
    "target_files_changed": ["file1.c"],
    "source_commit_url": "https://github.com/...",
    "target_commit_url": "https://github.com/...",
    "backporting_type": "cross_version"
  }
}
```

## 现有数据集分析结果

通过分析merged_dataset.json，我们了解到：

- **总记录数**: 5,093个补丁对
- **唯一CVE数**: 3,821个
- **涉及仓库数**: 27个
- **CWE类型数**: 11种

### 主要仓库分布
1. torvalds/linux: 7,988个补丁
2. wireshark/wireshark: 702个补丁
3. FFmpeg/FFmpeg: 538个补丁
4. qemu/qemu: 258个补丁
5. openssl/openssl: 240个补丁

### CWE类型
- NULL pointer dereference
- denial of service
- divide by zero
- heap overflow
- information leakage
- integer overflow
- memory overflow
- privilege escalation
- random number weakness
- stack overflow
- use-after-free

## 使用方法

### 1. 环境准备

```bash
# 安装依赖
pip install requests

# 配置API密钥
# 编辑 /home/<USER>/src/data_collection/config/sources.json
# 添加GitHub token和NVD API key
```

### 2. 运行数据收集

```bash
# 完整数据收集
python3 src/main_collector.py

# 小规模测试
python3 test_small_collection.py

# 使用已知CVE测试
python3 test_with_known_cves.py
```

### 3. 查看结果

```bash
# 查看输出文件
ls output/

# 查看日志
ls logs/
```

## 技术特性

### 1. 智能去重
- CVE编号去重
- 仓库URL去重
- 确保与现有数据集无重复

### 2. 语言检测
- GitHub API语言统计
- 基于文件扩展名的C/C++文件过滤
- 项目名称启发式判断

### 3. Mystique标准实现
- 按时间戳排序commits
- 选择最早和最新的commit作为补丁对
- 过滤单一补丁的CVE

### 4. Backporting类型分析
- 基于仓库差异判断cross_repo
- 基于时间差推断cross_version
- 支持cross_branch检测

### 5. 错误处理和日志
- 完整的日志记录
- API限制处理
- 网络错误重试机制

## 示例输出

项目已生成示例数据集 `output/sample_dataset.json`，包含：
- same_repo类型的补丁对
- cross_repo类型的补丁对
- 完整的metadata信息
- backporting类型分析结果

## 下一步工作

1. **API配置**: 配置有效的GitHub API token和NVD API key
2. **网络环境**: 确保网络连接稳定，可访问GitHub和NVD API
3. **大规模运行**: 执行完整的2011-2023年数据收集
4. **结果验证**: 验证收集到的数据质量和格式正确性
5. **性能优化**: 根据实际运行情况优化API调用频率和缓存策略

## 项目状态

- ✅ 系统架构设计完成
- ✅ 核心功能实现完成
- ✅ 数据格式验证通过
- ✅ 示例数据生成完成
- ⏳ 等待API配置和网络环境
- ⏳ 大规模数据收集待执行

## 联系信息

如有问题或需要进一步的功能扩展，请参考代码注释或查看日志文件。
