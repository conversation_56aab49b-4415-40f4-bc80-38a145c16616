{"basic": {"total_patches": 117, "unique_cves": 117, "patch_type_dist": {"same_repo": 97, "cross_repo": 20}, "year_dist": {"2012": 1, "2013": 1, "2011": 1, "2014": 4, "2015": 4, "2016": 13, "2017": 7, "2018": 11, "2019": 10, "2020": 16, "2021": 21, "2022": 22, "2023": 6}}, "cwe_dist": {"memory overflow": 28, "privilege escalation": 2, "use-after-free": 12, "integer overflow": 28, "information leakage": 9, "NULL pointer dereference": 22, "denial of service": 9, "divide by zero": 3, "heap overflow": 2, "stack overflow": 2}, "repo_dist": {"https://github.com/glensc/file": 2, "https://github.com/hercules-team/augeas": 2, "https://github.com/bonzini/qemu": 2, "https://github.com/adaptivecomputing/torque": 2, "https://github.com/luke-jr/bfgminer": 2, "https://github.com/ckolivas/cgminer": 2, "https://github.com/libguestfs/hivex": 2, "https://github.com/bblanchon/ArduinoJson": 2, "https://github.com/git/git": 2, "https://github.com/python-pillow/Pillow": 4, "https://github.com/googlei18n/sfntly": 2, "https://github.com/perl5-dbi/DBD-mysql": 2, "https://github.com/haakonnessjoen/MAC-Telnet": 2, "https://github.com/libgd/libgd": 4, "https://github.com/php/php-src": 2, "https://github.com/vadz/libtiff": 2, "https://github.com/systemd/systemd": 6, "https://github.com/inverse-inc/sogo": 2, "https://github.com/libgit2/libgit2": 8, "https://github.com/erikd/libsndfile": 2, "https://github.com/cesanta/mongoose-os": 1, "https://github.com/cesanta/mongoose": 1, "https://github.com/RIOT-OS/RIOT": 6, "https://github.com/pgbouncer/pgbouncer": 2, "https://github.com/zherczeg/jerryscript": 1, "https://github.com/jerryscript-project/jerryscript": 1, "https://github.com/file/file": 2, "https://github.com/zfsonlinux/zfs": 1, "https://github.com/FransUrbo/zfs": 1, "https://github.com/radare/radare2": 2, "https://github.com/varnishcache/varnish-cache": 2, "https://github.com/antirez/redis": 2, "https://github.com/fatcerberus/minisphere": 2, "https://github.com/mdadams/jasper": 2, "https://github.com/apache/trafficserver": 2, "https://github.com/viabtc/viabtc_exchange_server": 6, "https://github.com/acassen/keepalived": 2, "https://github.com/jjanku/podofo": 1, "https://github.com/mksdev/podofo": 1, "https://github.com/miniupnp/miniupnp": 4, "https://github.com/netdata/netdata": 2, "https://github.com/ImageMagick/ImageMagick": 10, "https://github.com/ImageMagick/ImageMagick6": 10, "https://github.com/fontforge/fontforge": 2, "https://github.com/unicode-org/icu": 2, "https://github.com/gpac/gpac": 2, "https://github.com/squid-cache/squid": 2, "https://github.com/zephyrproject-rtos/zephyr": 8, "https://github.com/znc/znc": 2, "https://github.com/nghttp2/nghttp2": 2, "https://github.com/mjurczak/mbed-coap": 1, "https://github.com/ARMmbed/mbed-coap": 1, "https://github.com/kelektiv/node.bcrypt.js": 2, "https://github.com/lua/lua": 2, "https://github.com/inspircd/inspircd": 4, "https://github.com/zeromq/libzmq": 2, "https://github.com/KDE/kdeconnect-kde": 2, "https://github.com/fluent/fluent-bit": 2, "https://github.com/brave/brave-core": 6, "https://github.com/389ds/389-ds-base": 2, "https://github.com/libexif/exif": 2, "https://github.com/radareorg/radare2": 2, "https://github.com/tensorflow/tensorflow": 22, "https://github.com/openvswitch/ovs": 2, "https://github.com/libjxl/libjxl": 2, "https://github.com/ruven/iipsrv": 2, "https://github.com/latchset/tang": 2, "https://github.com/OpenSC/OpenSC": 4, "https://github.com/unicorn-engine/unicorn": 2, "https://github.com/vim/vim": 2, "https://github.com/AcademySoftwareFoundation/openexr": 4, "https://github.com/opencryptoki/opencryptoki": 2, "https://github.com/openscad/openscad": 2, "https://github.com/GreycLab/CImg": 2, "https://github.com/libexpat/libexpat": 4, "https://github.com/sparklemotion/nokogiri": 2, "https://github.com/EliasOenal/multimon-ng": 2, "https://github.com/moby/hyperkit": 4, "https://github.com/PX4/PX4-Autopilot": 1, "https://github.com/apache/incubator-nuttx": 1, "https://github.com/rizinorg/rizin": 2, "https://github.com/protobuf-c/protobuf-c": 2, "https://github.com/torvalds/linux": 2}, "backporting_dist": {"same_version": 83, "cross_repo": 20, "cross_branch": 14}, "temporal": {"yearly_patches": {"2012": 1, "2013": 1, "2011": 1, "2014": 4, "2015": 4, "2016": 13, "2017": 7, "2018": 11, "2019": 10, "2020": 16, "2021": 21, "2022": 22, "2023": 6}, "yearly_cves": {"2012": 1, "2013": 1, "2011": 1, "2014": 4, "2015": 4, "2016": 13, "2017": 7, "2018": 11, "2019": 10, "2020": 16, "2021": 21, "2022": 22, "2023": 6}}}