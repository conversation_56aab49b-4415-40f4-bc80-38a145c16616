#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
收集到的CVE补丁数据分析脚本
分析CWE类型分布、仓库分布、时间分布等
"""

import json
import os
import glob
from collections import defaultdict, Counter
from datetime import datetime
import matplotlib.pyplot as plt

def load_all_patch_data():
    """加载所有年份的补丁数据"""
    output_dir = "/home/<USER>/2011-20230823&no merged_data/output"
    all_patches = []
    
    # 获取所有补丁文件
    patch_files = glob.glob(os.path.join(output_dir, "patches_*.json"))
    patch_files.sort()
    
    print(f"找到 {len(patch_files)} 个补丁文件")
    
    for file_path in patch_files:
        year = os.path.basename(file_path).replace('patches_', '').replace('.json', '')
        print(f"加载 {year} 年数据...")
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                patches = data if isinstance(data, list) else []
                all_patches.extend(patches)
                print(f"  {year} 年: {len(patches)} 个补丁对")
        except Exception as e:
            print(f"  加载 {file_path} 失败: {str(e)}")
    
    print(f"总计加载 {len(all_patches)} 个补丁对")
    return all_patches

def analyze_basic_statistics(patches):
    """分析基础统计信息"""
    print("\n=== 基础统计信息 ===")
    
    total_patches = len(patches)
    print(f"总补丁对数: {total_patches}")
    
    # CVE统计
    cve_ids = [patch['cve_id'] for patch in patches]
    unique_cves = len(set(cve_ids))
    print(f"唯一CVE数: {unique_cves}")
    
    # 补丁类型统计
    patch_types = [patch['patch_type'] for patch in patches]
    patch_type_dist = Counter(patch_types)
    print(f"补丁类型分布:")
    for patch_type, count in patch_type_dist.items():
        percentage = (count / total_patches) * 100
        print(f"  {patch_type}: {count} ({percentage:.1f}%)")
    
    # 年份分布
    years = []
    for patch in patches:
        year = patch.get('metadata', {}).get('year')
        if year:
            years.append(year)
    
    year_dist = Counter(years)
    print(f"年份分布:")
    for year in sorted(year_dist.keys()):
        count = year_dist[year]
        percentage = (count / len(years)) * 100 if years else 0
        print(f"  {year}: {count} ({percentage:.1f}%)")
    
    return {
        'total_patches': total_patches,
        'unique_cves': unique_cves,
        'patch_type_dist': dict(patch_type_dist),
        'year_dist': dict(year_dist)
    }

def analyze_cwe_distribution(patches):
    """分析CWE类型分布"""
    print("\n=== CWE类型分布分析 ===")
    
    bug_types = []
    for patch in patches:
        bug_type = patch.get('metadata', {}).get('bug_type', 'unknown')
        if bug_type and bug_type != 'unknown':
            bug_types.append(bug_type)
    
    cwe_dist = Counter(bug_types)
    print(f"CWE类型统计 (共 {len(cwe_dist)} 种类型):")
    
    for i, (cwe_type, count) in enumerate(cwe_dist.most_common(20)):
        percentage = (count / len(bug_types)) * 100 if bug_types else 0
        print(f"  {i+1:2d}. {cwe_type}: {count} ({percentage:.1f}%)")
    
    return dict(cwe_dist)

def analyze_repository_distribution(patches):
    """分析仓库分布"""
    print("\n=== 仓库分布分析 ===")
    
    all_repos = []
    for patch in patches:
        source_repo = patch.get('projects', {}).get('source', {}).get('repo', '')
        target_repo = patch.get('projects', {}).get('target', {}).get('repo', '')
        
        if source_repo:
            all_repos.append(source_repo)
        if target_repo:
            all_repos.append(target_repo)
    
    repo_dist = Counter(all_repos)
    unique_repos = len(repo_dist)
    
    print(f"涉及仓库总数: {unique_repos}")
    print(f"仓库引用总次数: {len(all_repos)}")
    print(f"前20个仓库分布:")
    
    for i, (repo, count) in enumerate(repo_dist.most_common(20)):
        percentage = (count / len(all_repos)) * 100
        repo_name = repo.replace('https://github.com/', '')
        print(f"  {i+1:2d}. {repo_name}: {count} ({percentage:.1f}%)")
    
    return dict(repo_dist)

def analyze_backporting_types(patches):
    """分析backporting类型分布"""
    print("\n=== Backporting类型分布 ===")

    backporting_types = []
    for patch in patches:
        # 检查两个可能的位置
        bp_type = patch.get('metadata', {}).get('backporting_type', 'unknown')
        if bp_type == 'unknown':
            # 如果metadata中没有，检查backporting_analysis
            bp_analysis = patch.get('metadata', {}).get('backporting_analysis', {})
            bp_type = bp_analysis.get('analysis_result', 'unknown')

        backporting_types.append(bp_type)

    bp_dist = Counter(backporting_types)

    print(f"Backporting类型统计 (共 {len(bp_dist)} 种类型):")
    for bp_type, count in bp_dist.items():
        percentage = (count / len(backporting_types)) * 100
        print(f"  {bp_type}: {count} ({percentage:.1f}%)")

    return dict(bp_dist)

def analyze_temporal_patterns(patches):
    """分析时间模式"""
    print("\n=== 时间模式分析 ===")
    
    # 按年份统计补丁对数量
    yearly_patches = defaultdict(int)
    yearly_cves = defaultdict(set)
    
    for patch in patches:
        year = patch.get('metadata', {}).get('year')
        cve_id = patch.get('cve_id', '')
        
        if year:
            yearly_patches[year] += 1
            if cve_id:
                yearly_cves[year].add(cve_id)
    
    print("年度统计:")
    for year in sorted(yearly_patches.keys()):
        patch_count = yearly_patches[year]
        cve_count = len(yearly_cves[year])
        print(f"  {year}: {patch_count} 个补丁对, {cve_count} 个CVE")
    
    return {
        'yearly_patches': dict(yearly_patches),
        'yearly_cves': {year: len(cves) for year, cves in yearly_cves.items()}
    }

def create_visualizations(analysis_results):
    """创建可视化图表"""
    print("\n=== 生成可视化图表 ===")
    
    try:
        # 1. CWE类型分布图
        if 'cwe_dist' in analysis_results:
            plt.figure(figsize=(12, 8))
            cwe_items = sorted(analysis_results['cwe_dist'].items(), key=lambda x: x[1], reverse=True)[:15]
            if cwe_items:
                cwes, counts = zip(*cwe_items)
                
                plt.barh(range(len(cwes)), counts)
                plt.yticks(range(len(cwes)), [cwe.replace('_', ' ').title() for cwe in cwes])
                plt.xlabel('Count')
                plt.title('Top 15 CWE Types Distribution')
                plt.tight_layout()
                plt.savefig('/home/<USER>/2011-20230823&no merged_data/analysis_results/cwe_distribution.png', 
                           dpi=300, bbox_inches='tight')
                plt.close()
                print("  CWE分布图已保存")
        
        # 2. 仓库分布图
        if 'repo_dist' in analysis_results:
            plt.figure(figsize=(14, 10))
            repo_items = sorted(analysis_results['repo_dist'].items(), key=lambda x: x[1], reverse=True)[:20]
            if repo_items:
                repos, counts = zip(*repo_items)
                repo_names = [repo.replace('https://github.com/', '') for repo in repos]
                
                plt.barh(range(len(repo_names)), counts)
                plt.yticks(range(len(repo_names)), repo_names)
                plt.xlabel('Count')
                plt.title('Top 20 Repository Distribution')
                plt.tight_layout()
                plt.savefig('/home/<USER>/2011-20230823&no merged_data/analysis_results/repo_distribution.png', 
                           dpi=300, bbox_inches='tight')
                plt.close()
                print("  仓库分布图已保存")
        
        # 3. 年份分布图
        if 'yearly_patches' in analysis_results:
            plt.figure(figsize=(12, 6))
            years = sorted(analysis_results['yearly_patches'].keys())
            counts = [analysis_results['yearly_patches'][year] for year in years]
            
            plt.bar(years, counts)
            plt.xlabel('Year')
            plt.ylabel('Number of Patch Pairs')
            plt.title('Patch Pairs Distribution by Year')
            plt.xticks(rotation=45)
            plt.tight_layout()
            plt.savefig('/home/<USER>/2011-20230823&no merged_data/analysis_results/yearly_distribution.png', 
                       dpi=300, bbox_inches='tight')
            plt.close()
            print("  年份分布图已保存")
            
    except Exception as e:
        print(f"  生成图表时出错: {str(e)}")

def generate_report(patches, analysis_results):
    """生成分析报告"""
    print("\n=== 生成分析报告 ===")
    
    report = f"""# CVE等价补丁数据收集分析报告

## 数据收集概况

- **收集时间范围**: 2011年至2023年8月23日
- **总补丁对数**: {analysis_results['basic']['total_patches']}
- **唯一CVE数**: {analysis_results['basic']['unique_cves']}
- **涉及仓库数**: {len(analysis_results['repo_dist'])}

## 补丁类型分布

"""
    
    for patch_type, count in analysis_results['basic']['patch_type_dist'].items():
        percentage = (count / analysis_results['basic']['total_patches']) * 100
        report += f"- **{patch_type}**: {count} ({percentage:.1f}%)\n"
    
    report += f"""
## CWE类型分布 (前15)

"""
    
    cwe_items = sorted(analysis_results['cwe_dist'].items(), key=lambda x: x[1], reverse=True)[:15]
    for cwe_type, count in cwe_items:
        total_cwe = sum(analysis_results['cwe_dist'].values())
        percentage = (count / total_cwe) * 100 if total_cwe > 0 else 0
        report += f"- **{cwe_type}**: {count} ({percentage:.1f}%)\n"
    
    report += f"""
## 仓库分布 (前20)

"""
    
    repo_items = sorted(analysis_results['repo_dist'].items(), key=lambda x: x[1], reverse=True)[:20]
    total_repo_refs = sum(analysis_results['repo_dist'].values())
    for repo, count in repo_items:
        percentage = (count / total_repo_refs) * 100
        repo_name = repo.replace('https://github.com/', '')
        report += f"- **{repo_name}**: {count} ({percentage:.1f}%)\n"
    
    report += f"""
## Backporting类型分布

"""
    
    for bp_type, count in analysis_results['backporting_dist'].items():
        percentage = (count / analysis_results['basic']['total_patches']) * 100
        report += f"- **{bp_type}**: {count} ({percentage:.1f}%)\n"
    
    report += f"""
## 年度分布

"""
    
    for year in sorted(analysis_results['temporal']['yearly_patches'].keys()):
        patch_count = analysis_results['temporal']['yearly_patches'][year]
        cve_count = analysis_results['temporal']['yearly_cves'][year]
        report += f"- **{year}**: {patch_count} 个补丁对, {cve_count} 个CVE\n"
    
    # 保存报告
    report_file = "/home/<USER>/2011-20230823&no merged_data/analysis_results/collection_analysis_report.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"分析报告已保存到: {report_file}")

def main():
    """主函数"""
    print("开始分析收集到的CVE补丁数据...")
    
    # 创建分析结果目录
    os.makedirs("/home/<USER>/2011-20230823&no merged_data/analysis_results", exist_ok=True)
    
    # 加载数据
    patches = load_all_patch_data()
    
    if not patches:
        print("未找到补丁数据，退出分析")
        return
    
    # 执行各项分析
    analysis_results = {}
    
    analysis_results['basic'] = analyze_basic_statistics(patches)
    analysis_results['cwe_dist'] = analyze_cwe_distribution(patches)
    analysis_results['repo_dist'] = analyze_repository_distribution(patches)
    analysis_results['backporting_dist'] = analyze_backporting_types(patches)
    analysis_results['temporal'] = analyze_temporal_patterns(patches)
    
    # 生成可视化图表
    create_visualizations(analysis_results)
    
    # 生成报告
    generate_report(patches, analysis_results)
    
    # 保存详细分析数据
    detailed_data_file = "/home/<USER>/2011-20230823&no merged_data/analysis_results/detailed_analysis_data.json"
    with open(detailed_data_file, 'w', encoding='utf-8') as f:
        json.dump(analysis_results, f, indent=2, ensure_ascii=False)
    
    print(f"详细分析数据已保存到: {detailed_data_file}")
    print("\n分析完成！")

if __name__ == "__main__":
    main()
