2025-07-26 09:00:19.041 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='services.nvd.nist.gov', port=443): Max retries exceeded with url: /rest/json/cves/2.0/?cveId=CVE-2023-32079 (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1137)')))
2025-07-26 09:33:19.863 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-4569 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x75b80a419940>
2025-07-26 09:33:20.913 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-1995 补丁时出错: functools.partial(<function FigureCanvasAgg.print_png at 0x75b80a58e550>, orientation='portrait') did not call Figure.draw, so no renderer is available
2025-07-26 09:33:21.445 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-41361 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x75b80a574910>
2025-07-26 09:33:21.887 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-41359 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x75b80a4dbd00>
2025-07-26 09:33:22.317 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-41360 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x75b80a7858e0>
2025-07-26 09:33:23.126 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-39650 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x75b7a5dea790>
2025-07-26 09:37:49.226 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-34173 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x76e92b36d490>
2025-07-26 09:37:49.239 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-34022 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x76e92b36d490>
2025-07-26 09:37:49.240 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-3356 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x76e92b36d490>
2025-07-26 09:44:03.724 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-41751 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x76e92aaf3ac0>
2025-07-26 09:59:21.431 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='api.github.com', port=443): Max retries exceeded with url: /graphql (Caused by ProxyError('Unable to connect to proxy', NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x76e658694f70>: Failed to resolve 'host.docker.internal' ([Errno -3] Temporary failure in name resolution)")))
2025-07-26 09:59:21.431 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='api.github.com', port=443): Max retries exceeded with url: /graphql (Caused by ProxyError('Unable to connect to proxy', NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x76e658694f70>: Failed to resolve 'host.docker.internal' ([Errno -3] Temporary failure in name resolution)")))
2025-07-26 09:59:21.318 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='api.github.com', port=443): Max retries exceeded with url: /graphql (Caused by ProxyError('Unable to connect to proxy', NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x76e6586940a0>: Failed to resolve 'host.docker.internal' ([Errno -3] Temporary failure in name resolution)")))
2025-07-26 09:59:21.431 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='api.github.com', port=443): Max retries exceeded with url: /graphql (Caused by ProxyError('Unable to connect to proxy', NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x76e658694f70>: Failed to resolve 'host.docker.internal' ([Errno -3] Temporary failure in name resolution)")))
2025-07-26 09:59:21.431 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='api.github.com', port=443): Max retries exceeded with url: /graphql (Caused by ProxyError('Unable to connect to proxy', NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x76e658694f70>: Failed to resolve 'host.docker.internal' ([Errno -3] Temporary failure in name resolution)")))
2025-07-26 09:59:21.318 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='api.github.com', port=443): Max retries exceeded with url: /graphql (Caused by ProxyError('Unable to connect to proxy', NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x76e6586940a0>: Failed to resolve 'host.docker.internal' ([Errno -3] Temporary failure in name resolution)")))
2025-07-26 09:59:17.666 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='api.github.com', port=443): Max retries exceeded with url: /graphql (Caused by ProxyError('Unable to connect to proxy', NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x76e658694e80>: Failed to resolve 'host.docker.internal' ([Errno -3] Temporary failure in name resolution)")))
2025-07-26 09:59:21.318 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='api.github.com', port=443): Max retries exceeded with url: /graphql (Caused by ProxyError('Unable to connect to proxy', NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x76e6586940a0>: Failed to resolve 'host.docker.internal' ([Errno -3] Temporary failure in name resolution)")))
2025-07-26 09:59:21.318 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='api.github.com', port=443): Max retries exceeded with url: /graphql (Caused by ProxyError('Unable to connect to proxy', NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x76e6586940a0>: Failed to resolve 'host.docker.internal' ([Errno -3] Temporary failure in name resolution)")))
2025-07-26 09:59:29.300 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='api.github.com', port=443): Max retries exceeded with url: /graphql (Caused by ProxyError('Unable to connect to proxy', NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x76e658694040>: Failed to resolve 'host.docker.internal' ([Errno -3] Temporary failure in name resolution)")))
2025-07-26 09:59:17.666 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='api.github.com', port=443): Max retries exceeded with url: /graphql (Caused by ProxyError('Unable to connect to proxy', NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x76e658694e80>: Failed to resolve 'host.docker.internal' ([Errno -3] Temporary failure in name resolution)")))
2025-07-26 09:59:29.300 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='api.github.com', port=443): Max retries exceeded with url: /graphql (Caused by ProxyError('Unable to connect to proxy', NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x76e658694040>: Failed to resolve 'host.docker.internal' ([Errno -3] Temporary failure in name resolution)")))
2025-07-26 09:59:17.666 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='api.github.com', port=443): Max retries exceeded with url: /graphql (Caused by ProxyError('Unable to connect to proxy', NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x76e658694e80>: Failed to resolve 'host.docker.internal' ([Errno -3] Temporary failure in name resolution)")))
2025-07-26 09:59:29.300 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='api.github.com', port=443): Max retries exceeded with url: /graphql (Caused by ProxyError('Unable to connect to proxy', NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x76e658694040>: Failed to resolve 'host.docker.internal' ([Errno -3] Temporary failure in name resolution)")))
2025-07-26 09:59:17.666 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='api.github.com', port=443): Max retries exceeded with url: /graphql (Caused by ProxyError('Unable to connect to proxy', NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x76e658694e80>: Failed to resolve 'host.docker.internal' ([Errno -3] Temporary failure in name resolution)")))
2025-07-26 09:59:29.300 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='api.github.com', port=443): Max retries exceeded with url: /graphql (Caused by ProxyError('Unable to connect to proxy', NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x76e658694040>: Failed to resolve 'host.docker.internal' ([Errno -3] Temporary failure in name resolution)")))
2025-07-26 11:20:14.781 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-28187 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x72800e26b9d0>
2025-07-26 11:20:14.793 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-28208 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x72800e26b9d0>
2025-07-26 11:20:15.557 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-28209 补丁时出错: functools.partial(<function FigureCanvasAgg.print_png at 0x72800e219e50>, orientation='portrait') did not call Figure.draw, so no renderer is available
2025-07-26 11:20:15.557 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-28209 补丁时出错: functools.partial(<function FigureCanvasAgg.print_png at 0x72800e219e50>, orientation='portrait') did not call Figure.draw, so no renderer is available
2025-07-26 11:20:15.587 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2022-32920 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x72800db67bb0>
2025-07-26 11:20:15.587 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2022-32920 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x72800db67bb0>
2025-07-26 11:20:15.662 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-4485 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x72800e20db80>
2025-07-26 11:20:15.662 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-4485 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x72800e20db80>
2025-07-26 11:20:15.740 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-28195 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x72800e10da00>
2025-07-26 11:20:15.740 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-28195 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x72800e10da00>
2025-07-26 11:20:16.102 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-28188 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x72800dca8eb0>
2025-07-26 11:20:16.102 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-28188 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x72800dca8eb0>
2025-07-26 11:20:16.102 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-28188 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x72800dca8eb0>
2025-07-26 11:20:16.102 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-28188 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x72800dca8eb0>
2025-07-26 11:20:16.165 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-27950 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x72800e588ac0>
2025-07-26 11:20:16.165 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-27950 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x72800e588ac0>
2025-07-26 11:20:16.165 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-27950 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x72800e588ac0>
2025-07-26 11:20:16.165 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-27950 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x72800e588ac0>
2025-07-26 11:22:36.916 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-38568 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727fbe9ee100>
2025-07-26 11:22:37.565 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-38563 补丁时出错: functools.partial(<function FigureCanvasAgg.print_png at 0x727fbeaa75e0>, orientation='portrait') did not call Figure.draw, so no renderer is available
2025-07-26 11:22:38.280 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-37284 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727fbeb8b7f0>
2025-07-26 12:30:01.006 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-39319 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727fff26ce20>
2025-07-26 12:30:01.890 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-39321 补丁时出错: functools.partial(<function FigureCanvasAgg.print_png at 0x727fff361ee0>, orientation='portrait') did not call Figure.draw, so no renderer is available
2025-07-26 12:30:02.320 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-39322 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727fff451580>
2025-07-26 12:30:02.944 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-39676 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727fff451580>
2025-07-26 12:30:04.253 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-28010 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727fff36db80>
2025-07-26 12:38:22.083 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-40728 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6df192b0>
2025-07-26 12:38:23.039 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-40729 补丁时出错: functools.partial(<function FigureCanvasAgg.print_png at 0x727f305ad700>, orientation='portrait') did not call Figure.draw, so no renderer is available
2025-07-26 12:38:23.801 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-40727 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6dfb66a0>
2025-07-26 12:38:24.765 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-40726 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6de94f70>
2025-07-26 12:40:05.181 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-36759 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x72800c6c2b80>
2025-07-26 12:40:06.362 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-36761 补丁时出错: functools.partial(<function FigureCanvasAgg.print_png at 0x72800c8ddf70>, orientation='portrait') did not call Figure.draw, so no renderer is available
2025-07-26 12:40:06.363 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-36765 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x72800c8fe430>
2025-07-26 12:40:08.421 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-36766 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x72800c9c5bb0>
2025-07-26 12:40:08.421 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-36766 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x72800c9c5bb0>
2025-07-26 12:42:53.338 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-39208 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.338 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-39208 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.312 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-21523 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.338 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-39208 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.546 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-39201 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.312 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-21523 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.338 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-39208 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.312 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-21523 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.338 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-39208 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.546 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-39201 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.327 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-3711 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.546 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-39201 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.312 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-21523 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.327 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-3711 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.327 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-3711 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.546 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-39201 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.286 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-3710 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.286 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-3710 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.312 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-21523 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.286 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-3710 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.338 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-39208 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.327 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-3711 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.546 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-39201 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.338 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-39208 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.338 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-39208 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.327 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-3711 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.286 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-3710 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.286 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-3710 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.312 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-21523 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.312 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-21523 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.327 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-3711 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.312 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-21523 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.546 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-39201 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.286 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-3710 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.546 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-39201 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.546 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-39201 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.286 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-3710 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.286 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-3710 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.327 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-3711 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:42:53.327 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-3711 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x727e6907c1f0>
2025-07-26 12:49:55.307 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-21520 补丁时出错: functools.partial(<function FigureCanvasAgg.print_png at 0x727e68fef700>, orientation='portrait') did not call Figure.draw, so no renderer is available
2025-07-26 12:49:55.307 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-21520 补丁时出错: functools.partial(<function FigureCanvasAgg.print_png at 0x727e68fef700>, orientation='portrait') did not call Figure.draw, so no renderer is available
2025-07-26 12:49:55.307 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-21520 补丁时出错: functools.partial(<function FigureCanvasAgg.print_png at 0x727e68fef700>, orientation='portrait') did not call Figure.draw, so no renderer is available
2025-07-26 12:49:55.307 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-21520 补丁时出错: functools.partial(<function FigureCanvasAgg.print_png at 0x727e68fef700>, orientation='portrait') did not call Figure.draw, so no renderer is available
2025-07-26 12:49:55.307 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-21520 补丁时出错: functools.partial(<function FigureCanvasAgg.print_png at 0x727e68fef700>, orientation='portrait') did not call Figure.draw, so no renderer is available
2025-07-26 12:50:39.073 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-39215 补丁时出错: functools.partial(<function FigureCanvasAgg.print_png at 0x727e68ee4310>, orientation='portrait') did not call Figure.draw, so no renderer is available
2025-07-26 12:50:40.807 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='services.nvd.nist.gov', port=443): Max retries exceeded with url: /rest/json/cves/2.0/?cveId=CVE-2023-4921 (Caused by ProxyError('Unable to connect to proxy', NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x727d95bfe7f0>: Failed to resolve 'host.docker.internal' ([Errno -3] Temporary failure in name resolution)")))
2025-07-26 12:50:40.943 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='services.nvd.nist.gov', port=443): Max retries exceeded with url: /rest/json/cves/2.0/?cveId=CVE-2023-4918 (Caused by ProxyError('Unable to connect to proxy', NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x727e69214070>: Failed to resolve 'host.docker.internal' ([Errno -3] Temporary failure in name resolution)")))
2025-07-26 12:50:40.943 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='services.nvd.nist.gov', port=443): Max retries exceeded with url: /rest/json/cves/2.0/?cveId=CVE-2023-3712 (Caused by ProxyError('Unable to connect to proxy', NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x727d95bfeb50>: Failed to resolve 'host.docker.internal' ([Errno -3] Temporary failure in name resolution)")))
2025-07-26 12:50:40.943 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-4501 补丁时出错: functools.partial(<function FigureCanvasAgg.print_png at 0x727e68fb2040>, orientation='portrait') did not call Figure.draw, so no renderer is available
2025-07-26 12:51:03.018 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='services.nvd.nist.gov', port=443): Max retries exceeded with url: /rest/json/cves/2.0/?cveId=CVE-2023-41885 (Caused by ProxyError('Unable to connect to proxy', NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x727d95b9e850>: Failed to resolve 'host.docker.internal' ([Errno -3] Temporary failure in name resolution)")))
2025-07-26 13:20:52.304 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-4915 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x707df57a44f0>
2025-07-26 13:20:52.310 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-4153 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x707df57a44f0>
2025-07-26 13:20:52.310 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-4400 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x707df57a44f0>
2025-07-26 13:20:52.329 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-4213 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x707df57a44f0>
2025-07-26 13:20:53.221 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-4916 补丁时出错: functools.partial(<function FigureCanvasAgg.print_png at 0x707df506b820>, orientation='portrait') did not call Figure.draw, so no renderer is available
2025-07-26 13:20:53.221 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-4916 补丁时出错: functools.partial(<function FigureCanvasAgg.print_png at 0x707df506b820>, orientation='portrait') did not call Figure.draw, so no renderer is available
2025-07-26 13:20:53.226 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-26369 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x707df582bb80>
2025-07-26 13:20:53.221 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-4916 补丁时出错: functools.partial(<function FigureCanvasAgg.print_png at 0x707df506b820>, orientation='portrait') did not call Figure.draw, so no renderer is available
2025-07-26 13:20:53.221 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-4916 补丁时出错: functools.partial(<function FigureCanvasAgg.print_png at 0x707df506b820>, orientation='portrait') did not call Figure.draw, so no renderer is available
2025-07-26 13:20:53.226 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-26369 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x707df582bb80>
2025-07-26 13:20:53.226 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-26369 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x707df582bb80>
2025-07-26 13:20:53.226 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-26369 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x707df582bb80>
2025-07-26 13:20:53.248 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-4917 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x707df57e9580>
2025-07-26 13:20:53.248 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-4917 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x707df57e9580>
2025-07-26 13:20:53.248 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-4917 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x707df57e9580>
2025-07-26 13:20:53.248 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-4917 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x707df57e9580>
2025-07-26 13:22:51.698 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-4568 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x707de7423f10>
2025-07-26 13:22:52.126 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-23840 补丁时出错: functools.partial(<function FigureCanvasAgg.print_png at 0x707de734ba60>, orientation='portrait') did not call Figure.draw, so no renderer is available
2025-07-26 13:22:52.290 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-41155 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x707de73da820>
2025-07-26 13:22:52.454 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-40617 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x707de73da820>
2025-07-26 13:22:52.605 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-41152 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x707de73da820>
2025-07-26 13:22:52.947 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-41158 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x707de77e5fd0>
2025-07-26 13:22:53.068 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-41162 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x707de76352b0>
2025-07-26 13:22:53.090 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-41154 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x707de73b6cd0>
2025-07-26 13:31:00.727 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: 500 Server Error: Internal Server Error for url: https://api.github.com/graphql
2025-07-26 13:31:00.779 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: 500 Server Error: Internal Server Error for url: https://api.github.com/graphql
2025-07-26 13:35:33.215 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-2163 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7c0bbeaac190>
2025-07-26 13:35:33.760 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-5063 补丁时出错: functools.partial(<function FigureCanvasAgg.print_png at 0x7c0bbecd31f0>, orientation='portrait') did not call Figure.draw, so no renderer is available
2025-07-26 13:35:33.966 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-43616 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7c0bbeb5d4c0>
2025-07-26 13:35:34.076 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-43617 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7c0bbeb138b0>
2025-07-26 13:35:34.361 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-43618 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7c0bbeb138b0>
2025-07-26 13:35:34.361 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-43618 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7c0bbeb138b0>
2025-07-26 13:42:28.002 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-41029 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7c0bde4febe0>
2025-07-26 13:51:15.530 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-40668 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b48fb3559a0>
2025-07-26 13:51:16.121 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-40677 补丁时出错: functools.partial(<function FigureCanvasAgg.print_png at 0x7b48fb49c9d0>, orientation='portrait') did not call Figure.draw, so no renderer is available
2025-07-26 13:51:16.326 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-40665 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b48fb6ec5e0>
2025-07-26 13:51:16.778 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-40669 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b48fb61e520>
2025-07-26 13:51:16.778 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-40669 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b48fb61e520>
2025-07-26 13:51:16.963 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-40667 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b48fb3b9d00>
2025-07-26 13:51:16.963 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-40667 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b48fb3b9d00>
2025-07-26 13:53:10.900 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-41984 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b4859463eb0>
2025-07-26 13:53:10.900 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-41984 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b4859463eb0>
2025-07-26 13:53:11.921 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-41981 补丁时出错: functools.partial(<function FigureCanvasAgg.print_png at 0x7b48593a1ee0>, orientation='portrait') did not call Figure.draw, so no renderer is available
2025-07-26 13:53:12.506 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-41904 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b489a678070>
2025-07-26 13:53:12.861 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-41979 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b48595e8b50>
2025-07-26 13:53:13.321 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-41980 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b48594bba90>
2025-07-26 13:53:13.961 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-41968 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b48594bba90>
2025-07-26 13:53:13.961 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-41968 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b48594bba90>
2025-07-26 13:53:13.961 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-41968 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b48594bba90>
2025-07-26 13:53:14.286 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-41986 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b489a656970>
2025-07-26 14:00:03.603 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='api.github.com', port=443): Max retries exceeded with url: /graphql (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1137)')))
2025-07-26 14:00:04.755 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='api.github.com', port=443): Max retries exceeded with url: /graphql (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1137)')))
2025-07-26 14:00:06.059 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='services.nvd.nist.gov', port=443): Max retries exceeded with url: /rest/json/cves/2.0/?cveId=CVE-2023-42756 (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1137)')))
2025-07-26 14:00:06.228 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='services.nvd.nist.gov', port=443): Max retries exceeded with url: /rest/json/cves/2.0/?cveId=CVE-2023-43860 (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1137)')))
2025-07-26 14:00:06.229 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='services.nvd.nist.gov', port=443): Max retries exceeded with url: /rest/json/cves/2.0/?cveId=CVE-2023-43861 (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1137)')))
2025-07-26 14:00:07.271 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='services.nvd.nist.gov', port=443): Max retries exceeded with url: /rest/json/cves/2.0/?cveId=CVE-2023-43862 (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1137)')))
2025-07-26 14:00:08.664 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='services.nvd.nist.gov', port=443): Max retries exceeded with url: /rest/json/cves/2.0/?cveId=CVE-2023-43863 (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1137)')))
2025-07-26 14:00:12.355 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='services.nvd.nist.gov', port=443): Max retries exceeded with url: /rest/json/cves/2.0/?cveId=CVE-2023-43864 (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1137)')))
2025-07-26 14:00:13.768 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='services.nvd.nist.gov', port=443): Max retries exceeded with url: /rest/json/cves/2.0/?cveId=CVE-2023-43865 (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1137)')))
2025-07-26 14:00:14.850 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='services.nvd.nist.gov', port=443): Max retries exceeded with url: /rest/json/cves/2.0/?cveId=CVE-2023-43866 (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1137)')))
2025-07-26 14:00:14.851 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='api.github.com', port=443): Max retries exceeded with url: /graphql (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1137)')))
2025-07-26 14:00:15.175 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='services.nvd.nist.gov', port=443): Max retries exceeded with url: /rest/json/cves/2.0/?cveId=CVE-2023-43867 (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1137)')))
2025-07-26 14:00:16.605 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='services.nvd.nist.gov', port=443): Max retries exceeded with url: /rest/json/cves/2.0/?cveId=CVE-2023-43868 (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1137)')))
2025-07-26 14:00:16.606 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='services.nvd.nist.gov', port=443): Max retries exceeded with url: /rest/json/cves/2.0/?cveId=CVE-2023-43871 (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1137)')))
2025-07-26 14:00:17.484 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='services.nvd.nist.gov', port=443): Max retries exceeded with url: /rest/json/cves/2.0/?cveId=CVE-2023-43872 (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1137)')))
2025-07-26 14:00:21.299 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='services.nvd.nist.gov', port=443): Max retries exceeded with url: /rest/json/cves/2.0/?cveId=CVE-2023-43873 (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1137)')))
2025-07-26 14:00:22.703 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='services.nvd.nist.gov', port=443): Max retries exceeded with url: /rest/json/cves/2.0/?cveId=CVE-2023-43874 (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1137)')))
2025-07-26 14:00:23.779 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='services.nvd.nist.gov', port=443): Max retries exceeded with url: /rest/json/cves/2.0/?cveId=CVE-2023-5215 (Caused by SSLError(SSLEOFError(8, 'EOF occurred in violation of protocol (_ssl.c:1137)')))
2025-07-26 14:03:08.091 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-5194 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b47f0559b20>
2025-07-26 14:03:09.235 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-5196 补丁时出错: functools.partial(<function FigureCanvasAgg.print_png at 0x7b47f06a69d0>, orientation='portrait') did not call Figure.draw, so no renderer is available
2025-07-26 14:03:10.126 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-5193 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b47f0831790>
2025-07-26 14:03:10.687 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-5198 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b481046f6d0>
2025-07-26 14:03:10.695 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-5195 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b481046f6d0>
2025-07-26 14:03:13.087 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-5257 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b47f0897550>
2025-07-26 14:03:13.087 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-5257 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b47f0897550>
2025-07-26 14:03:13.087 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-5257 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b47f0897550>
2025-07-26 14:03:13.087 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-5257 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b47f0897550>
2025-07-26 14:03:28.299 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-5260 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b47ce5d1f10>
2025-07-26 14:05:23.648 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-5285 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b4730ebf2e0>
2025-07-26 14:05:24.692 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-5286 补丁时出错: functools.partial(<function FigureCanvasAgg.print_png at 0x7b4730f8ad30>, orientation='portrait') did not call Figure.draw, so no renderer is available
2025-07-26 14:05:25.467 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-5293 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b479d463070>
2025-07-26 14:05:26.092 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-5284 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b473a549880>
2025-07-26 14:05:26.933 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-5294 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b473af4cb80>
2025-07-26 14:05:26.933 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-5294 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b473af4cb80>
2025-07-26 14:05:47.473 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-5317 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b473088f340>
2025-07-26 14:05:48.778 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-5319 补丁时出错: functools.partial(<function FigureCanvasAgg.print_png at 0x7b4730a0eb80>, orientation='portrait') did not call Figure.draw, so no renderer is available
2025-07-26 14:05:49.457 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-5227 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b47309c8580>
2025-07-26 14:05:50.147 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-5318 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b4730a72cd0>
2025-07-26 14:05:50.915 | ERROR    | __main__:collect_patches_for_cve:720 | 收集 CVE-2023-5316 补丁时出错: <matplotlib.backends.backend_agg.RendererAgg object at 0x7b4730a72cd0>
2025-07-26 14:12:36.903 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='api.github.com', port=443): Max retries exceeded with url: /graphql (Caused by ProxyError('Unable to connect to proxy', NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x7b46c51ea6d0>: Failed to resolve 'host.docker.internal' ([Errno -3] Temporary failure in name resolution)")))
2025-07-26 14:12:36.903 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='api.github.com', port=443): Max retries exceeded with url: /graphql (Caused by ProxyError('Unable to connect to proxy', NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x7b46c51ea6d0>: Failed to resolve 'host.docker.internal' ([Errno -3] Temporary failure in name resolution)")))
2025-07-26 14:13:29.765 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='api.github.com', port=443): Max retries exceeded with url: /graphql (Caused by ProxyError('Unable to connect to proxy', NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x7b46c51765b0>: Failed to resolve 'host.docker.internal' ([Errno -3] Temporary failure in name resolution)")))
2025-07-26 14:13:29.765 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='api.github.com', port=443): Max retries exceeded with url: /graphql (Caused by ProxyError('Unable to connect to proxy', NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x7b46c51765b0>: Failed to resolve 'host.docker.internal' ([Errno -3] Temporary failure in name resolution)")))
2025-07-26 17:31:20.101 | ERROR    | src.data_collection.utils.http:request:196 | Request failed: HTTPSConnectionPool(host='services.nvd.nist.gov', port=443): Max retries exceeded with url: /rest/json/cves/2.0/?resultsPerPage=2000&noRejected=&startIndex=0&pubStartDate=2023-08-23T00%3A00%3A00.000Z&pubEndDate=2023-08-30T23%3A59%3A59.999Z (Caused by ProxyError('Unable to connect to proxy', NameResolutionError("<urllib3.connection.HTTPSConnection object at 0x766ea7dea310>: Failed to resolve 'host.docker.internal' ([Errno -2] Name or service not known)")))
