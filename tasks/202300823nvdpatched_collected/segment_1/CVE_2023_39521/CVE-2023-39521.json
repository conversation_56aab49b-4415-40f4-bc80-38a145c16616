{"cve_id": "CVE-2023-39521", "published_date": "2023-08-24T23:15:09.000", "last_modified_date": "2024-11-21T08:15:35.613", "descriptions": [{"lang": "en", "value": "Tuleap is an open source suite to improve management of software developments and collaboration. In Tuleap Community Edition prior to version *********** and Tuleap Enterprise Edition prior to versions 14.10-6 and 14.11-3, content displayed in the \"card fields\" (visible in the kanban and PV2 apps) is not properly escaped. An agile dashboard administrator deleting a kanban with a malicious label can be forced to execute uncontrolled code. Tuleap Community Edition ***********, Tuleap Enterprise Edition 14.10-6, and Tuleap Enterprise Edition 14.11-3 contain a fix for this issue."}], "references": [{"url": "https://github.com/Enalean/tuleap/commit/93d10654b1d95c5bf500204666310418b01b8a8d", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/Enalean/tuleap/security/advisories/GHSA-h9xc-w7qq-vpfc", "source": "<EMAIL>", "tags": ["Patch", "Vendor Advisory"]}, {"url": "https://tuleap.net/plugins/git/tuleap/tuleap/stable?a=commit&h=93d10654b1d95c5bf500204666310418b01b8a8d", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://tuleap.net/plugins/tracker/?aid=33656", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Patch", "Vendor Advisory"]}]}