{"cve_id": "CVE-2023-39521", "trace_time": "2025-07-26T18:32:43.907370", "network_stats": {"total_nodes": 14, "total_edges": 20, "issue_nodes": 0, "patch_nodes": 1}, "all_patches": [], "high_confidence_patches": [], "patch_summary": "## 补丁候选摘要\n\n### 1. https://github.com/Enalean/tuleap/commit/93d10654b1d95c5bf500204666310418b01b8a8d\n- 置信度: 0.80\n- 类型: 3\n- 来源: GITHUB\n- 来源路径:\n  - 路径1: ROOT → NVD → GITHUB\n  - 路径2: ROOT → GITHUB → GITHUB\n  - 路径3: ROOT → GITHUB\n- 提交信息: Fixes request #33656: XSS on the success message of a kanban deletion\n- 仓库: Enalean/tuleap\n- 更改统计: 2个文件, +4, -3\n", "equivalent_patches": {}, "language_filter": "c,cpp,c++", "language_filter_stats": {"original_patches": 1, "filtered_patches": 0, "original_high_confidence": 1, "filtered_high_confidence": 0}}