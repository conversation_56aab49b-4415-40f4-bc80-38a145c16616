{"cve_id": "CVE-2023-39521", "cve_published_date": "2023-08-24T23:15:09.000", "cve_affected_cpes": [], "nodes": [{"node_id": "709cd8eb-246d-4ea8-afa8-fbcaae51579f", "url": "https://nvd.nist.gov/vuln/detail/CVE-2023-39521", "title": "CVE-2023-39521", "node_type": "ROOT", "source": "ROOT", "depth": 0, "category_in_type": "", "importance_score": 1.0}, {"node_id": "7d961abd-ba7b-4807-abed-559c4108aa29", "url": "https://nvd.nist.gov", "title": "NVD", "node_type": "SOURCE", "source": "NVD", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "acf910d5-1aad-4b8b-9999-031b16aa5eed", "url": "https://github", "title": "GITHUB", "node_type": "SOURCE", "source": "GITHUB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "0a432f54-99ae-40fb-9624-aa66dab0aff3", "url": "https://gitlab", "title": "GITLAB", "node_type": "SOURCE", "source": "GITLAB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "7ca40e45-7060-4e73-9de9-e29d35f83a48", "url": "https://debian", "title": "DEBIAN", "node_type": "SOURCE", "source": "DEBIAN", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "ce180a5c-7645-4404-be92-38c55b6fbede", "url": "https://redhat", "title": "REDHAT", "node_type": "SOURCE", "source": "REDHAT", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "46889156-31b9-4396-8e35-bb6c51c7fd15", "url": "https://apache", "title": "APACHE", "node_type": "SOURCE", "source": "APACHE", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "e239ed1c-090d-41f0-982b-fe8c94d97d2a", "url": "https://other.source", "title": "OTHER", "node_type": "SOURCE", "source": "OTHER", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "a6e7fa64-70e2-49b3-ab3e-f09aa27a6f37", "url": "https://github.com/Enalean/tuleap/commit/93d10654b1d95c5bf500204666310418b01b8a8d", "title": "93d10654b1d95c5bf500204666310418b01b8a8d", "node_type": "PATCH", "source": "GITHUB", "depth": 2, "category_in_type": "git_commit", "importance_score": 1.0}, {"node_id": "6eafca3d-3003-4347-8215-3a8007d62a3f", "url": "https://github.com/Enalean/tuleap/security/advisories/GHSA-h9xc-w7qq-vpfc", "title": "Ghsa h9xc w7qq vpfc", "node_type": "COMMON", "source": "GITHUB", "depth": 2, "category_in_type": "common_github_url", "importance_score": 1.0}, {"node_id": "2842bff7-6d10-4487-8527-4b0d28cea58c", "url": "https://tuleap.net/plugins/git/tuleap/tuleap/stable", "title": "Stable", "node_type": "COMMON", "source": "OTHER", "depth": 2, "category_in_type": "common_url", "importance_score": 1.0}, {"node_id": "6b4432f3-4951-46ac-a826-5ee97889b44f", "url": "https://tuleap.net/plugins/tracker", "title": "Tracker", "node_type": "COMMON", "source": "OTHER", "depth": 2, "category_in_type": "common_url", "importance_score": 1.0}, {"node_id": "610e68d7-f302-4feb-befe-fbec908420e3", "url": "https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax", "title": "", "node_type": "COMMON", "source": "GITHUB", "depth": 3, "category_in_type": "common_github_url", "importance_score": 0.0}, {"node_id": "c4e759d0-9a12-47f8-af41-ca4ee569c263", "url": "https://www.tuleap.org/security", "title": "", "node_type": "COMMON", "source": "GITHUB", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}], "edges": [{"edge_id": "472f705f-2040-483e-890a-a8e4047e90c6", "source_id": "709cd8eb-246d-4ea8-afa8-fbcaae51579f", "target_id": "7d961abd-ba7b-4807-abed-559c4108aa29", "weight": 0.8, "description": "来源"}, {"edge_id": "f36d2f51-1c57-46d5-a5e5-b66096da3a9a", "source_id": "709cd8eb-246d-4ea8-afa8-fbcaae51579f", "target_id": "acf910d5-1aad-4b8b-9999-031b16aa5eed", "weight": 0.8, "description": "来源"}, {"edge_id": "245662c7-b827-4637-8c4b-152df5b3858a", "source_id": "709cd8eb-246d-4ea8-afa8-fbcaae51579f", "target_id": "0a432f54-99ae-40fb-9624-aa66dab0aff3", "weight": 0.8, "description": "来源"}, {"edge_id": "3269178c-8bb8-4fb8-ab93-ad54195df30a", "source_id": "709cd8eb-246d-4ea8-afa8-fbcaae51579f", "target_id": "7ca40e45-7060-4e73-9de9-e29d35f83a48", "weight": 0.8, "description": "来源"}, {"edge_id": "b17c9fef-50e7-443a-8b07-80582c62b5e8", "source_id": "709cd8eb-246d-4ea8-afa8-fbcaae51579f", "target_id": "ce180a5c-7645-4404-be92-38c55b6fbede", "weight": 0.8, "description": "来源"}, {"edge_id": "690dadb3-2d02-4cd0-aba5-8a4ba64aaa96", "source_id": "709cd8eb-246d-4ea8-afa8-fbcaae51579f", "target_id": "46889156-31b9-4396-8e35-bb6c51c7fd15", "weight": 0.8, "description": "来源"}, {"edge_id": "5fcbd1ab-460b-4cda-90f6-631f314c525b", "source_id": "709cd8eb-246d-4ea8-afa8-fbcaae51579f", "target_id": "e239ed1c-090d-41f0-982b-fe8c94d97d2a", "weight": 0.5, "description": "其他来源"}, {"edge_id": "6c7aeaba-bba7-4e00-8a8c-53bdca1c6412", "source_id": "7d961abd-ba7b-4807-abed-559c4108aa29", "target_id": "709cd8eb-246d-4ea8-afa8-fbcaae51579f", "weight": 1.0, "description": "引用"}, {"edge_id": "dcb99f76-02c5-4023-ae69-4b1a6d867307", "source_id": "acf910d5-1aad-4b8b-9999-031b16aa5eed", "target_id": "a6e7fa64-70e2-49b3-ab3e-f09aa27a6f37", "weight": 1.0, "description": "引用"}, {"edge_id": "a4c8d985-e84e-41b8-9364-686fc9047c60", "source_id": "7d961abd-ba7b-4807-abed-559c4108aa29", "target_id": "a6e7fa64-70e2-49b3-ab3e-f09aa27a6f37", "weight": 0.7, "description": "NVD引用"}, {"edge_id": "a491b58b-e176-4396-ba6d-eb4e6ebee7b9", "source_id": "709cd8eb-246d-4ea8-afa8-fbcaae51579f", "target_id": "a6e7fa64-70e2-49b3-ab3e-f09aa27a6f37", "weight": 0.9, "description": "直接补丁"}, {"edge_id": "7d5b1c81-4027-47bd-a4e9-c732ee7e27b9", "source_id": "acf910d5-1aad-4b8b-9999-031b16aa5eed", "target_id": "6eafca3d-3003-4347-8215-3a8007d62a3f", "weight": 1.0, "description": "引用"}, {"edge_id": "9a1cdede-5ded-44d5-86b9-d73b3471abd3", "source_id": "7d961abd-ba7b-4807-abed-559c4108aa29", "target_id": "6eafca3d-3003-4347-8215-3a8007d62a3f", "weight": 0.7, "description": "NVD引用"}, {"edge_id": "3e3695d2-d4dd-4375-9df7-cd44f4478d58", "source_id": "e239ed1c-090d-41f0-982b-fe8c94d97d2a", "target_id": "2842bff7-6d10-4487-8527-4b0d28cea58c", "weight": 1.0, "description": "引用"}, {"edge_id": "c34274bb-e01a-4e02-8689-43a4f7358c19", "source_id": "7d961abd-ba7b-4807-abed-559c4108aa29", "target_id": "2842bff7-6d10-4487-8527-4b0d28cea58c", "weight": 0.7, "description": "NVD引用"}, {"edge_id": "6b84d9d5-b6b1-4328-b776-589537bc8e18", "source_id": "e239ed1c-090d-41f0-982b-fe8c94d97d2a", "target_id": "6b4432f3-4951-46ac-a826-5ee97889b44f", "weight": 1.0, "description": "引用"}, {"edge_id": "09d190e7-789e-486f-aa5b-19f7c268b3c8", "source_id": "7d961abd-ba7b-4807-abed-559c4108aa29", "target_id": "6b4432f3-4951-46ac-a826-5ee97889b44f", "weight": 0.7, "description": "NVD引用"}, {"edge_id": "3a888238-f3c1-42ea-ae46-8081e52bc457", "source_id": "6eafca3d-3003-4347-8215-3a8007d62a3f", "target_id": "610e68d7-f302-4feb-befe-fbec908420e3", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "a3b049ad-83d1-462c-b40b-48d5d75ee7fc", "source_id": "acf910d5-1aad-4b8b-9999-031b16aa5eed", "target_id": "610e68d7-f302-4feb-befe-fbec908420e3", "weight": 0.9, "description": "来源引用"}, {"edge_id": "68b443ac-b652-4078-9c69-685e0d3e9a2a", "source_id": "6eafca3d-3003-4347-8215-3a8007d62a3f", "target_id": "c4e759d0-9a12-47f8-af41-ca4ee569c263", "weight": 0.3333333333333333, "description": "引用"}]}