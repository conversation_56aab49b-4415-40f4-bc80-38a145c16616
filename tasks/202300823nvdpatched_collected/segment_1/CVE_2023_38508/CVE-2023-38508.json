{"cve_id": "CVE-2023-38508", "published_date": "2023-08-24T23:15:08.803", "last_modified_date": "2024-11-21T08:13:43.343", "descriptions": [{"lang": "en", "value": "Tuleap is an open source suite to improve management of software developments and collaboration. In Tuleap Community Edition prior to version *********** and Tuleap Enterprise Edition prior to versions 14.10-6 and 14.11-3, the preview of an artifact link with a type does not respect the project, tracker and artifact level permissions. The issue occurs on the artifact view (not reproducible on the artifact modal). Users might get access to information they should not have access to. Only the title, status, assigned to and last update date fields as defined by the semantics are impacted. If those fields have strict permissions (e.g. the title is only visible to a specific user group) those permissions are still enforced. Tuleap Community Edition ***********, Tuleap Enterprise Edition 14.10-6, and Tuleap Enterprise Edition 14.11-3 contain a fix for this issue."}], "references": [{"url": "https://github.com/Enalean/tuleap/commit/307c1c8044522a2dcc711062b18a3b3f9059a6c3", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/Enalean/tuleap/security/advisories/GHSA-h637-g4xp-2992", "source": "<EMAIL>", "tags": ["Patch", "Vendor Advisory"]}, {"url": "https://tuleap.net/plugins/git/tuleap/tuleap/stable?a=commit&h=307c1c8044522a2dcc711062b18a3b3f9059a6c3", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://tuleap.net/plugins/tracker/?aid=33608", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Patch", "Vendor Advisory"]}]}