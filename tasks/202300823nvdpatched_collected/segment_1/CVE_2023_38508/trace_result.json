{"cve_id": "CVE-2023-38508", "trace_time": "2025-07-26T18:29:19.409451", "network_stats": {"total_nodes": 12, "total_edges": 17, "issue_nodes": 0, "patch_nodes": 1}, "all_patches": [], "high_confidence_patches": [], "patch_summary": "## 补丁候选摘要\n\n### 1. https://github.com/Enalean/tuleap/commit/307c1c8044522a2dcc711062b18a3b3f9059a6c3\n- 置信度: 0.80\n- 类型: 3\n- 来源: GITHUB\n- 来源路径:\n  - 路径1: ROOT → NVD → GITHUB\n  - 路径2: ROOT → GITHUB → GITHUB\n  - 路径3: ROOT → GITHUB\n- 提交信息: Fixes request #33608: Preview of a linked artifact with a type does not respect permissions\n- 仓库: Enalean/tuleap\n- 更改统计: 3个文件, +37, -25\n", "equivalent_patches": {}, "language_filter": "c,cpp,c++", "language_filter_stats": {"original_patches": 1, "filtered_patches": 0, "original_high_confidence": 1, "filtered_high_confidence": 0}}