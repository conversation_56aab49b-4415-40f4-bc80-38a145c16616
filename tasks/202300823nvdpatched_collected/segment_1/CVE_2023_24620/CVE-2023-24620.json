{"cve_id": "CVE-2023-24620", "published_date": "2023-08-25T20:15:07.893", "last_modified_date": "2024-11-21T07:48:14.763", "descriptions": [{"lang": "en", "value": "An issue was discovered in Esoteric YamlBeans through 1.15. A crafted YAML document is able perform am XML Entity Expansion attack against YamlBeans YamlReader. By exploiting the Anchor feature in YAML, it is possible to generate a small YAML document that, when read, is expanded to a large size, causing CPU and memory consumption, such as a Java Out-of-Memory exception."}, {"lang": "es", "value": "Un problema fue descubierto en Esoteric YamlBeans a través de 1.15. Un documento YAML manipulado es capaz de realizar un ataque de Expansión de Entidad XML contra YamlReader YamlBeans. Mediante la explotación de la función de anclaje en YAML, es posible generar un pequeño documento YAML que, cuando se lee, se expande a un gran tamaño, causando el consumo de CPU y memoria, como una excepción Java Out-of-Memory.\n"}], "references": [{"url": "https://contrastsecurity.com", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://github.com/Contrast-Security-OSS/yamlbeans/blob/main/SECURITY.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://github.com/EsotericSoftware", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}