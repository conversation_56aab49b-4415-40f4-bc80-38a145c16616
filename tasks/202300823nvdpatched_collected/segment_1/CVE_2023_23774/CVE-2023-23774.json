{"cve_id": "CVE-2023-23774", "published_date": "2023-08-29T09:15:09.403", "last_modified_date": "2024-11-21T07:46:48.237", "descriptions": [{"lang": "en", "value": "Motorola EBTS/MBTS Site Controller drops to debug prompt on unhandled exception. The Motorola MBTS Site Controller exposes a debug prompt on the device's serial port in case of an unhandled exception. This allows an attacker with physical access that is able to trigger such an exception to extract secret key material and/or gain arbitrary code execution on the device."}, {"lang": "es", "value": "El controlador de sitio EBTS/MBTS de Motorola muestra un mensaje de depuración en caso de una excepción no controlada. El controlador de sitio MBTS de Motorola expone un mensaje de depuración en el puerto serie del dispositivo en caso de una excepción no controlada. Esto permite que un atacante con acceso físico que pueda desencadenar dicha excepción extraiga material de clave secreta y/o obtenga la ejecución de código arbitrario en el dispositivo."}], "references": [{"url": "https://tetraburst.com/", "source": "<EMAIL>", "tags": ["Not Applicable"]}]}