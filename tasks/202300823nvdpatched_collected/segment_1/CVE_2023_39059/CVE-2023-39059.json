{"cve_id": "CVE-2023-39059", "published_date": "2023-08-28T22:15:08.983", "last_modified_date": "2024-11-21T08:14:42.013", "descriptions": [{"lang": "en", "value": "An issue in ansible semaphore v.2.8.90 allows a remote attacker to execute arbitrary code via a crafted payload to the extra variables parameter."}], "references": [{"url": "https://gist.github.com/Alevsk/1757da24c5fb8db735d392fd4146ca3a", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://www.alevsk.com/2023/07/a-quick-story-of-security-pitfalls-with-execcommand-in-software-integrations/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}