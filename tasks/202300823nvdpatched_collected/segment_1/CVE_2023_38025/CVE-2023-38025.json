{"cve_id": "CVE-2023-38025", "published_date": "2023-08-28T04:15:16.477", "last_modified_date": "2024-11-21T08:12:41.877", "descriptions": [{"lang": "en", "value": "\nSpotCam Co., Ltd. SpotCam FHD 2’s hidden Telnet function has a vulnerability of OS command injection. An remote unauthenticated attacker can exploit this vulnerability to execute command injection attack to arbitrary system commands or disrupt service.\n\n"}, {"lang": "es", "value": "La función oculta en SpotCam FHD 2 de SpotCam Co. Ltd. tiene una vulnerabilidad de inyección de comandos del sistema operativo. Un atacante remoto no autenticado puede explotar esta vulnerabilidad para ejecutar un ataque de inyección de comandos a comandos arbitrarios del sistema o interrumpir el servicio. "}], "references": [{"url": "https://www.twcert.org.tw/tw/cp-132-7332-ee011-1.html", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}