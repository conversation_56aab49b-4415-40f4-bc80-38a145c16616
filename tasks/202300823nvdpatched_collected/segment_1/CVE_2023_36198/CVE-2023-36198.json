{"cve_id": "CVE-2023-36198", "published_date": "2023-08-25T20:15:08.193", "last_modified_date": "2024-11-21T08:09:24.290", "descriptions": [{"lang": "en", "value": "Buffer Overflow vulnerability in skalenetwork sgxwallet v.1.9.0 allows an attacker to cause a denial of service via the trustedBlsSignMessage function."}, {"lang": "es", "value": "La vulnerabilidad de desbordamiento del búfer en sgxwallet v.1.9.0 de skalenetwork permite a un atacante provocar una denegación de servicio a través de la función trustedBlsSignMessage.\n"}], "references": [{"url": "https://github.com/skalenetwork/sgxwallet/issues/419", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}]}