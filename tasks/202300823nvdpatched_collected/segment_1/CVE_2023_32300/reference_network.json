{"cve_id": "CVE-2023-32300", "cve_published_date": "2023-08-23T15:15:08.117", "cve_affected_cpes": [], "nodes": [{"node_id": "6eb2c03b-79a2-4463-9200-5ebee1487f27", "url": "https://nvd.nist.gov/vuln/detail/CVE-2023-32300", "title": "CVE-2023-32300", "node_type": "ROOT", "source": "ROOT", "depth": 0, "category_in_type": "", "importance_score": 1.0}, {"node_id": "2b088713-11c0-4806-ba99-756574a6e0f9", "url": "https://nvd.nist.gov", "title": "NVD", "node_type": "SOURCE", "source": "NVD", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "2550d518-4072-469d-970e-9563d776fbec", "url": "https://github", "title": "GITHUB", "node_type": "SOURCE", "source": "GITHUB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "e0ed3918-b2a8-412c-a2b6-b67c34b93643", "url": "https://gitlab", "title": "GITLAB", "node_type": "SOURCE", "source": "GITLAB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "17755f5c-c383-4695-a722-0f90cf15d3b0", "url": "https://debian", "title": "DEBIAN", "node_type": "SOURCE", "source": "DEBIAN", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "b56d7264-0f18-4026-80d2-ea8902f8d4ed", "url": "https://redhat", "title": "REDHAT", "node_type": "SOURCE", "source": "REDHAT", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "d7cf2def-586f-47e7-80e8-bb2313acbac1", "url": "https://apache", "title": "APACHE", "node_type": "SOURCE", "source": "APACHE", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "16497611-b233-4005-89a8-b9f91e4af427", "url": "https://other.source", "title": "OTHER", "node_type": "SOURCE", "source": "OTHER", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "38f34a57-f3ff-4e14-b94f-c93031055b0a", "url": "https://patchstack.com/database/vulnerability/wpseo-local/wordpress-yoast-seo-local-plugin-14-8-reflected-cross-site-scripting-xss-vulnerability", "title": "Wordpress yoast seo local plugin 14 8 reflected cross site scripting xss vulnerability", "node_type": "COMMON", "source": "OTHER", "depth": 2, "category_in_type": "common_url", "importance_score": 1.0}, {"node_id": "fa505c42-4ea5-443f-b50c-73102d3da725", "url": "https://patchstack.com/wordpress-security", "title": "", "node_type": "COMMON", "source": "OTHER", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}, {"node_id": "2e400443-980a-4fb9-9fec-e3d335bd700e", "url": "https://patchstack.com/for-hosts", "title": "", "node_type": "COMMON", "source": "OTHER", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}], "edges": [{"edge_id": "528fa0ba-13a5-4482-9b01-e498a8868ca2", "source_id": "6eb2c03b-79a2-4463-9200-5ebee1487f27", "target_id": "2b088713-11c0-4806-ba99-756574a6e0f9", "weight": 0.8, "description": "来源"}, {"edge_id": "6d4ae319-9391-4825-be62-9b2fffa1c8ed", "source_id": "6eb2c03b-79a2-4463-9200-5ebee1487f27", "target_id": "2550d518-4072-469d-970e-9563d776fbec", "weight": 0.8, "description": "来源"}, {"edge_id": "1cd7d234-bf8f-45cf-a5b1-fb5fa84a472b", "source_id": "6eb2c03b-79a2-4463-9200-5ebee1487f27", "target_id": "e0ed3918-b2a8-412c-a2b6-b67c34b93643", "weight": 0.8, "description": "来源"}, {"edge_id": "b52bbf8a-bdeb-4689-be72-f54b3974df21", "source_id": "6eb2c03b-79a2-4463-9200-5ebee1487f27", "target_id": "17755f5c-c383-4695-a722-0f90cf15d3b0", "weight": 0.8, "description": "来源"}, {"edge_id": "41fd6816-7559-4e47-8ffb-fdb9f59839f4", "source_id": "6eb2c03b-79a2-4463-9200-5ebee1487f27", "target_id": "b56d7264-0f18-4026-80d2-ea8902f8d4ed", "weight": 0.8, "description": "来源"}, {"edge_id": "15854b96-64a4-48f6-bea9-9862f019ef00", "source_id": "6eb2c03b-79a2-4463-9200-5ebee1487f27", "target_id": "d7cf2def-586f-47e7-80e8-bb2313acbac1", "weight": 0.8, "description": "来源"}, {"edge_id": "abb9dca2-1279-47e1-b57f-492cd098e88c", "source_id": "6eb2c03b-79a2-4463-9200-5ebee1487f27", "target_id": "16497611-b233-4005-89a8-b9f91e4af427", "weight": 0.5, "description": "其他来源"}, {"edge_id": "5cd3bf82-ee62-459f-82ce-72f69dc00edb", "source_id": "2b088713-11c0-4806-ba99-756574a6e0f9", "target_id": "6eb2c03b-79a2-4463-9200-5ebee1487f27", "weight": 1.0, "description": "引用"}, {"edge_id": "1effaef9-858d-4deb-bb18-66526baa25e9", "source_id": "16497611-b233-4005-89a8-b9f91e4af427", "target_id": "38f34a57-f3ff-4e14-b94f-c93031055b0a", "weight": 1.0, "description": "引用"}, {"edge_id": "f9529aef-1258-495c-8960-ec88fe0b79d4", "source_id": "2b088713-11c0-4806-ba99-756574a6e0f9", "target_id": "38f34a57-f3ff-4e14-b94f-c93031055b0a", "weight": 0.7, "description": "NVD引用"}, {"edge_id": "739d34bf-cd5d-4463-afdb-200b6174e3aa", "source_id": "38f34a57-f3ff-4e14-b94f-c93031055b0a", "target_id": "fa505c42-4ea5-443f-b50c-73102d3da725", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "2e6f54c0-3866-4ea7-a185-b5e0855cc046", "source_id": "38f34a57-f3ff-4e14-b94f-c93031055b0a", "target_id": "2e400443-980a-4fb9-9fec-e3d335bd700e", "weight": 0.3333333333333333, "description": "引用"}]}