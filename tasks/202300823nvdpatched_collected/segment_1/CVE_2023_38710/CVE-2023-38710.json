{"cve_id": "CVE-2023-38710", "published_date": "2023-08-25T21:15:08.167", "last_modified_date": "2024-11-21T08:14:06.133", "descriptions": [{"lang": "en", "value": "An issue was discovered in Libreswan before 4.12. When an IKEv2 Child SA REKEY packet contains an invalid IPsec protocol ID number of 0 or 1, an error notify INVALID_SPI is sent back. The notify payload's protocol ID is copied from the incoming packet, but the code that verifies outgoing packets fails an assertion that the protocol ID must be ESP (2) or AH(3) and causes the pluto daemon to crash and restart. NOTE: the earliest affected version is 3.20."}, {"lang": "es", "value": "Se ha descubierto un problema en Libreswan anterior a 4.12. <PERSON><PERSON>do un paquete IKEv2 Child SA REKEY contiene un número de ID de protocolo IPsec no válido de 0 o 1, se devuelve una notificación de error INVALID_SPI. El ID de protocolo de la carga útil de la notificación se copia del paquete entrante, pero el código que verifica los paquetes salientes falla en la afirmación de que el ID de protocolo debe ser ESP (2) o AH(3) y hace que el demonio pluto se bloquee y reinicie. NOTA: la primera versión afectada es la 3.20.\n"}], "references": [{"url": "https://github.com/libreswan/libreswan/tags", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://libreswan.org/security/CVE-2023-38710/", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}