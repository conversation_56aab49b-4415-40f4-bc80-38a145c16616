{"cve_id": "CVE-2023-32236", "published_date": "2023-08-23T14:15:07.967", "last_modified_date": "2024-11-21T08:02:57.577", "descriptions": [{"lang": "en", "value": "Unauth. Reflected Cross-Site Scripting (XSS) vulnerability in Booking Ultra Pro Booking Ultra Pro Appointments Booking Calendar Plugin <= 1.1.8 versions."}], "references": [{"url": "https://patchstack.com/database/vulnerability/booking-ultra-pro/wordpress-booking-ultra-pro-appointments-booking-calendar-plugin-plugin-1-1-4-cross-site-scripting-xss?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}