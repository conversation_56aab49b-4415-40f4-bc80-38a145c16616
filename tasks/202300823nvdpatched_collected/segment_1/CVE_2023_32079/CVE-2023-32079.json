{"cve_id": "CVE-2023-32079", "published_date": "2023-08-24T23:15:08.570", "last_modified_date": "2024-11-21T08:02:40.237", "descriptions": [{"lang": "en", "value": "Netmaker makes networks with WireGuard. A Mass assignment vulnerability was found in versions prior to 0.17.1 and 0.18.6 that allows a non-admin user to escalate privileges to those of an admin user. The issue is patched in 0.17.1 and fixed in 0.18.6. If Users are using 0.17.1, they should run `docker pull gravitl/netmaker:v0.17.1` and `docker-compose up -d`. This will switch them to the patched users If users are using v0.18.0-0.18.5, they should upgrade to v0.18.6 or later. As a workaround, someone using version 0.17.1 can pull the latest docker image of the backend and restart the server."}], "references": [{"url": "https://github.com/gravitl/netmaker/security/advisories/GHSA-826j-8wp2-4x6q", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}