{"cve_id": "CVE-2021-3262", "published_date": "2023-08-29T20:15:09.487", "last_modified_date": "2024-11-21T06:21:10.810", "descriptions": [{"lang": "en", "value": "TripSpark VEO Transportation-2.2.x-XP_BB-20201123-184084 NovusEDU-2.2.x-XP_BB-20201123-184084 allows unsafe data inputs in POST body parameters from end users without sanitizing using server-side logic. It was possible to inject custom SQL commands into the \"Student Busing Information\" search queries."}], "references": [{"url": "http://tripspark.com", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "http://veo.com", "source": "<EMAIL>", "tags": ["Broken Link"]}, {"url": "https://susos.co/blog/f/cve-disclosureuncovered-sql-injection-in-tripspark-veo-transport", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}