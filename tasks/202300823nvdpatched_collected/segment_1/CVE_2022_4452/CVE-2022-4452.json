{"cve_id": "CVE-2022-4452", "published_date": "2023-08-25T15:15:08.040", "last_modified_date": "2024-11-21T07:35:17.463", "descriptions": [{"lang": "en", "value": "Insufficient data validation in crosvm in Google Chrome prior to 107.0.5304.62 allowed a remote attacker to potentially exploit object corruption via a crafted HTML page. (Chromium security severity: High)"}, {"lang": "es", "value": "Una validación de datos insuficiente en crosvm en Google Chrome anterior a 107.0.5304.62 permitía a un atacante remoto explotar potencialmente la corrupción de objetos a través de una página HTML manipulada. (Gravedad de seguridad de Chromium: Alta)"}], "references": [{"url": "https://bugs.chromium.org/p/chromium/issues/detail?id=1372457", "source": "<EMAIL>", "tags": ["Issue Tracking", "Patch", "Permissions Required", "Vendor Advisory"]}, {"url": "https://crbug.com/1372457", "source": "<EMAIL>", "tags": ["Issue Tracking", "Patch", "Permissions Required", "Vendor Advisory"]}]}