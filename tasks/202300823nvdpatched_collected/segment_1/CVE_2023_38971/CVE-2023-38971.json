{"cve_id": "CVE-2023-38971", "published_date": "2023-08-29T22:15:08.883", "last_modified_date": "2024-11-21T08:14:33.190", "descriptions": [{"lang": "en", "value": "Cross Site Scripting vulnerabiltiy in Badaso v.0.0.1 thru v.2.9.7 allows a remote attacker to execute arbitrary code via a crafted payload to the rack number parameter in the add new rack function."}], "references": [{"url": "https://github.com/anh91/uasoft-indonesia--badaso/blob/main/XSS3.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://panda002.hashnode.dev/badaso-version-297-has-xss-vulnerability-in-add-ranks", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}