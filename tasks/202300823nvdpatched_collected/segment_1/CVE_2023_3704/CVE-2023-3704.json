{"cve_id": "CVE-2023-3704", "published_date": "2023-08-24T07:15:11.670", "last_modified_date": "2024-11-21T08:17:52.930", "descriptions": [{"lang": "en", "value": "The vulnerability exists in CP-Plus DVR due to an improper input validation within the web-based management interface of the affected products. An unauthenticated remote attacker could exploit this vulnerability by sending specially crafted HTTP requests to the vulnerable device.\n\nSuccessful exploitation of this vulnerability could allow the remote attacker to change system time of the targeted device.\n"}], "references": [{"url": "https://www.cert-in.org.in/s2cMainServlet?pageid=PUBVLNOTES01&VLCODE=CIVN-2023-0240", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}