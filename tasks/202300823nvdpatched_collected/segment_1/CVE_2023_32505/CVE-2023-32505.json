{"cve_id": "CVE-2023-32505", "published_date": "2023-08-23T15:15:08.253", "last_modified_date": "2024-11-21T08:03:29.907", "descriptions": [{"lang": "en", "value": "Auth. (admin+) Stored Cross-Site Scripting (XSS) vulnerability in Arshid Easy Hide Login plugin <= 1.0.7 versions."}], "references": [{"url": "https://patchstack.com/database/vulnerability/easy-hide-login/wordpress-easy-hide-login-plugin-1-0-7-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}