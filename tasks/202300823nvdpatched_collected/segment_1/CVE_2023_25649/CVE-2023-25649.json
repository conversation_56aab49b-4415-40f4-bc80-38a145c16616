{"cve_id": "CVE-2023-25649", "published_date": "2023-08-25T10:15:08.247", "last_modified_date": "2024-11-21T07:49:52.010", "descriptions": [{"lang": "en", "value": "\nThere is a command injection vulnerability in a mobile internet product of ZTE. Due to insufficient validation of SET_DEVICE_LED interface parameter, an authenticated attacker could use the vulnerability to execute arbitrary commands.\n\n"}, {"lang": "es", "value": "Existe una vulnerabilidad de inyección de comandos en un producto de Internet móvil de ZTE. Debido a la insuficiente validación del parámetro de interfaz SET_DEVICE_LED, un atacante autenticado podría utilizar la vulnerabilidad para ejecutar comandos arbitrarios."}], "references": [{"url": "https://support.zte.com.cn/support/news/LoopholeInfoDetail.aspx?newsId=1032544", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}