{"cve_id": "CVE-2018-25089", "published_date": "2023-08-28T13:15:09.100", "last_modified_date": "2024-11-21T04:03:32.033", "descriptions": [{"lang": "en", "value": "A vulnerability was found in glb Meetup Tag Extension 0.1 on MediaWiki. It has been rated as problematic. This issue affects some unknown processing of the component Link Attribute Handler. The manipulation leads to use of web link to untrusted target with window.opener access. Upgrading to version 0.2 is able to address this issue. The identifier of the patch is 850c726d6bbfe0bf270801fbb92a30babea4155c. It is recommended to upgrade the affected component. The identifier VDB-238157 was assigned to this vulnerability."}], "references": [{"url": "https://github.com/glb/mediawiki-tag-extension-meetup/commit/850c726d6bbfe0bf270801fbb92a30babea4155c", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/glb/mediawiki-tag-extension-meetup/releases/tag/v0.2", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://vuldb.com/?ctiid.238157", "source": "<EMAIL>", "tags": ["Permissions Required", "Third Party Advisory"]}, {"url": "https://vuldb.com/?id.238157", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}