{"cve_id": "CVE-2023-38283", "published_date": "2023-08-29T16:15:08.960", "last_modified_date": "2024-11-21T08:13:13.623", "descriptions": [{"lang": "en", "value": "In OpenBGPD before 8.1, incorrect handling of BGP update data (length of path attributes) set by a potentially distant remote actor may cause the system to incorrectly reset a session. This is fixed in OpenBSD 7.3 errata 006."}], "references": [{"url": "https://blog.benjojo.co.uk/post/bgp-path-attributes-grave-error-handling", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://ftp.openbsd.org/pub/OpenBSD/patches/7.3/common/006_bgpd.patch.sig", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/openbgpd-portable/openbgpd-portable/releases/tag/8.1", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://news.ycombinator.com/item?id=37305800", "source": "<EMAIL>", "tags": ["Mailing List"]}, {"url": "https://www.openbsd.org/errata73.html", "source": "<EMAIL>", "tags": ["Release Notes"]}]}