{"cve_id": "CVE-2023-39663", "published_date": "2023-08-29T20:15:09.990", "last_modified_date": "2024-11-21T08:15:47.080", "descriptions": [{"lang": "en", "value": "Mathjax up to v2.7.9 was discovered to contain two Regular expression Denial of Service (ReDoS) vulnerabilities in MathJax.js via the components pattern and markdownPattern. NOTE: the vendor disputes this because the regular expressions are not applied to user input; thus, there is no risk."}, {"lang": "es", "value": "** DISPUTA ** Se descubrió que Mathjax hasta la versión v2.7.9 contenía dos expresiones Regulares de Denegación de Servicio (ReDoS) vulnerabilidades en MathJax.js a través del patrón de componentes y markdownPattern. NOTA: el proveedor cuestiona esto porque las expresiones regulares no se aplican a la entrada del usuario; <PERSON>r lo tanto, no hay riesgo."}], "references": [{"url": "https://github.com/mathjax/MathJax/issues/3074", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Vendor Advisory"]}]}