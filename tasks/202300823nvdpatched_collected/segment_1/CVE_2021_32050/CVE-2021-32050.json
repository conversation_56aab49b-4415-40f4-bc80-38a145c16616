{"cve_id": "CVE-2021-32050", "published_date": "2023-08-29T16:15:08.423", "last_modified_date": "2025-02-13T17:15:29.377", "descriptions": [{"lang": "en", "value": "Some MongoDB Drivers may erroneously publish events containing authentication-related data to a command listener configured by an application. The published events may contain security-sensitive data when specific authentication-related commands are executed.\n\nWithout due care, an application may inadvertently expose this sensitive information, e.g., by writing it to a log file. This issue only arises if an application enables the command listener feature (this is not enabled by default).\n\nThis issue affects the MongoDB C Driver 1.0.0 prior to 1.17.7, MongoDB PHP Driver 1.0.0 prior to 1.9.2, MongoDB Swift Driver 1.0.0 prior to 1.1.1, MongoDB Node.js Driver 3.6 prior to 3.6.10, MongoDB Node.js Driver 4.0 prior to 4.17.0 and MongoDB Node.js Driver 5.0 prior to 5.8.0. This issue also affects users of the MongoDB C++ Driver dependent on the C driver 1.0.0 prior to 1.17.7 (C++ driver prior to 3.7.0)."}], "references": [{"url": "https://jira.mongodb.org/browse/CDRIVER-3797", "source": "<EMAIL>", "tags": ["Issue Tracking", "Patch", "Vendor Advisory"]}, {"url": "https://jira.mongodb.org/browse/CXX-2028", "source": "<EMAIL>", "tags": ["Issue Tracking", "Patch", "Vendor Advisory"]}, {"url": "https://jira.mongodb.org/browse/NODE-3356", "source": "<EMAIL>", "tags": ["Issue Tracking", "Patch", "Vendor Advisory"]}, {"url": "https://jira.mongodb.org/browse/PHPC-1869", "source": "<EMAIL>", "tags": ["Issue Tracking", "Patch", "Vendor Advisory"]}, {"url": "https://jira.mongodb.org/browse/SWIFT-1229", "source": "<EMAIL>", "tags": ["Issue Tracking"]}, {"url": "https://security.netapp.com/advisory/ntap-20231006-0001/", "source": "<EMAIL>", "tags": []}]}