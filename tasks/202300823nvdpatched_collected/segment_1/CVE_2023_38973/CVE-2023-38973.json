{"cve_id": "CVE-2023-38973", "published_date": "2023-08-25T01:15:08.630", "last_modified_date": "2024-11-21T08:14:33.350", "descriptions": [{"lang": "en", "value": "A stored cross-site scripting (XSS) vulnerability in the Add Tag function of Badaso v2.9.7 allows attackers to execute arbitrary web scripts or HTML via a crafted payload injected into the Title parameter."}, {"lang": "es", "value": "Una vulnerabilidad de Cross-Site Scripting (XSS) almacenado existente en la función \"Add Tag\" de Badaso v2.9.7 permite a los atacantes ejecutar sripts web o scripts HTML arbitrarios a través de un payload manipulado inyectado en el parámetro \"Title\".  "}], "references": [{"url": "https://github.com/anh91/uasoft-indonesia--badaso/blob/main/xss5.md", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}