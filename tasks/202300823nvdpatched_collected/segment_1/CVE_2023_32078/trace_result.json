{"cve_id": "CVE-2023-32078", "trace_time": "2025-07-26T18:33:01.127002", "network_stats": {"total_nodes": 31, "total_edges": 57, "issue_nodes": 6, "patch_nodes": 3}, "all_patches": [], "high_confidence_patches": [], "patch_summary": "## 补丁候选摘要\n\n### 1. https://github.com/gravitl/netmaker/commit/b3be57c65bf0bbfab43b66853c8e3637a43e2839\n- 置信度: 0.80\n- 类型: 3\n- 来源: GITHUB\n- 来源路径:\n  - 路径1: ROOT → NVD → GITHUB\n  - 路径2: ROOT → GITHUB → GITHUB\n  - 路径3: ROOT → GITHUB\n- 提交信息: Merge pull request #2158 from gravitl/GRA-1479-user-updates\n- 仓库: gravitl/netmaker\n- 更改统计: 2个文件, +29, -0\n\n### 2. https://github.com/gravitl/netmaker/pull/2158/commits/d82e3a9b9e222fb9f4f4698ca278e011bd068f5d\n- 置信度: 0.80\n- 类型: 3\n- 来源: GITHUB\n- 来源路径:\n  - 路径1: ROOT → NVD → GITHUB → GITHUB\n  - 路径2: ROOT → GITHUB → GITHUB → GITHUB\n  - 路径3: ROOT → GITHUB → GITHUB\n  - 路径4: ROOT → GITHUB\n- 仓库: gravitl/netmaker\n- PR标题: add checks to user update processing\n\n### 3. https://github.com/gravitl/netmaker/pull/2158/commits/c2a4cb1145a59636e597f2332275814ade064a31\n- 置信度: 0.80\n- 类型: 3\n- 来源: GITHUB\n- 来源路径:\n  - 路径1: ROOT → NVD → GITHUB → GITHUB\n  - 路径2: ROOT → GITHUB → GITHUB → GITHUB\n  - 路径3: ROOT → GITHUB → GITHUB\n  - 路径4: ROOT → GITHUB\n- 仓库: gravitl/netmaker\n- PR标题: add checks to user update processing\n", "equivalent_patches": {}, "language_filter": "c,cpp,c++", "language_filter_stats": {"original_patches": 3, "filtered_patches": 0, "original_high_confidence": 3, "filtered_high_confidence": 0}}