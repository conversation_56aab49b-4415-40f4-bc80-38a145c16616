{"cve_id": "CVE-2023-32078", "published_date": "2023-08-24T22:15:10.267", "last_modified_date": "2024-11-21T08:02:40.113", "descriptions": [{"lang": "en", "value": "Netmaker makes networks with WireGuard. An Insecure Direct Object Reference (IDOR) vulnerability was found in versions prior to 0.17.1 and 0.18.6 in the user update function. By specifying another user's username, it was possible to update the other user's password. The issue is patched in 0.17.1 and fixed in 0.18.6. If Users are using 0.17.1, they should run `docker pull gravitl/netmaker:v0.17.1` and `docker-compose up -d`. This will switch them to the patched users. If users are using v0.18.0-0.18.5, they should upgrade to v0.18.6 or later. As a workaround, someone using version 0.17.1 can pull the latest docker image of the backend and restart the server."}], "references": [{"url": "https://github.com/gravitl/netmaker/commit/b3be57c65bf0bbfab43b66853c8e3637a43e2839", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/gravitl/netmaker/pull/2158", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/gravitl/netmaker/security/advisories/GHSA-256m-j5qw-38f4", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}