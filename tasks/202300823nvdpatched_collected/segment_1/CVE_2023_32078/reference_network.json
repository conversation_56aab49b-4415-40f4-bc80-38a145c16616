{"cve_id": "CVE-2023-32078", "cve_published_date": "2023-08-24T22:15:10.267", "cve_affected_cpes": [], "nodes": [{"node_id": "ac060a10-f9d6-4f08-8657-c6f7566bc9f5", "url": "https://nvd.nist.gov/vuln/detail/CVE-2023-32078", "title": "CVE-2023-32078", "node_type": "ROOT", "source": "ROOT", "depth": 0, "category_in_type": "", "importance_score": 1.0}, {"node_id": "3eecb802-7e23-4800-af90-05643172bd87", "url": "https://nvd.nist.gov", "title": "NVD", "node_type": "SOURCE", "source": "NVD", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "717bfa08-08c8-463d-a831-aa70c962eec7", "url": "https://github", "title": "GITHUB", "node_type": "SOURCE", "source": "GITHUB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "f85d3177-53f0-4b6c-b122-3618092e6e6f", "url": "https://gitlab", "title": "GITLAB", "node_type": "SOURCE", "source": "GITLAB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "0a447823-5528-41f8-a7f6-d30d245cd7f0", "url": "https://debian", "title": "DEBIAN", "node_type": "SOURCE", "source": "DEBIAN", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "553da115-607d-4102-9cd2-41feb4c3a664", "url": "https://redhat", "title": "REDHAT", "node_type": "SOURCE", "source": "REDHAT", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "2be1a394-769e-4caf-8fa9-ea3362530be6", "url": "https://apache", "title": "APACHE", "node_type": "SOURCE", "source": "APACHE", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "09fe8632-0c77-4545-8680-71e9005edd91", "url": "https://other.source", "title": "OTHER", "node_type": "SOURCE", "source": "OTHER", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "5cb2131d-4d8d-4d62-a9c4-1d59554ff00b", "url": "https://github.com/gravitl/netmaker/security/advisories/GHSA-256m-j5qw-38f4", "title": "Ghsa 256m j5qw 38f4", "node_type": "COMMON", "source": "GITHUB", "depth": 2, "category_in_type": "common_github_url", "importance_score": 1.0}, {"node_id": "6245cb17-d2ff-436d-930e-f824d074b7df", "url": "https://github.com/gravitl/netmaker/pull/2158", "title": "2158", "node_type": "ISSUE", "source": "GITHUB", "depth": 2, "category_in_type": "git_pr", "importance_score": 1.0}, {"node_id": "b3b57063-61f6-44a2-bd88-2721ef5b27ea", "url": "https://github.com/gravitl/netmaker/commit/b3be57c65bf0bbfab43b66853c8e3637a43e2839", "title": "B3be57c65bf0bbfab43b66853c8e3637a43e2839", "node_type": "PATCH", "source": "GITHUB", "depth": 2, "category_in_type": "git_commit", "importance_score": 1.0}, {"node_id": "d2c1d464-b331-438e-b2f3-8c4a69ce32a0", "url": "https://github.com/gravitl/netmaker/pull/2158/commits/d82e3a9b9e222fb9f4f4698ca278e011bd068f5d", "title": "", "node_type": "PATCH", "source": "GITHUB", "depth": 3, "category_in_type": "git_commit", "importance_score": 0.0}, {"node_id": "913ead2f-c678-4399-bcd5-cb69d68d5614", "url": "https://github.com/gravitl/netmaker/pull/2158/commits/c2a4cb1145a59636e597f2332275814ade064a31", "title": "", "node_type": "PATCH", "source": "GITHUB", "depth": 3, "category_in_type": "git_commit", "importance_score": 0.0}, {"node_id": "25956003-a8e7-4cd9-8e6a-b5d8daac1cb6", "url": "https://github.com/golang/vulndb/issues/2023", "title": "x/vulndb: potential Go vuln in github.com/gravitl/netmaker: CVE-2023-32078 · Issue #2023 · golang/vulndb · GitHub", "node_type": "ISSUE", "source": "GITHUB", "depth": 3, "category_in_type": "git_issue", "importance_score": 0.0}, {"node_id": "070d90c3-2f39-40df-a3bb-059a91650ea6", "url": "https://github.com/gravitl/netmaker/pull/2158/commits", "title": "add checks to user update processing by <PERSON><PERSON><PERSON><PERSON> · Pull Request #2158 · gravitl/netmaker · GitHub", "node_type": "ISSUE", "source": "GITHUB", "depth": 3, "category_in_type": "git_pr", "importance_score": 0.0}, {"node_id": "534281b1-ebec-4733-b17a-9d7987ae7861", "url": "https://github.com/gravitl/netmaker/pull/2158/checks", "title": "add checks to user update processing by <PERSON><PERSON><PERSON><PERSON> · Pull Request #2158 · gravitl/netmaker · GitHub", "node_type": "ISSUE", "source": "GITHUB", "depth": 3, "category_in_type": "git_pr", "importance_score": 0.0}, {"node_id": "2930cc9b-9bd2-4ea3-abdc-a313bc13b347", "url": "https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax", "title": "", "node_type": "COMMON", "source": "GITHUB", "depth": 3, "category_in_type": "common_github_url", "importance_score": 0.0}, {"node_id": "46609376-0fc6-47f0-b54e-6db409baf5de", "url": "https://github.com/gravitl/netmaker/pull/{{ revealButtonHref }}", "title": "", "node_type": "COMMON", "source": "GITHUB", "depth": 3, "category_in_type": "common_github_url", "importance_score": 0.0}, {"node_id": "4126a19c-a88c-48dc-9cc1-7c9b659b075d", "url": "https://github.com/golang/vulndb/issues/328", "title": "x/vulndb: potential Go vuln in github.com/gravitl/netmaker: CVE-2022-23650 · Issue #328 · golang/vulndb · GitHub", "node_type": "ISSUE", "source": "GITHUB", "depth": 4, "category_in_type": "git_issue", "importance_score": 0.0}, {"node_id": "635a4b9f-f664-4314-9688-816565f2c6c0", "url": "https://github.com/golang/vulndb/issues/561", "title": "x/vulndb: potential Go vuln in github.com/gravitl/netmaker: GHSA-6rrw-4fm9-rghv · Issue #561 · golang/vulndb · GitHub", "node_type": "ISSUE", "source": "GITHUB", "depth": 4, "category_in_type": "git_issue", "importance_score": 0.0}, {"node_id": "3c51510b-198d-4a35-a5aa-14369722119b", "url": "https://github.com/golang/vulndb", "title": "", "node_type": "REPO", "source": "GITHUB", "depth": 4, "category_in_type": "github_repo", "importance_score": 0.0}, {"node_id": "56f9942d-2fc8-45a3-8cd9-b0672d315168", "url": "https://github.com/timothy-king", "title": "", "node_type": "COMMON", "source": "GITHUB", "depth": 4, "category_in_type": "common_github_url", "importance_score": 0.0}, {"node_id": "3e468dd9-e0e1-46b5-a153-7cc06ecae35d", "url": "https://github.com/golang/vulndb/blob/master/doc/triage.md", "title": "", "node_type": "COMMON", "source": "GITHUB", "depth": 4, "category_in_type": "", "importance_score": 0.0}, {"node_id": "ae88c285-b221-45ce-8abd-6f4babc4cff1", "url": "https://github.com/golang/vulndb/issues/github.com/gravitl/netmaker", "title": "", "node_type": "COMMON", "source": "GITHUB", "depth": 5, "category_in_type": "common_github_url", "importance_score": 0.0}, {"node_id": "95be4b70-56b0-4d75-847d-d5d83dbac5a0", "url": "https://github.com/golang", "title": "", "node_type": "COMMON", "source": "GITHUB", "depth": 5, "category_in_type": "common_github_url", "importance_score": 0.0}, {"node_id": "600bc17b-b4c5-411b-a145-cfbdafc92c45", "url": "https://github.com/golang/vulndb/issues", "title": "", "node_type": "COMMON", "source": "GITHUB", "depth": 5, "category_in_type": "common_github_url", "importance_score": 0.0}, {"node_id": "e7ffce2c-7932-4f8a-9b51-55d13c4964cc", "url": "https://github.com/tatianab", "title": "", "node_type": "COMMON", "source": "GITHUB", "depth": 5, "category_in_type": "common_github_url", "importance_score": 0.0}, {"node_id": "538ff3d2-568d-409c-b3d9-e8a20f16b80c", "url": "https://github.com/gravitl/netmaker/commits", "title": "", "node_type": "COMMON", "source": "GITHUB", "depth": 4, "category_in_type": "common_github_url", "importance_score": 0.0}, {"node_id": "bd7a8b65-9685-43bf-94f2-5a7c3b679e54", "url": "https://github.com/gravitl/netmaker/tree/d82e3a9b9e222fb9f4f4698ca278e011bd068f5d", "title": "", "node_type": "COMMON", "source": "GITHUB", "depth": 4, "category_in_type": "common_github_url", "importance_score": 0.0}, {"node_id": "456a4a45-1a26-4837-86e6-6cb0094e9bff", "url": "https://github.com/advisories", "title": "", "node_type": "COMMON", "source": "GITHUB", "depth": 3, "category_in_type": "common_github_url", "importance_score": 0.0}, {"node_id": "3816cd4e-96e4-46b9-bf61-f3e1b4c2e075", "url": "https://github.com/rootxharsh", "title": "", "node_type": "COMMON", "source": "GITHUB", "depth": 3, "category_in_type": "common_github_url", "importance_score": 0.0}], "edges": [{"edge_id": "6fdc702a-229c-483a-9ce5-670cfad7a985", "source_id": "ac060a10-f9d6-4f08-8657-c6f7566bc9f5", "target_id": "3eecb802-7e23-4800-af90-05643172bd87", "weight": 0.8, "description": "来源"}, {"edge_id": "18d9ae83-547c-4af5-b7fe-f7fd97689554", "source_id": "ac060a10-f9d6-4f08-8657-c6f7566bc9f5", "target_id": "717bfa08-08c8-463d-a831-aa70c962eec7", "weight": 0.8, "description": "来源"}, {"edge_id": "d8efa91b-7241-47e5-b041-04367327ea9e", "source_id": "ac060a10-f9d6-4f08-8657-c6f7566bc9f5", "target_id": "f85d3177-53f0-4b6c-b122-3618092e6e6f", "weight": 0.8, "description": "来源"}, {"edge_id": "b6818862-04e3-4b4f-bbe3-f2dc1f338fa4", "source_id": "ac060a10-f9d6-4f08-8657-c6f7566bc9f5", "target_id": "0a447823-5528-41f8-a7f6-d30d245cd7f0", "weight": 0.8, "description": "来源"}, {"edge_id": "6504eef3-2364-4f4c-8905-062b2ea36517", "source_id": "ac060a10-f9d6-4f08-8657-c6f7566bc9f5", "target_id": "553da115-607d-4102-9cd2-41feb4c3a664", "weight": 0.8, "description": "来源"}, {"edge_id": "758f3982-e9f9-45bc-8c72-a15741834c28", "source_id": "ac060a10-f9d6-4f08-8657-c6f7566bc9f5", "target_id": "2be1a394-769e-4caf-8fa9-ea3362530be6", "weight": 0.8, "description": "来源"}, {"edge_id": "5154d221-e3fd-418c-8250-6fe76ba70073", "source_id": "ac060a10-f9d6-4f08-8657-c6f7566bc9f5", "target_id": "09fe8632-0c77-4545-8680-71e9005edd91", "weight": 0.5, "description": "其他来源"}, {"edge_id": "6c9a81f3-b703-4566-a43b-7ec8d967c535", "source_id": "3eecb802-7e23-4800-af90-05643172bd87", "target_id": "ac060a10-f9d6-4f08-8657-c6f7566bc9f5", "weight": 1.0, "description": "引用"}, {"edge_id": "9c7ef236-879a-412d-8cc2-9f706c1eb0b3", "source_id": "717bfa08-08c8-463d-a831-aa70c962eec7", "target_id": "5cb2131d-4d8d-4d62-a9c4-1d59554ff00b", "weight": 1.0, "description": "引用"}, {"edge_id": "38379868-7b76-4e00-9e3f-0ecf154f6199", "source_id": "3eecb802-7e23-4800-af90-05643172bd87", "target_id": "5cb2131d-4d8d-4d62-a9c4-1d59554ff00b", "weight": 0.7, "description": "NVD引用"}, {"edge_id": "338a19f2-bea5-4c8c-ba4d-2d7360c29a28", "source_id": "717bfa08-08c8-463d-a831-aa70c962eec7", "target_id": "6245cb17-d2ff-436d-930e-f824d074b7df", "weight": 1.0, "description": "引用"}, {"edge_id": "0a22fa44-89b8-4602-8024-b32a4c56a41e", "source_id": "3eecb802-7e23-4800-af90-05643172bd87", "target_id": "6245cb17-d2ff-436d-930e-f824d074b7df", "weight": 0.7, "description": "NVD引用"}, {"edge_id": "693c998a-3b47-4a3d-a59f-d6940a969131", "source_id": "717bfa08-08c8-463d-a831-aa70c962eec7", "target_id": "b3b57063-61f6-44a2-bd88-2721ef5b27ea", "weight": 1.0, "description": "引用"}, {"edge_id": "84a76d2f-2a21-4dfb-8e2f-c38064d4d655", "source_id": "3eecb802-7e23-4800-af90-05643172bd87", "target_id": "b3b57063-61f6-44a2-bd88-2721ef5b27ea", "weight": 0.7, "description": "NVD引用"}, {"edge_id": "6f3f8bda-5b8b-437f-96be-0c0b5e6a9555", "source_id": "ac060a10-f9d6-4f08-8657-c6f7566bc9f5", "target_id": "b3b57063-61f6-44a2-bd88-2721ef5b27ea", "weight": 0.9, "description": "直接补丁"}, {"edge_id": "72ff5d0a-f550-4d88-9646-76372da22849", "source_id": "6245cb17-d2ff-436d-930e-f824d074b7df", "target_id": "d2c1d464-b331-438e-b2f3-8c4a69ce32a0", "weight": 1.5, "description": "PR中的提交"}, {"edge_id": "bc48f20a-1e1f-4466-8219-124a0f661697", "source_id": "717bfa08-08c8-463d-a831-aa70c962eec7", "target_id": "d2c1d464-b331-438e-b2f3-8c4a69ce32a0", "weight": 0.9, "description": "来源引用"}, {"edge_id": "46eb9dfc-51db-43e7-b8ba-6e770259ddde", "source_id": "ac060a10-f9d6-4f08-8657-c6f7566bc9f5", "target_id": "d2c1d464-b331-438e-b2f3-8c4a69ce32a0", "weight": 1.0, "description": "关联补丁"}, {"edge_id": "68355b9d-5d3f-48b9-9051-001466e11eaf", "source_id": "6245cb17-d2ff-436d-930e-f824d074b7df", "target_id": "913ead2f-c678-4399-bcd5-cb69d68d5614", "weight": 1.5, "description": "PR中的提交"}, {"edge_id": "7f899e56-5d44-4d38-8d5c-d24bc44ef4b4", "source_id": "717bfa08-08c8-463d-a831-aa70c962eec7", "target_id": "913ead2f-c678-4399-bcd5-cb69d68d5614", "weight": 0.9, "description": "来源引用"}, {"edge_id": "4c4e59cf-ec35-44cf-82da-ab38719e30f2", "source_id": "ac060a10-f9d6-4f08-8657-c6f7566bc9f5", "target_id": "913ead2f-c678-4399-bcd5-cb69d68d5614", "weight": 1.0, "description": "关联补丁"}, {"edge_id": "72fb1b49-00c5-43aa-9200-c235ac3bb48d", "source_id": "6245cb17-d2ff-436d-930e-f824d074b7df", "target_id": "25956003-a8e7-4cd9-8e6a-b5d8daac1cb6", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "dc2e4462-6722-4ed6-b5fe-6c0f2ca8b862", "source_id": "717bfa08-08c8-463d-a831-aa70c962eec7", "target_id": "25956003-a8e7-4cd9-8e6a-b5d8daac1cb6", "weight": 0.9, "description": "来源引用"}, {"edge_id": "d682eb41-6124-4dfc-9938-9c08189fa559", "source_id": "6245cb17-d2ff-436d-930e-f824d074b7df", "target_id": "070d90c3-2f39-40df-a3bb-059a91650ea6", "weight": 1.5, "description": "PR中的提交"}, {"edge_id": "4391cdb8-e8a8-4a6e-ab1b-d5ecbe0a1e71", "source_id": "717bfa08-08c8-463d-a831-aa70c962eec7", "target_id": "070d90c3-2f39-40df-a3bb-059a91650ea6", "weight": 0.9, "description": "来源引用"}, {"edge_id": "1c1641c2-b877-49c9-afaa-7fd7eb904c62", "source_id": "6245cb17-d2ff-436d-930e-f824d074b7df", "target_id": "534281b1-ebec-4733-b17a-9d7987ae7861", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "585c449e-90a8-475f-ae53-ba9e379dbd8f", "source_id": "717bfa08-08c8-463d-a831-aa70c962eec7", "target_id": "534281b1-ebec-4733-b17a-9d7987ae7861", "weight": 0.9, "description": "来源引用"}, {"edge_id": "b95ec4a2-ec13-40be-9306-7107deaccce9", "source_id": "6245cb17-d2ff-436d-930e-f824d074b7df", "target_id": "2930cc9b-9bd2-4ea3-abdc-a313bc13b347", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "075865c7-29db-484a-bbf8-053b5988d8e4", "source_id": "717bfa08-08c8-463d-a831-aa70c962eec7", "target_id": "2930cc9b-9bd2-4ea3-abdc-a313bc13b347", "weight": 0.9, "description": "来源引用"}, {"edge_id": "05d9d737-1660-44be-8ab8-627d092170e0", "source_id": "6245cb17-d2ff-436d-930e-f824d074b7df", "target_id": "46609376-0fc6-47f0-b54e-6db409baf5de", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "34caa324-5ddf-41db-95c0-ef69b2c53902", "source_id": "717bfa08-08c8-463d-a831-aa70c962eec7", "target_id": "46609376-0fc6-47f0-b54e-6db409baf5de", "weight": 0.9, "description": "来源引用"}, {"edge_id": "b47b9dcd-684c-4b72-87d7-2f0c0755cc14", "source_id": "25956003-a8e7-4cd9-8e6a-b5d8daac1cb6", "target_id": "4126a19c-a88c-48dc-9cc1-7c9b659b075d", "weight": 0.25, "description": "引用"}, {"edge_id": "79dea95c-a990-4bb3-a04c-0acf3d40005e", "source_id": "717bfa08-08c8-463d-a831-aa70c962eec7", "target_id": "4126a19c-a88c-48dc-9cc1-7c9b659b075d", "weight": 0.9, "description": "来源引用"}, {"edge_id": "631f8f99-ff31-4e9b-ae85-426ec81593f5", "source_id": "25956003-a8e7-4cd9-8e6a-b5d8daac1cb6", "target_id": "635a4b9f-f664-4314-9688-816565f2c6c0", "weight": 0.25, "description": "引用"}, {"edge_id": "c08a21ec-a1b9-495a-9207-d43416cac0df", "source_id": "717bfa08-08c8-463d-a831-aa70c962eec7", "target_id": "635a4b9f-f664-4314-9688-816565f2c6c0", "weight": 0.9, "description": "来源引用"}, {"edge_id": "808ed3d4-c80e-4b8b-8999-793a79feb5c3", "source_id": "25956003-a8e7-4cd9-8e6a-b5d8daac1cb6", "target_id": "3c51510b-198d-4a35-a5aa-14369722119b", "weight": 0.25, "description": "引用"}, {"edge_id": "7b005c18-f23a-4c81-9a8b-6293c73af36a", "source_id": "717bfa08-08c8-463d-a831-aa70c962eec7", "target_id": "3c51510b-198d-4a35-a5aa-14369722119b", "weight": 0.9, "description": "来源引用"}, {"edge_id": "eae2b3cb-7daa-43d4-bdb2-a9b244cd0bc8", "source_id": "25956003-a8e7-4cd9-8e6a-b5d8daac1cb6", "target_id": "56f9942d-2fc8-45a3-8cd9-b0672d315168", "weight": 0.25, "description": "引用"}, {"edge_id": "82d75084-728d-4866-bb35-f7bda4699b36", "source_id": "717bfa08-08c8-463d-a831-aa70c962eec7", "target_id": "56f9942d-2fc8-45a3-8cd9-b0672d315168", "weight": 0.9, "description": "来源引用"}, {"edge_id": "ebe2371f-6636-4cbb-86c4-57f292191b2c", "source_id": "25956003-a8e7-4cd9-8e6a-b5d8daac1cb6", "target_id": "3e468dd9-e0e1-46b5-a153-7cc06ecae35d", "weight": 0.25, "description": "引用"}, {"edge_id": "95356b63-cc81-4176-8318-f0e8c6854e74", "source_id": "717bfa08-08c8-463d-a831-aa70c962eec7", "target_id": "3e468dd9-e0e1-46b5-a153-7cc06ecae35d", "weight": 0.9, "description": "来源引用"}, {"edge_id": "9c679eda-1270-41f1-90c2-a546ca42ff78", "source_id": "4126a19c-a88c-48dc-9cc1-7c9b659b075d", "target_id": "ae88c285-b221-45ce-8abd-6f4babc4cff1", "weight": 0.2, "description": "引用"}, {"edge_id": "1b86c80b-c9ae-464f-b99f-3d3927f82c8c", "source_id": "717bfa08-08c8-463d-a831-aa70c962eec7", "target_id": "ae88c285-b221-45ce-8abd-6f4babc4cff1", "weight": 0.9, "description": "来源引用"}, {"edge_id": "e16d05c0-4b55-484e-89a8-03eb5b6215d8", "source_id": "4126a19c-a88c-48dc-9cc1-7c9b659b075d", "target_id": "95be4b70-56b0-4d75-847d-d5d83dbac5a0", "weight": 0.2, "description": "引用"}, {"edge_id": "7d45879c-c823-4d4b-b99b-b66645f6bb74", "source_id": "717bfa08-08c8-463d-a831-aa70c962eec7", "target_id": "95be4b70-56b0-4d75-847d-d5d83dbac5a0", "weight": 0.9, "description": "来源引用"}, {"edge_id": "0bb743ef-4f0b-4e3c-aca9-ec8bb7a4e185", "source_id": "635a4b9f-f664-4314-9688-816565f2c6c0", "target_id": "600bc17b-b4c5-411b-a145-cfbdafc92c45", "weight": 0.2, "description": "引用"}, {"edge_id": "71562f9a-215e-4ca2-8b32-5f409ebf21f9", "source_id": "717bfa08-08c8-463d-a831-aa70c962eec7", "target_id": "600bc17b-b4c5-411b-a145-cfbdafc92c45", "weight": 0.9, "description": "来源引用"}, {"edge_id": "fdcd0bdb-46a2-4dbf-a046-db68f970f174", "source_id": "635a4b9f-f664-4314-9688-816565f2c6c0", "target_id": "e7ffce2c-7932-4f8a-9b51-55d13c4964cc", "weight": 0.2, "description": "引用"}, {"edge_id": "149adcec-02f7-40a6-861c-4d90c87eca9e", "source_id": "717bfa08-08c8-463d-a831-aa70c962eec7", "target_id": "e7ffce2c-7932-4f8a-9b51-55d13c4964cc", "weight": 0.9, "description": "来源引用"}, {"edge_id": "8b15d6ab-0337-4443-aed7-76fbfe21d508", "source_id": "070d90c3-2f39-40df-a3bb-059a91650ea6", "target_id": "538ff3d2-568d-409c-b3d9-e8a20f16b80c", "weight": 0.25, "description": "引用"}, {"edge_id": "6b0ce1ab-bc78-4d51-881e-a1045f912c2c", "source_id": "717bfa08-08c8-463d-a831-aa70c962eec7", "target_id": "538ff3d2-568d-409c-b3d9-e8a20f16b80c", "weight": 0.9, "description": "来源引用"}, {"edge_id": "f8524c21-f665-46ac-afa0-3b0868f6b7a4", "source_id": "070d90c3-2f39-40df-a3bb-059a91650ea6", "target_id": "bd7a8b65-9685-43bf-94f2-5a7c3b679e54", "weight": 0.25, "description": "引用"}, {"edge_id": "65ae2ddd-0761-420f-a1ea-7722bb0a2dd6", "source_id": "717bfa08-08c8-463d-a831-aa70c962eec7", "target_id": "bd7a8b65-9685-43bf-94f2-5a7c3b679e54", "weight": 0.9, "description": "来源引用"}, {"edge_id": "85b1228c-1300-46ca-ac90-896e0c39d632", "source_id": "5cb2131d-4d8d-4d62-a9c4-1d59554ff00b", "target_id": "456a4a45-1a26-4837-86e6-6cb0094e9bff", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "7be19b84-b27f-4945-8f3e-4317a9f8fb1a", "source_id": "717bfa08-08c8-463d-a831-aa70c962eec7", "target_id": "456a4a45-1a26-4837-86e6-6cb0094e9bff", "weight": 0.9, "description": "来源引用"}, {"edge_id": "8bcacf41-6a33-46cb-a092-db3441483ed5", "source_id": "5cb2131d-4d8d-4d62-a9c4-1d59554ff00b", "target_id": "3816cd4e-96e4-46b9-bf61-f3e1b4c2e075", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "1debcbbc-194e-4063-b2af-857313f7552f", "source_id": "717bfa08-08c8-463d-a831-aa70c962eec7", "target_id": "3816cd4e-96e4-46b9-bf61-f3e1b4c2e075", "weight": 0.9, "description": "来源引用"}]}