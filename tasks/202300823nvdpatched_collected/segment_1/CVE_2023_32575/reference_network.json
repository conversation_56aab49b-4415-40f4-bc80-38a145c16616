{"cve_id": "CVE-2023-32575", "cve_published_date": "2023-08-25T11:15:08.177", "cve_affected_cpes": [], "nodes": [{"node_id": "c4055554-94d0-42b5-a9e4-0c59cb861b26", "url": "https://nvd.nist.gov/vuln/detail/CVE-2023-32575", "title": "CVE-2023-32575", "node_type": "ROOT", "source": "ROOT", "depth": 0, "category_in_type": "", "importance_score": 1.0}, {"node_id": "d7860651-6a2d-45f5-843a-fd296d801a7c", "url": "https://nvd.nist.gov", "title": "NVD", "node_type": "SOURCE", "source": "NVD", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "944e8577-4805-4e49-a7bc-66ee46d0f682", "url": "https://github", "title": "GITHUB", "node_type": "SOURCE", "source": "GITHUB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "7386171a-2d09-4710-91ef-c6035e64227a", "url": "https://gitlab", "title": "GITLAB", "node_type": "SOURCE", "source": "GITLAB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "cecca3be-3bb9-4b33-bba0-bb57cbf6e9ee", "url": "https://debian", "title": "DEBIAN", "node_type": "SOURCE", "source": "DEBIAN", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "9bfc03c3-8f6f-4db6-b4af-914738de557a", "url": "https://redhat", "title": "REDHAT", "node_type": "SOURCE", "source": "REDHAT", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "0d1eb35a-c934-44ae-84f0-c58487a7c016", "url": "https://apache", "title": "APACHE", "node_type": "SOURCE", "source": "APACHE", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "84cd4be3-e11a-4f2c-a5b6-06953ccf3557", "url": "https://other.source", "title": "OTHER", "node_type": "SOURCE", "source": "OTHER", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "1355d803-2f23-43f2-892f-d9ac95e16926", "url": "https://patchstack.com/database/vulnerability/product-page-shipping-calculator-for-woocommerce/wordpress-product-page-shipping-calculator-for-woocommerce-plugin-1-3-25-cross-site-scripting-xss-vulnerability", "title": "Wordpress product page shipping calculator for woocommerce plugin 1 3 25 cross site scripting xss vulnerability", "node_type": "COMMON", "source": "OTHER", "depth": 2, "category_in_type": "common_url", "importance_score": 1.0}, {"node_id": "aa46f90c-8fb8-473a-b02f-e66d872a8458", "url": "https://patchstack.com/wordpress-security", "title": "", "node_type": "COMMON", "source": "OTHER", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}, {"node_id": "77b10bdd-a16c-4407-8bf7-ee5131e7865d", "url": "https://patchstack.com/for-hosts", "title": "", "node_type": "COMMON", "source": "OTHER", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}], "edges": [{"edge_id": "8b673ce3-a884-413d-a1b0-69a4808099be", "source_id": "c4055554-94d0-42b5-a9e4-0c59cb861b26", "target_id": "d7860651-6a2d-45f5-843a-fd296d801a7c", "weight": 0.8, "description": "来源"}, {"edge_id": "6c067229-65dd-4bf3-a5ec-9979cfb8053c", "source_id": "c4055554-94d0-42b5-a9e4-0c59cb861b26", "target_id": "944e8577-4805-4e49-a7bc-66ee46d0f682", "weight": 0.8, "description": "来源"}, {"edge_id": "945346fa-3d19-4b2f-b0fa-68f58fe57ad0", "source_id": "c4055554-94d0-42b5-a9e4-0c59cb861b26", "target_id": "7386171a-2d09-4710-91ef-c6035e64227a", "weight": 0.8, "description": "来源"}, {"edge_id": "2c8a4ae6-d642-4fd7-8c11-caf71d2c08aa", "source_id": "c4055554-94d0-42b5-a9e4-0c59cb861b26", "target_id": "cecca3be-3bb9-4b33-bba0-bb57cbf6e9ee", "weight": 0.8, "description": "来源"}, {"edge_id": "6761dada-3fd0-4661-adc3-0d94d958d4f3", "source_id": "c4055554-94d0-42b5-a9e4-0c59cb861b26", "target_id": "9bfc03c3-8f6f-4db6-b4af-914738de557a", "weight": 0.8, "description": "来源"}, {"edge_id": "e7198dcc-ef60-4755-92ad-4ff8a76c2b72", "source_id": "c4055554-94d0-42b5-a9e4-0c59cb861b26", "target_id": "0d1eb35a-c934-44ae-84f0-c58487a7c016", "weight": 0.8, "description": "来源"}, {"edge_id": "5ccf4be1-e45e-43a2-9301-f9482918799e", "source_id": "c4055554-94d0-42b5-a9e4-0c59cb861b26", "target_id": "84cd4be3-e11a-4f2c-a5b6-06953ccf3557", "weight": 0.5, "description": "其他来源"}, {"edge_id": "3880b51f-1d96-41c4-bb06-e3cccff10fa7", "source_id": "d7860651-6a2d-45f5-843a-fd296d801a7c", "target_id": "c4055554-94d0-42b5-a9e4-0c59cb861b26", "weight": 1.0, "description": "引用"}, {"edge_id": "6d533aaf-3e78-4a82-9685-3cd352c67259", "source_id": "84cd4be3-e11a-4f2c-a5b6-06953ccf3557", "target_id": "1355d803-2f23-43f2-892f-d9ac95e16926", "weight": 1.0, "description": "引用"}, {"edge_id": "28fdf2be-1ba9-4562-b6b0-4389c4a8c20e", "source_id": "d7860651-6a2d-45f5-843a-fd296d801a7c", "target_id": "1355d803-2f23-43f2-892f-d9ac95e16926", "weight": 0.7, "description": "NVD引用"}, {"edge_id": "ae5eb913-5fd4-4e03-acaf-8cd008b9cb87", "source_id": "1355d803-2f23-43f2-892f-d9ac95e16926", "target_id": "aa46f90c-8fb8-473a-b02f-e66d872a8458", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "ae56b0b0-dfef-4372-9745-ddc0b356a367", "source_id": "1355d803-2f23-43f2-892f-d9ac95e16926", "target_id": "77b10bdd-a16c-4407-8bf7-ee5131e7865d", "weight": 0.3333333333333333, "description": "引用"}]}