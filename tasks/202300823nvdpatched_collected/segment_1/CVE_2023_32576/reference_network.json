{"cve_id": "CVE-2023-32576", "cve_published_date": "2023-08-25T09:15:08.477", "cve_affected_cpes": [], "nodes": [{"node_id": "b868e20e-f99a-4cee-8623-3605710eb242", "url": "https://nvd.nist.gov/vuln/detail/CVE-2023-32576", "title": "CVE-2023-32576", "node_type": "ROOT", "source": "ROOT", "depth": 0, "category_in_type": "", "importance_score": 1.0}, {"node_id": "d3c34cd2-459f-4fe0-8a60-f272e17f6732", "url": "https://nvd.nist.gov", "title": "NVD", "node_type": "SOURCE", "source": "NVD", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "e10af10a-50f6-4fa2-b267-d272f8cdf5b4", "url": "https://github", "title": "GITHUB", "node_type": "SOURCE", "source": "GITHUB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "e9b318a1-e567-4a06-b0a0-a4d9a4dc57c7", "url": "https://gitlab", "title": "GITLAB", "node_type": "SOURCE", "source": "GITLAB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "7e8022af-3c20-4780-a793-a5d001d18aa1", "url": "https://debian", "title": "DEBIAN", "node_type": "SOURCE", "source": "DEBIAN", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "3f525647-c952-434c-8250-c101e7ca47f9", "url": "https://redhat", "title": "REDHAT", "node_type": "SOURCE", "source": "REDHAT", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "6a168962-8203-49ab-8bd8-0519acdeb380", "url": "https://apache", "title": "APACHE", "node_type": "SOURCE", "source": "APACHE", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "1af9d0a6-0bef-4d26-b99d-2754296aaa5f", "url": "https://other.source", "title": "OTHER", "node_type": "SOURCE", "source": "OTHER", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "bc82c20c-bd48-4bc5-a8c8-2ef5c104b26f", "url": "https://patchstack.com/database/vulnerability/locatoraid/wordpress-locatoraid-store-locator-plugin-3-9-18-cross-site-scripting-xss-vulnerability", "title": "Wordpress locatoraid store locator plugin 3 9 18 cross site scripting xss vulnerability", "node_type": "COMMON", "source": "OTHER", "depth": 2, "category_in_type": "common_url", "importance_score": 1.0}, {"node_id": "b705f014-1a79-4fd8-926b-82057006e615", "url": "https://patchstack.com/wordpress-security", "title": "", "node_type": "COMMON", "source": "OTHER", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}, {"node_id": "d7a5a07f-ab9c-4aef-a3d5-b9f877630b94", "url": "https://patchstack.com/for-hosts", "title": "", "node_type": "COMMON", "source": "OTHER", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}], "edges": [{"edge_id": "e5361223-7b76-4b67-97ec-611100111818", "source_id": "b868e20e-f99a-4cee-8623-3605710eb242", "target_id": "d3c34cd2-459f-4fe0-8a60-f272e17f6732", "weight": 0.8, "description": "来源"}, {"edge_id": "e11ae045-f2dd-4b71-b6d5-51f1ca249c83", "source_id": "b868e20e-f99a-4cee-8623-3605710eb242", "target_id": "e10af10a-50f6-4fa2-b267-d272f8cdf5b4", "weight": 0.8, "description": "来源"}, {"edge_id": "042a2433-2b51-4289-91ac-cafe2e0f1f27", "source_id": "b868e20e-f99a-4cee-8623-3605710eb242", "target_id": "e9b318a1-e567-4a06-b0a0-a4d9a4dc57c7", "weight": 0.8, "description": "来源"}, {"edge_id": "85ea43fb-6aeb-4ec6-90cc-ba2425f07eac", "source_id": "b868e20e-f99a-4cee-8623-3605710eb242", "target_id": "7e8022af-3c20-4780-a793-a5d001d18aa1", "weight": 0.8, "description": "来源"}, {"edge_id": "5874a321-86ef-4ef8-a64e-593cc8e4f5d0", "source_id": "b868e20e-f99a-4cee-8623-3605710eb242", "target_id": "3f525647-c952-434c-8250-c101e7ca47f9", "weight": 0.8, "description": "来源"}, {"edge_id": "f02ff8f9-b64a-46a9-a5a5-d21f2bb6f4a8", "source_id": "b868e20e-f99a-4cee-8623-3605710eb242", "target_id": "6a168962-8203-49ab-8bd8-0519acdeb380", "weight": 0.8, "description": "来源"}, {"edge_id": "86cb0bd1-d7fd-4e36-927e-de63f7cf5589", "source_id": "b868e20e-f99a-4cee-8623-3605710eb242", "target_id": "1af9d0a6-0bef-4d26-b99d-2754296aaa5f", "weight": 0.5, "description": "其他来源"}, {"edge_id": "66893939-b6d6-48f2-9c8e-00262fb6c1ca", "source_id": "d3c34cd2-459f-4fe0-8a60-f272e17f6732", "target_id": "b868e20e-f99a-4cee-8623-3605710eb242", "weight": 1.0, "description": "引用"}, {"edge_id": "deaf53c2-46d9-4918-9225-09749032d1f5", "source_id": "1af9d0a6-0bef-4d26-b99d-2754296aaa5f", "target_id": "bc82c20c-bd48-4bc5-a8c8-2ef5c104b26f", "weight": 1.0, "description": "引用"}, {"edge_id": "d070aa2c-6e5a-4679-a7a6-8d79c13abcbc", "source_id": "d3c34cd2-459f-4fe0-8a60-f272e17f6732", "target_id": "bc82c20c-bd48-4bc5-a8c8-2ef5c104b26f", "weight": 0.7, "description": "NVD引用"}, {"edge_id": "ef666233-21db-4f82-8238-9587748a8f94", "source_id": "bc82c20c-bd48-4bc5-a8c8-2ef5c104b26f", "target_id": "b705f014-1a79-4fd8-926b-82057006e615", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "4f95ccff-c631-462a-b8da-0c67a3fd28f1", "source_id": "bc82c20c-bd48-4bc5-a8c8-2ef5c104b26f", "target_id": "d7a5a07f-ab9c-4aef-a3d5-b9f877630b94", "weight": 0.3333333333333333, "description": "引用"}]}