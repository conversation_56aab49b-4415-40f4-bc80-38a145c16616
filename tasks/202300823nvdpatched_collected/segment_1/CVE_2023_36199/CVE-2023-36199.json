{"cve_id": "CVE-2023-36199", "published_date": "2023-08-25T20:15:08.253", "last_modified_date": "2024-11-21T08:09:24.440", "descriptions": [{"lang": "en", "value": "An issue in skalenetwork sgxwallet v.1.9.0 and below allows an attacker to cause a denial of service via the trustedGenerateEcdsaKey component."}, {"lang": "es", "value": "Un problema en sgxwallet v.1.9.0 e inferiores de skalenetwork, permite a un atacante causar una denegación de servicio a través del componente trustedGenerateEcdsaKey.\n"}], "references": [{"url": "https://github.com/skalenetwork/sgxwallet/issues/419", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Third Party Advisory"]}]}