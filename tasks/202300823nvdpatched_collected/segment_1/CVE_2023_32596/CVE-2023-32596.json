{"cve_id": "CVE-2023-32596", "published_date": "2023-08-25T11:15:08.497", "last_modified_date": "2024-11-21T08:03:40.210", "descriptions": [{"lang": "en", "value": "Auth. (admin+) Stored Cross-Site Scripting (XSS) vulnerability in Wolfgang Ertl weebotLite plugin <= 1.0.0 versions."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Scripting (XSS) Almacenada weebotLite de Wolfgang Ertl que afecta a las versiones 1.0.0 e inferiores. Para explotar esta vulnerabilidad vulnerabilidad hace falta estar autenticado y tener permisos de administrador o superior."}], "references": [{"url": "https://patchstack.com/database/vulnerability/weebotlite/wordpress-weebotlite-plugin-1-0-0-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}