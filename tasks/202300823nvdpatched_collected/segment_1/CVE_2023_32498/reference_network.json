{"cve_id": "CVE-2023-32498", "cve_published_date": "2023-08-23T14:15:08.887", "cve_affected_cpes": [], "nodes": [{"node_id": "bed0f8a7-18a9-4efc-8431-69a822f670e0", "url": "https://nvd.nist.gov/vuln/detail/CVE-2023-32498", "title": "CVE-2023-32498", "node_type": "ROOT", "source": "ROOT", "depth": 0, "category_in_type": "", "importance_score": 1.0}, {"node_id": "d01a5d56-c703-4dca-8a87-1275afd2de3f", "url": "https://nvd.nist.gov", "title": "NVD", "node_type": "SOURCE", "source": "NVD", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "ecedf751-2aa4-4193-89ae-47256adb031f", "url": "https://github", "title": "GITHUB", "node_type": "SOURCE", "source": "GITHUB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "0951912a-eb50-4599-b670-d801df1e7bf9", "url": "https://gitlab", "title": "GITLAB", "node_type": "SOURCE", "source": "GITLAB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "ff6121be-ea1e-4854-b821-969753244120", "url": "https://debian", "title": "DEBIAN", "node_type": "SOURCE", "source": "DEBIAN", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "cb4d680e-a697-4dba-89cf-11c546f191b0", "url": "https://redhat", "title": "REDHAT", "node_type": "SOURCE", "source": "REDHAT", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "8c6007f5-15e4-44d8-98b4-f13c36866c22", "url": "https://apache", "title": "APACHE", "node_type": "SOURCE", "source": "APACHE", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "58068598-8ebb-4a90-aa8e-95e9dd02a913", "url": "https://other.source", "title": "OTHER", "node_type": "SOURCE", "source": "OTHER", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "172f9554-aca7-4aac-b098-0525bc2a8dae", "url": "https://patchstack.com/database/vulnerability/easy-form/wordpress-easy-form-by-ays-plugin-1-2-0-cross-site-scripting-xss-vulnerability", "title": "Wordpress easy form by ays plugin 1 2 0 cross site scripting xss vulnerability", "node_type": "COMMON", "source": "OTHER", "depth": 2, "category_in_type": "common_url", "importance_score": 1.0}, {"node_id": "f7b20f1d-1e14-4a55-a55c-87400c7c8ab2", "url": "https://patchstack.com/wordpress-security", "title": "", "node_type": "COMMON", "source": "OTHER", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}, {"node_id": "5ea93af7-c0f4-4fec-8466-e1e31c559ef6", "url": "https://patchstack.com/for-hosts", "title": "", "node_type": "COMMON", "source": "OTHER", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}], "edges": [{"edge_id": "6e8a4773-de85-4fa8-8b9c-591cec978198", "source_id": "bed0f8a7-18a9-4efc-8431-69a822f670e0", "target_id": "d01a5d56-c703-4dca-8a87-1275afd2de3f", "weight": 0.8, "description": "来源"}, {"edge_id": "0a6e94bb-8e08-49f3-94a7-61965be18541", "source_id": "bed0f8a7-18a9-4efc-8431-69a822f670e0", "target_id": "ecedf751-2aa4-4193-89ae-47256adb031f", "weight": 0.8, "description": "来源"}, {"edge_id": "98bff79a-221d-4c7b-90e5-de3edcac293e", "source_id": "bed0f8a7-18a9-4efc-8431-69a822f670e0", "target_id": "0951912a-eb50-4599-b670-d801df1e7bf9", "weight": 0.8, "description": "来源"}, {"edge_id": "e22f5b1a-622d-4d20-9710-8d3cb6c7cbaa", "source_id": "bed0f8a7-18a9-4efc-8431-69a822f670e0", "target_id": "ff6121be-ea1e-4854-b821-969753244120", "weight": 0.8, "description": "来源"}, {"edge_id": "06e56eca-8dfd-4fdf-b5de-e2eb823e1055", "source_id": "bed0f8a7-18a9-4efc-8431-69a822f670e0", "target_id": "cb4d680e-a697-4dba-89cf-11c546f191b0", "weight": 0.8, "description": "来源"}, {"edge_id": "a099b435-cfd6-48fa-a978-a338662d9edc", "source_id": "bed0f8a7-18a9-4efc-8431-69a822f670e0", "target_id": "8c6007f5-15e4-44d8-98b4-f13c36866c22", "weight": 0.8, "description": "来源"}, {"edge_id": "b88cca2a-bd4b-4a5e-8a6c-a10a991f8ae7", "source_id": "bed0f8a7-18a9-4efc-8431-69a822f670e0", "target_id": "58068598-8ebb-4a90-aa8e-95e9dd02a913", "weight": 0.5, "description": "其他来源"}, {"edge_id": "630993be-625f-4829-9d41-84fda8f7c7f7", "source_id": "d01a5d56-c703-4dca-8a87-1275afd2de3f", "target_id": "bed0f8a7-18a9-4efc-8431-69a822f670e0", "weight": 1.0, "description": "引用"}, {"edge_id": "22fdb5fd-e487-4c83-b39a-62962b7c9d19", "source_id": "58068598-8ebb-4a90-aa8e-95e9dd02a913", "target_id": "172f9554-aca7-4aac-b098-0525bc2a8dae", "weight": 1.0, "description": "引用"}, {"edge_id": "c94091b2-9f3d-4829-bed2-b4d62b858eaf", "source_id": "d01a5d56-c703-4dca-8a87-1275afd2de3f", "target_id": "172f9554-aca7-4aac-b098-0525bc2a8dae", "weight": 0.7, "description": "NVD引用"}, {"edge_id": "903d9d91-6894-4254-9fd1-af6cecfc2d0b", "source_id": "172f9554-aca7-4aac-b098-0525bc2a8dae", "target_id": "f7b20f1d-1e14-4a55-a55c-87400c7c8ab2", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "ac31f9db-81d5-4b4f-85d0-ca3f185e398a", "source_id": "172f9554-aca7-4aac-b098-0525bc2a8dae", "target_id": "5ea93af7-c0f4-4fec-8466-e1e31c559ef6", "weight": 0.3333333333333333, "description": "引用"}]}