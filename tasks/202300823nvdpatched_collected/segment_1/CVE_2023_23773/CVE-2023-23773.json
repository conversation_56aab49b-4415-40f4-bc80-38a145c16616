{"cve_id": "CVE-2023-23773", "published_date": "2023-08-29T09:15:09.330", "last_modified_date": "2024-11-21T07:46:48.100", "descriptions": [{"lang": "en", "value": "Motorola EBTS/MBTS Base Radio fails to check firmware authenticity. The Motorola MBTS Base Radio lacks cryptographic signature validation for firmware update packages, allowing an authenticated attacker to gain arbitrary code execution, extract secret key material, and/or leave a persistent implant on the device."}, {"lang": "es", "value": "La radio base Motorola EBTS/MBTS no puede verificar la autenticidad del firmware. La radio base MBTS de Motorola carece de validación de firma criptográfica para los paquetes de actualización de firmware, lo que permite a un atacante autenticado obtener la ejecución de código arbitrario, extraer material de clave secreta y/o dejar un implante persistente en el dispositivo."}], "references": [{"url": "https://tetraburst.com/", "source": "<EMAIL>", "tags": ["Not Applicable"]}]}