{"cve_id": "CVE-2023-27604", "published_date": "2023-08-28T08:15:14.697", "last_modified_date": "2024-11-21T07:53:14.847", "descriptions": [{"lang": "en", "value": "Apache Airflow Sqoop Provider, versions before 4.0.0, is affected by a vulnerability that allows an attacker pass parameters with the connections, which makes it possible to implement RCE attacks via ‘sqoop import --connect’, obtain airflow server permissions, etc. The attacker needs to be logged in and have authorization (permissions) to create/edit connections.\n\n It is recommended to upgrade to a version that is not affected.\nThis issue was reported independently by happyhacking-k, <PERSON><PERSON> and <PERSON><PERSON> of Caiji Sec Team also reported it."}], "references": [{"url": "https://github.com/apache/airflow/pull/33039", "source": "<EMAIL>", "tags": ["Patch", "Vendor Advisory"]}, {"url": "https://lists.apache.org/thread/lswlxf11do51ob7f6xyyg8qp3n7wdrgd", "source": "<EMAIL>", "tags": ["Mailing List", "Vendor Advisory"]}]}