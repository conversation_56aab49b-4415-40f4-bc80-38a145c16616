{"cve_id": "CVE-2023-38024", "published_date": "2023-08-28T04:15:14.783", "last_modified_date": "2024-11-21T08:12:41.740", "descriptions": [{"lang": "en", "value": "\nSpotCam Co., Ltd. SpotCam FHD 2’s hidden Telnet function has a vulnerability of using hard-coded Telnet credentials. An remote unauthenticated attacker can exploit this vulnerability to access the system to perform arbitrary system operations or disrupt service.\n\n"}], "references": [{"url": "https://www.twcert.org.tw/tw/cp-132-7331-9099e-1.html", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}