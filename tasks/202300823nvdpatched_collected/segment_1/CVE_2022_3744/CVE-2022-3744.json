{"cve_id": "CVE-2022-3744", "published_date": "2023-08-23T20:15:08.577", "last_modified_date": "2024-11-21T07:20:09.543", "descriptions": [{"lang": "en", "value": "A potential vulnerability was discovered in LCFC BIOS for some Lenovo consumer notebook models that could allow a local attacker with elevated privileges to unlock UEFI variables due to a hard-coded SMI handler credential."}], "references": [{"url": "https://support.lenovo.com/us/en/product_security/LEN-103710", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}