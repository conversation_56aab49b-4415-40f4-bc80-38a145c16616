{"cve_id": "CVE-2023-37379", "published_date": "2023-08-23T16:15:09.330", "last_modified_date": "2025-02-13T17:16:44.640", "descriptions": [{"lang": "en", "value": "Apache Airflow, in versions prior to 2.7.0, contains a security vulnerability that can be exploited by an authenticated user possessing Connection edit privileges. This vulnerability allows the user to access connection information and exploit the test connection feature by sending many requests, leading to a denial of service (DoS) condition on the server. Furthermore, malicious actors can leverage this vulnerability to establish harmful connections with the server.\n\nUsers of Apache Airflow are strongly advised to upgrade to version 2.7.0 or newer to mitigate the risk associated with this vulnerability. Additionally, administrators are encouraged to review and adjust user permissions to restrict access to sensitive functionalities, reducing the attack surface."}, {"lang": "es", "value": "Apache Airflow, en versiones anteriores a la 2.7.0, contiene una vulnerabilidad de seguridad que puede ser explotada por un usuario autenticado que posea privilegios de edición de conexión. Esta vulnerabilidad permite al usuario acceder a la información de conexión y explotar la función de conexión de prueba enviando muchas peticiones, lo que provoca una denegación de servicio (DoS) en el servidor. <PERSON><PERSON><PERSON>, actores maliciosos pueden aprovechar esta vulnerabilidad para establecer conexiones dañinas con el servidor. Se recomienda encarecidamente a los usuarios de Apache Airflow que actualicen a la versión 2.7.0 o posterior para mitigar el riesgo asociado a esta vulnerabilidad. Además, se recomienda a los administradores que revisen y ajusten los permisos de usuario para restringir el acceso a funcionalidades sensibles, reduciendo la superficie de ataque."}], "references": [{"url": "http://www.openwall.com/lists/oss-security/2023/08/23/4", "source": "<EMAIL>", "tags": ["Mailing List", "Third Party Advisory"]}, {"url": "https://github.com/apache/airflow/pull/32052", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://lists.apache.org/thread/g5c9vcn27lr14go48thrjpo6f4vw571r", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}