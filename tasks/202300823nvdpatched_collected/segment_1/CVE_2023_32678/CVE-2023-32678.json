{"cve_id": "CVE-2023-32678", "published_date": "2023-08-25T21:15:08.060", "last_modified_date": "2024-11-21T08:03:50.010", "descriptions": [{"lang": "en", "value": "Zulip is an open-source team collaboration tool with topic-based threading that combines email and chat. Users who used to be subscribed to a private stream and have been removed from it since retain the ability to edit messages/topics, move messages to other streams, and delete messages that they used to have access to, if other relevant organization permissions allow these actions. For example, a user may be able to edit or delete their old messages they posted in such a private stream. An administrator will be able to delete old messages (that they had access to) from the private stream. This issue was fixed in Zulip Server version 7.3."}, {"lang": "es", "value": "Zulip es una herramienta de colaboración en equipo de código abierto que combina correo electrónico y chat. Los usuarios que solían estar suscritos a un flujo privado y han sido eliminados de él desde entonces conservan la capacidad de editar mensajes/temas, mover mensajes a otros flujos y eliminar mensajes a los que solían tener acceso, si otros permisos relevantes de la organización permiten estas acciones. Por ejemplo, un usuario podrá editar o borrar sus mensajes antiguos que publicó en dicho stream privado. Un administrador podrá borrar mensajes antiguos (a los que tenía acceso) del flujo privado. Este problema ha sido solucionado en la versión 7.3 de Zulip Server.\n"}], "references": [{"url": "https://github.com/zulip/zulip/security/advisories/GHSA-q3wg-jm9p-35fj", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://zulip.readthedocs.io/en/latest/overview/changelog.html#zulip-server-7-3", "source": "<EMAIL>", "tags": ["Release Notes"]}]}