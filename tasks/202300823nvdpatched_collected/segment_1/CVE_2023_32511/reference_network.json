{"cve_id": "CVE-2023-32511", "cve_published_date": "2023-08-24T12:15:07.903", "cve_affected_cpes": [], "nodes": [{"node_id": "8cd1c834-b8cc-42e2-8e9c-8f79a8c8f03a", "url": "https://nvd.nist.gov/vuln/detail/CVE-2023-32511", "title": "CVE-2023-32511", "node_type": "ROOT", "source": "ROOT", "depth": 0, "category_in_type": "", "importance_score": 1.0}, {"node_id": "a08a1e81-f1b5-4da4-9ec8-72b0e9f7ac1e", "url": "https://nvd.nist.gov", "title": "NVD", "node_type": "SOURCE", "source": "NVD", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "31b2c949-d90b-4f63-91e1-fe266c687f27", "url": "https://github", "title": "GITHUB", "node_type": "SOURCE", "source": "GITHUB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "62f8d1ee-8837-4525-81c1-1b5491cae956", "url": "https://gitlab", "title": "GITLAB", "node_type": "SOURCE", "source": "GITLAB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "ada98e31-3bcb-46f2-bf91-951772ac076e", "url": "https://debian", "title": "DEBIAN", "node_type": "SOURCE", "source": "DEBIAN", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "977ce04f-9a0d-45d7-b3b7-52802e6822e7", "url": "https://redhat", "title": "REDHAT", "node_type": "SOURCE", "source": "REDHAT", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "c6dbab88-982a-4d84-924b-38bbc2504bf9", "url": "https://apache", "title": "APACHE", "node_type": "SOURCE", "source": "APACHE", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "61fc3ae3-7dfa-4f57-af0a-b9ca211782d8", "url": "https://other.source", "title": "OTHER", "node_type": "SOURCE", "source": "OTHER", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "0ea3c731-c0b7-4f8c-8e35-6813e1215bbe", "url": "https://patchstack.com/database/vulnerability/booking-ultra-pro/wordpress-booking-ultra-pro-appointments-booking-calendar-plugin-plugin-1-1-4-cross-site-scripting-xss-vulnerability", "title": "Wordpress booking ultra pro appointments booking calendar plugin plugin 1 1 4 cross site scripting xss vulnerability", "node_type": "COMMON", "source": "OTHER", "depth": 2, "category_in_type": "common_url", "importance_score": 1.0}, {"node_id": "82c15303-d49f-48c2-ace2-329b46cc99e7", "url": "https://patchstack.com/wordpress-security", "title": "", "node_type": "COMMON", "source": "OTHER", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}, {"node_id": "951f9f6b-eda1-444a-bc71-ea32ed79a25b", "url": "https://patchstack.com/for-hosts", "title": "", "node_type": "COMMON", "source": "OTHER", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}], "edges": [{"edge_id": "e6ede828-043d-441a-9e88-33bf0e7b8be8", "source_id": "8cd1c834-b8cc-42e2-8e9c-8f79a8c8f03a", "target_id": "a08a1e81-f1b5-4da4-9ec8-72b0e9f7ac1e", "weight": 0.8, "description": "来源"}, {"edge_id": "ea87c0d9-4d07-40cb-883c-e439d837d984", "source_id": "8cd1c834-b8cc-42e2-8e9c-8f79a8c8f03a", "target_id": "31b2c949-d90b-4f63-91e1-fe266c687f27", "weight": 0.8, "description": "来源"}, {"edge_id": "fde29134-2504-4ba3-8a84-5c1632ac9679", "source_id": "8cd1c834-b8cc-42e2-8e9c-8f79a8c8f03a", "target_id": "62f8d1ee-8837-4525-81c1-1b5491cae956", "weight": 0.8, "description": "来源"}, {"edge_id": "8ba1644d-e5ff-4260-b302-38f038d83299", "source_id": "8cd1c834-b8cc-42e2-8e9c-8f79a8c8f03a", "target_id": "ada98e31-3bcb-46f2-bf91-951772ac076e", "weight": 0.8, "description": "来源"}, {"edge_id": "3e2a87fb-5803-4034-822f-ece2a209e397", "source_id": "8cd1c834-b8cc-42e2-8e9c-8f79a8c8f03a", "target_id": "977ce04f-9a0d-45d7-b3b7-52802e6822e7", "weight": 0.8, "description": "来源"}, {"edge_id": "aaeb436a-130a-474b-8089-bfe969a8fc20", "source_id": "8cd1c834-b8cc-42e2-8e9c-8f79a8c8f03a", "target_id": "c6dbab88-982a-4d84-924b-38bbc2504bf9", "weight": 0.8, "description": "来源"}, {"edge_id": "9eb93864-dbf3-411a-8f59-8f7bec8f6410", "source_id": "8cd1c834-b8cc-42e2-8e9c-8f79a8c8f03a", "target_id": "61fc3ae3-7dfa-4f57-af0a-b9ca211782d8", "weight": 0.5, "description": "其他来源"}, {"edge_id": "cbcdbb5a-4549-4dfe-8f9b-e15a3c769573", "source_id": "a08a1e81-f1b5-4da4-9ec8-72b0e9f7ac1e", "target_id": "8cd1c834-b8cc-42e2-8e9c-8f79a8c8f03a", "weight": 1.0, "description": "引用"}, {"edge_id": "4a9a024b-99cd-4632-9b23-900468990056", "source_id": "61fc3ae3-7dfa-4f57-af0a-b9ca211782d8", "target_id": "0ea3c731-c0b7-4f8c-8e35-6813e1215bbe", "weight": 1.0, "description": "引用"}, {"edge_id": "d48e913f-ee4d-43c2-b532-72d3f8985d0b", "source_id": "a08a1e81-f1b5-4da4-9ec8-72b0e9f7ac1e", "target_id": "0ea3c731-c0b7-4f8c-8e35-6813e1215bbe", "weight": 0.7, "description": "NVD引用"}, {"edge_id": "dc61333f-7913-412c-ba7a-e2d84a2a62cd", "source_id": "0ea3c731-c0b7-4f8c-8e35-6813e1215bbe", "target_id": "82c15303-d49f-48c2-ace2-329b46cc99e7", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "aae8503e-1aaf-46b4-97b2-545a7470f56a", "source_id": "0ea3c731-c0b7-4f8c-8e35-6813e1215bbe", "target_id": "951f9f6b-eda1-444a-bc71-ea32ed79a25b", "weight": 0.3333333333333333, "description": "引用"}]}