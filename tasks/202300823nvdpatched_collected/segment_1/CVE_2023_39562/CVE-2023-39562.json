{"cve_id": "CVE-2023-39562", "published_date": "2023-08-28T19:15:07.747", "last_modified_date": "2024-11-21T08:15:40.650", "descriptions": [{"lang": "en", "value": "GPAC v2.3-DEV-rev449-g5948e4f70-master was discovered to contain a heap-use-after-free via the gf_bs_align function at bitstream.c. This vulnerability allows attackers to cause a Denial of Service (DoS) via supplying a crafted file."}], "references": [{"url": "https://github.com/ChanStormstout/Pocs/blob/master/gpac_POC/id%3A000000%2Csig%3A06%2Csrc%3A003771%2Ctime%3A328254%2Cexecs%3A120473%2Cop%3Ahavoc%2Crep%3A8", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://github.com/gpac/gpac/issues/2537", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}]}