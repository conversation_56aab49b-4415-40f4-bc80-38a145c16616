{"cve_id": "CVE-2023-20168", "published_date": "2023-08-23T19:15:07.777", "last_modified_date": "2024-11-21T07:40:43.873", "descriptions": [{"lang": "en", "value": "A vulnerability in TACACS+ and RADIUS remote authentication for Cisco NX-OS Software could allow an unauthenticated, local attacker to cause an affected device to unexpectedly reload. This vulnerability is due to incorrect input validation when processing an authentication attempt if the directed request option is enabled for TACACS+ or RADIUS. An attacker could exploit this vulnerability by entering a crafted string at the login prompt of an affected device. A successful exploit could allow the attacker to cause the affected device to unexpectedly reload, resulting in a denial of service (DoS) condition. "}], "references": [{"url": "https://sec.cloudapps.cisco.com/security/center/content/CiscoSecurityAdvisory/cisco-sa-nxos-remoteauth-dos-XB6pv74m", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}