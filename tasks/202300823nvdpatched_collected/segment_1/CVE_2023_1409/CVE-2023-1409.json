{"cve_id": "CVE-2023-1409", "published_date": "2023-08-23T16:15:08.167", "last_modified_date": "2025-02-13T17:15:58.493", "descriptions": [{"lang": "en", "value": "If the MongoDB Server running on Windows or macOS is configured to use TLS with a specific set of configuration options that are already known to work securely in other platforms (e.g. Linux), it is possible that client certificate validation may not be in effect, potentially allowing client to establish a TLS connection with the server that supplies any certificate.\n\nThis issue affect all MongoDB Server v6.3 versions, MongoDB Server v5.0 versions v5.0.0 to v5.0.14 and all MongoDB Server v4.4 versions."}], "references": [{"url": "https://jira.mongodb.org/browse/SERVER-73662", "source": "<EMAIL>", "tags": ["Issue Tracking", "Patch", "Vendor Advisory"]}, {"url": "https://jira.mongodb.org/browse/SERVER-77028", "source": "<EMAIL>", "tags": ["Issue Tracking", "Patch", "Vendor Advisory"]}, {"url": "https://security.netapp.com/advisory/ntap-20230921-0007/", "source": "<EMAIL>", "tags": []}]}