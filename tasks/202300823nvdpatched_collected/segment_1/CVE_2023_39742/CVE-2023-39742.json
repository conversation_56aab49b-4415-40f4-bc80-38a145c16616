{"cve_id": "CVE-2023-39742", "published_date": "2023-08-25T14:15:09.763", "last_modified_date": "2024-11-21T08:15:54.413", "descriptions": [{"lang": "en", "value": "giflib v5.2.1 was discovered to contain a segmentation fault via the component getarg.c."}, {"lang": "es", "value": "Se ha descubierto que giflib v5.2.1 contiene un fallo de segmentación a través del componente getarg.c."}], "references": [{"url": "https://gist.github.com/huanglei3/ec9090096aa92445cf0a8baa8e929084", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/O4RLSFGPBPR3FMIUJCWPGVIYIU35YGQX/", "source": "<EMAIL>", "tags": []}, {"url": "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/OPNBOB65TEA4ZEPLVENI26BY4LEX7TEF/", "source": "<EMAIL>", "tags": []}, {"url": "https://lists.fedoraproject.org/archives/list/package-announce%40lists.fedoraproject.org/message/T5WO6WL2TCGO6T4VKGACDIVSZI74WJAU/", "source": "<EMAIL>", "tags": []}, {"url": "https://sourceforge.net/p/giflib/bugs/166/", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking"]}]}