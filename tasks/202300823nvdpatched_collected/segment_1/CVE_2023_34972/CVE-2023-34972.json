{"cve_id": "CVE-2023-34972", "published_date": "2023-08-24T17:15:08.693", "last_modified_date": "2024-11-21T08:07:45.047", "descriptions": [{"lang": "en", "value": "A cleartext transmission of sensitive information vulnerability has been reported to affect QNAP operating systems. If exploited, the vulnerability possibly allows local network clients to read the contents of unexpected sensitive data via unspecified vectors.\n\nWe have already fixed the vulnerability in the following versions:\nQTS 5.0.1.2425 build 20230609 and later\nQTS 5.1.0.2444 build 20230629 and later\nQuTS hero h5.1.0.2424 build 20230609 and later\n"}], "references": [{"url": "https://www.qnap.com/en/security-advisory/qsa-23-58", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}