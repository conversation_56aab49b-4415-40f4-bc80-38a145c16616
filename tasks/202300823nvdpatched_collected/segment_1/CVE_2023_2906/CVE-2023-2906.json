{"cve_id": "CVE-2023-2906", "published_date": "2023-08-25T21:15:07.963", "last_modified_date": "2025-02-13T17:16:23.153", "descriptions": [{"lang": "en", "value": "Due to a failure in validating the length provided by an attacker-crafted CP2179 packet, Wireshark versions 2.0.0 through 4.0.7 is susceptible to a divide by zero allowing for a denial of service attack."}, {"lang": "es", "value": "Debido a un error al validar la longitud proporcionada por un paquete CP2179 creado por un atacante, las versiones de Wireshark 2.0.0 a 4.0.7 son susceptibles a una división por cero, lo que permite un ataque de denegación de servicio."}], "references": [{"url": "https://gitlab.com/wireshark/wireshark/-/issues/19229", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Patch", "Third Party Advisory"]}, {"url": "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/6HCUPLDY7HLPO46PHMGIJSUBJFTT237C/", "source": "<EMAIL>", "tags": []}, {"url": "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/L4AVRUYSHDNEAJILVSGY5W6MPOMG2YRF/", "source": "<EMAIL>", "tags": []}, {"url": "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/TRKHFQPWFU7F3OXTL6IEIQSJG6FVXZTZ/", "source": "<EMAIL>", "tags": []}, {"url": "https://takeonme.org/cves/CVE-2023-2906.html", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}