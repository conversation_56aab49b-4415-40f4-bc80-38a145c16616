{"cve_id": "CVE-2023-34039", "published_date": "2023-08-29T18:15:08.680", "last_modified_date": "2024-11-21T08:06:27.193", "descriptions": [{"lang": "en", "value": "Aria Operations for Networks contains an Authentication Bypass vulnerability due to a lack of unique cryptographic key generation. A malicious actor with network access to Aria Operations for Networks could bypass SSH authentication to gain access to the Aria Operations for Networks CLI."}], "references": [{"url": "http://packetstormsecurity.com/files/174452/VMWare-Aria-Operations-For-Networks-Remote-Code-Execution.html", "source": "<EMAIL>", "tags": ["Third Party Advisory", "VDB Entry"]}, {"url": "http://packetstormsecurity.com/files/175320/VMWare-Aria-Operations-For-Networks-SSH-Private-Key-Exposure.html", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory", "VDB Entry"]}, {"url": "https://www.vmware.com/security/advisories/VMSA-2023-0018.html", "source": "<EMAIL>", "tags": ["Patch", "Vendor Advisory"]}]}