{"cve_id": "CVE-2023-32499", "cve_published_date": "2023-08-23T14:15:09.047", "cve_affected_cpes": [], "nodes": [{"node_id": "e7753441-b4a6-4821-bc38-ee98632c3563", "url": "https://nvd.nist.gov/vuln/detail/CVE-2023-32499", "title": "CVE-2023-32499", "node_type": "ROOT", "source": "ROOT", "depth": 0, "category_in_type": "", "importance_score": 1.0}, {"node_id": "7f859a90-f487-4b35-a82f-dc098b3ff018", "url": "https://nvd.nist.gov", "title": "NVD", "node_type": "SOURCE", "source": "NVD", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "8c962101-6e37-4ff5-a08a-ba00bcbaa7bf", "url": "https://github", "title": "GITHUB", "node_type": "SOURCE", "source": "GITHUB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "db42655b-9ef1-48ed-bfdf-6dfbcca58e74", "url": "https://gitlab", "title": "GITLAB", "node_type": "SOURCE", "source": "GITLAB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "b690bf3d-c169-4689-8d53-78f0b6459c81", "url": "https://debian", "title": "DEBIAN", "node_type": "SOURCE", "source": "DEBIAN", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "7852a348-33d1-405d-8f48-093fdf206d2c", "url": "https://redhat", "title": "REDHAT", "node_type": "SOURCE", "source": "REDHAT", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "f705659b-35a0-4a53-9bac-bd398c20f679", "url": "https://apache", "title": "APACHE", "node_type": "SOURCE", "source": "APACHE", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "edd85ac9-07ab-4060-8cc0-4663089173e7", "url": "https://other.source", "title": "OTHER", "node_type": "SOURCE", "source": "OTHER", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "0e1fcac9-2542-44b2-910e-c2b2cae11bf1", "url": "https://patchstack.com/database/vulnerability/radio-station/wordpress-radio-station-plugin-2-4-0-9-reflected-cross-site-scripting-xss-vulnerability", "title": "Wordpress radio station plugin 2 4 0 9 reflected cross site scripting xss vulnerability", "node_type": "COMMON", "source": "OTHER", "depth": 2, "category_in_type": "common_url", "importance_score": 1.0}, {"node_id": "2414a434-2d47-4304-807f-e4365111d33e", "url": "https://patchstack.com/wordpress-security", "title": "", "node_type": "COMMON", "source": "OTHER", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}, {"node_id": "ddf61b6b-9169-46f6-bcd7-b4f2a1869257", "url": "https://patchstack.com/for-hosts", "title": "", "node_type": "COMMON", "source": "OTHER", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}], "edges": [{"edge_id": "ad5d4924-0cdc-4672-8fbf-4452637508a6", "source_id": "e7753441-b4a6-4821-bc38-ee98632c3563", "target_id": "7f859a90-f487-4b35-a82f-dc098b3ff018", "weight": 0.8, "description": "来源"}, {"edge_id": "f7d54618-1660-46d3-a616-85eca405bb78", "source_id": "e7753441-b4a6-4821-bc38-ee98632c3563", "target_id": "8c962101-6e37-4ff5-a08a-ba00bcbaa7bf", "weight": 0.8, "description": "来源"}, {"edge_id": "e2a22d5c-aded-4b19-8b55-7db1b67c82f6", "source_id": "e7753441-b4a6-4821-bc38-ee98632c3563", "target_id": "db42655b-9ef1-48ed-bfdf-6dfbcca58e74", "weight": 0.8, "description": "来源"}, {"edge_id": "a01c061a-0810-4122-bcc1-4fc258851474", "source_id": "e7753441-b4a6-4821-bc38-ee98632c3563", "target_id": "b690bf3d-c169-4689-8d53-78f0b6459c81", "weight": 0.8, "description": "来源"}, {"edge_id": "36145b52-3155-492c-ad4c-9640d9d492e9", "source_id": "e7753441-b4a6-4821-bc38-ee98632c3563", "target_id": "7852a348-33d1-405d-8f48-093fdf206d2c", "weight": 0.8, "description": "来源"}, {"edge_id": "9c43a74e-20ea-4bdc-9dd1-96e609abe32f", "source_id": "e7753441-b4a6-4821-bc38-ee98632c3563", "target_id": "f705659b-35a0-4a53-9bac-bd398c20f679", "weight": 0.8, "description": "来源"}, {"edge_id": "8275ce68-7f27-4548-b6e4-8be4fff33897", "source_id": "e7753441-b4a6-4821-bc38-ee98632c3563", "target_id": "edd85ac9-07ab-4060-8cc0-4663089173e7", "weight": 0.5, "description": "其他来源"}, {"edge_id": "0c52b568-f3b8-40ad-9e13-125e05041581", "source_id": "7f859a90-f487-4b35-a82f-dc098b3ff018", "target_id": "e7753441-b4a6-4821-bc38-ee98632c3563", "weight": 1.0, "description": "引用"}, {"edge_id": "76454452-60d2-44c4-a38c-ae07c12edc68", "source_id": "edd85ac9-07ab-4060-8cc0-4663089173e7", "target_id": "0e1fcac9-2542-44b2-910e-c2b2cae11bf1", "weight": 1.0, "description": "引用"}, {"edge_id": "d587518c-4ac1-4880-bbb5-ee853f528959", "source_id": "7f859a90-f487-4b35-a82f-dc098b3ff018", "target_id": "0e1fcac9-2542-44b2-910e-c2b2cae11bf1", "weight": 0.7, "description": "NVD引用"}, {"edge_id": "663a6c2b-8a58-4311-b8e2-e7893649b215", "source_id": "0e1fcac9-2542-44b2-910e-c2b2cae11bf1", "target_id": "2414a434-2d47-4304-807f-e4365111d33e", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "1c9e4632-e202-44fe-8540-460c8bd1fced", "source_id": "0e1fcac9-2542-44b2-910e-c2b2cae11bf1", "target_id": "ddf61b6b-9169-46f6-bcd7-b4f2a1869257", "weight": 0.3333333333333333, "description": "引用"}]}