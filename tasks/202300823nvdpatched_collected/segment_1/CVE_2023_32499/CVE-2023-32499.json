{"cve_id": "CVE-2023-32499", "published_date": "2023-08-23T14:15:09.047", "last_modified_date": "2024-11-21T08:03:29.050", "descriptions": [{"lang": "en", "value": "Unauth. Reflected Cross-Site Scripting (XSS) vulnerability in <PERSON>, Tony <PERSON> Station by netmix® – Manage and play your Show Schedule in WordPress! plugin <= ******* versions."}], "references": [{"url": "https://patchstack.com/database/vulnerability/radio-station/wordpress-radio-station-plugin-2-4-0-9-reflected-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}