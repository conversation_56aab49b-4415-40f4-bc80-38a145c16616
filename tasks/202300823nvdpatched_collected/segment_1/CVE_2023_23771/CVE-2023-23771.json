{"cve_id": "CVE-2023-23771", "published_date": "2023-08-29T09:15:08.910", "last_modified_date": "2024-11-21T07:46:47.850", "descriptions": [{"lang": "en", "value": "Motorola MBTS Base Radio accepts hard-coded backdoor password. The Motorola MBTS Base Radio Man Machine Interface (MMI), allowing for service technicians to diagnose and configure the device, accepts a hard-coded backdoor password that cannot be changed or disabled."}], "references": [{"url": "https://tetraburst.com/", "source": "<EMAIL>", "tags": ["Not Applicable"]}]}