{"cve_id": "CVE-2020-18912", "published_date": "2023-08-29T23:15:07.980", "last_modified_date": "2024-11-21T05:08:52.053", "descriptions": [{"lang": "en", "value": "An issue found in Earcms Ear App v.20181124 allows a remote attacker to execute arbitrary code via the uload/index-uplog.php."}, {"lang": "es", "value": "Un problema encontrado en Earcms Ear App v20181124 permite a un atacante remoto ejecutar código arbitrario a través de \"uload/index-uplog.php\"."}], "references": [{"url": "https://www.cnblogs.com/hantom/p/10621198.html", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://www.cnblogs.com/yiwd/archive/2013/03/03/2941269.html", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}