{"cve_id": "CVE-2023-37469", "published_date": "2023-08-24T23:15:08.700", "last_modified_date": "2024-11-21T08:11:46.457", "descriptions": [{"lang": "en", "value": "CasaOS is an open-source personal cloud system. Prior to version 0.4.4, if an authenticated user using CasaOS is able to successfully connect to a controlled SMB server, they are able to execute arbitrary commands. Version 0.4.4 contains a patch for the issue."}], "references": [{"url": "https://github.com/IceWhaleTech/CasaOS/blob/96e92842357230098c771bc41fd3baf46189b859/route/v1/samba.go#L121", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/IceWhaleTech/CasaOS/blob/96e92842357230098c771bc41fd3baf46189b859/service/connections.go#L58", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/IceWhaleTech/CasaOS/commit/af440eac5563644854ff33f72041e52d3fd1f47c", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/IceWhaleTech/CasaOS/releases/tag/v0.4.4", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://securitylab.github.com/advisories/GHSL-2022-119_CasaOS/", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}