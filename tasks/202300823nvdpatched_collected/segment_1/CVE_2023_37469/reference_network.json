{"cve_id": "CVE-2023-37469", "cve_published_date": "2023-08-24T23:15:08.700", "cve_affected_cpes": [], "nodes": [{"node_id": "553277db-91cb-4ae9-a7be-d9228eda2d2c", "url": "https://nvd.nist.gov/vuln/detail/CVE-2023-37469", "title": "CVE-2023-37469", "node_type": "ROOT", "source": "ROOT", "depth": 0, "category_in_type": "", "importance_score": 1.0}, {"node_id": "ca64c6da-a8ee-4045-85b6-df123de620ba", "url": "https://nvd.nist.gov", "title": "NVD", "node_type": "SOURCE", "source": "NVD", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "66e3a85c-1c13-4ff4-8911-a5c269d5138c", "url": "https://github", "title": "GITHUB", "node_type": "SOURCE", "source": "GITHUB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "c6cf2920-fa13-471f-8841-64f131052f88", "url": "https://gitlab", "title": "GITLAB", "node_type": "SOURCE", "source": "GITLAB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "16ea736c-c5ea-40ab-bc9a-1f7164587ae3", "url": "https://debian", "title": "DEBIAN", "node_type": "SOURCE", "source": "DEBIAN", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "1dc831cf-d467-45e6-a9c0-c90ab44461d8", "url": "https://redhat", "title": "REDHAT", "node_type": "SOURCE", "source": "REDHAT", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "f7236963-2566-4a29-8d26-e2b9702ec1c3", "url": "https://apache", "title": "APACHE", "node_type": "SOURCE", "source": "APACHE", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "ab80ba98-db88-451d-957f-703e0b4600ae", "url": "https://other.source", "title": "OTHER", "node_type": "SOURCE", "source": "OTHER", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "e6e7041b-41dc-4ca4-a5a7-af38ab4bf236", "url": "https://securitylab.github.com/advisories/GHSL-2022-119_CasaOS", "title": "Ghsl 2022 119 casaos", "node_type": "REPO", "source": "GITHUB", "depth": 2, "category_in_type": "github_repo", "importance_score": 1.0}, {"node_id": "68a7452c-aca6-4bef-8a2e-96ffb074c091", "url": "https://github.com/IceWhaleTech/CasaOS/releases/tag/v0.4.4", "title": "V0", "node_type": "COMMON", "source": "GITHUB", "depth": 2, "category_in_type": "common_github_url", "importance_score": 1.0}, {"node_id": "59da0f3d-f900-4020-8670-e436189f609b", "url": "https://github.com/IceWhaleTech/CasaOS/blob/96e92842357230098c771bc41fd3baf46189b859/service/connections.go", "title": "Connections", "node_type": "COMMON", "source": "GITHUB", "depth": 2, "category_in_type": "", "importance_score": 1.0}, {"node_id": "78087598-2ef4-40c7-b95d-b5014b23904d", "url": "https://github.com/IceWhaleTech/CasaOS/blob/96e92842357230098c771bc41fd3baf46189b859/route/v1/samba.go", "title": "Samba", "node_type": "COMMON", "source": "GITHUB", "depth": 2, "category_in_type": "", "importance_score": 1.0}, {"node_id": "57f05c08-c86a-429a-b7f5-b305bc217bf3", "url": "https://github.com/IceWhaleTech/CasaOS/commit/af440eac5563644854ff33f72041e52d3fd1f47c", "title": "Af440eac5563644854ff33f72041e52d3fd1f47c", "node_type": "PATCH", "source": "GITHUB", "depth": 2, "category_in_type": "git_commit", "importance_score": 1.0}, {"node_id": "f6e56be1-c3db-4eba-a76e-ba4236cceb1d", "url": "https://github.com/IceWhaleTech/CasaOS/commit/9d6381d7ac91d51839814a9b350f8782c3042bc3", "title": "", "node_type": "PATCH", "source": "GITHUB", "depth": 3, "category_in_type": "git_commit", "importance_score": 0.0}, {"node_id": "6a4e172b-66bd-4e7f-acff-29bba024974f", "url": "https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax", "title": "", "node_type": "COMMON", "source": "GITHUB", "depth": 3, "category_in_type": "common_github_url", "importance_score": 0.0}, {"node_id": "af375ba6-dac4-4a2a-b59c-e856976db779", "url": "https://github.com/IceWhaleTech/CasaOS/tree/v0.4.4", "title": "", "node_type": "COMMON", "source": "GITHUB", "depth": 3, "category_in_type": "common_github_url", "importance_score": 0.0}, {"node_id": "a8f8ba8d-c055-4fb2-a78e-71521084a6d9", "url": "https://github.com/features/copilot", "title": "", "node_type": "REPO", "source": "GITHUB", "depth": 3, "category_in_type": "github_repo", "importance_score": 0.0}, {"node_id": "e5b91186-ebfb-4e04-ab61-f053eda7ea5b", "url": "https://github.com/features/spark", "title": "", "node_type": "REPO", "source": "GITHUB", "depth": 3, "category_in_type": "github_repo", "importance_score": 0.0}], "edges": [{"edge_id": "087d57ee-9d5c-4ea6-960b-50e07aa508a6", "source_id": "553277db-91cb-4ae9-a7be-d9228eda2d2c", "target_id": "ca64c6da-a8ee-4045-85b6-df123de620ba", "weight": 0.8, "description": "来源"}, {"edge_id": "edb0c949-71e6-4f4b-8086-3fcff359b82b", "source_id": "553277db-91cb-4ae9-a7be-d9228eda2d2c", "target_id": "66e3a85c-1c13-4ff4-8911-a5c269d5138c", "weight": 0.8, "description": "来源"}, {"edge_id": "638ef03f-b421-4c6a-a607-d25aa27503f3", "source_id": "553277db-91cb-4ae9-a7be-d9228eda2d2c", "target_id": "c6cf2920-fa13-471f-8841-64f131052f88", "weight": 0.8, "description": "来源"}, {"edge_id": "e7b10dc0-a9f5-4413-9afe-b6d5b953449d", "source_id": "553277db-91cb-4ae9-a7be-d9228eda2d2c", "target_id": "16ea736c-c5ea-40ab-bc9a-1f7164587ae3", "weight": 0.8, "description": "来源"}, {"edge_id": "ae1e8f07-6f19-4c3b-8078-390d99a92124", "source_id": "553277db-91cb-4ae9-a7be-d9228eda2d2c", "target_id": "1dc831cf-d467-45e6-a9c0-c90ab44461d8", "weight": 0.8, "description": "来源"}, {"edge_id": "a1f1d7ab-eeed-4874-86ae-5df0ea58a3bf", "source_id": "553277db-91cb-4ae9-a7be-d9228eda2d2c", "target_id": "f7236963-2566-4a29-8d26-e2b9702ec1c3", "weight": 0.8, "description": "来源"}, {"edge_id": "b5a9e408-8f29-4994-9df6-5c10ccf5e4ce", "source_id": "553277db-91cb-4ae9-a7be-d9228eda2d2c", "target_id": "ab80ba98-db88-451d-957f-703e0b4600ae", "weight": 0.5, "description": "其他来源"}, {"edge_id": "899b4576-f7b8-4979-93bf-204815c76896", "source_id": "ca64c6da-a8ee-4045-85b6-df123de620ba", "target_id": "553277db-91cb-4ae9-a7be-d9228eda2d2c", "weight": 1.0, "description": "引用"}, {"edge_id": "6f3c47cb-8baf-46d6-9de3-fbfbf49ee13b", "source_id": "66e3a85c-1c13-4ff4-8911-a5c269d5138c", "target_id": "e6e7041b-41dc-4ca4-a5a7-af38ab4bf236", "weight": 1.0, "description": "引用"}, {"edge_id": "0132c770-e1b2-4869-bf64-c361175fd712", "source_id": "ca64c6da-a8ee-4045-85b6-df123de620ba", "target_id": "e6e7041b-41dc-4ca4-a5a7-af38ab4bf236", "weight": 0.7, "description": "NVD引用"}, {"edge_id": "4e201590-1630-4491-b530-85c2d1f5e898", "source_id": "66e3a85c-1c13-4ff4-8911-a5c269d5138c", "target_id": "68a7452c-aca6-4bef-8a2e-96ffb074c091", "weight": 1.0, "description": "引用"}, {"edge_id": "de3b4d0a-4e71-4c80-917d-2c6a7e68c9ee", "source_id": "ca64c6da-a8ee-4045-85b6-df123de620ba", "target_id": "68a7452c-aca6-4bef-8a2e-96ffb074c091", "weight": 0.7, "description": "NVD引用"}, {"edge_id": "f33137b3-6ff0-4256-b100-cae5913b1451", "source_id": "66e3a85c-1c13-4ff4-8911-a5c269d5138c", "target_id": "59da0f3d-f900-4020-8670-e436189f609b", "weight": 1.0, "description": "引用"}, {"edge_id": "9645b5ed-446f-452d-b8ba-53bddfc4a32c", "source_id": "ca64c6da-a8ee-4045-85b6-df123de620ba", "target_id": "59da0f3d-f900-4020-8670-e436189f609b", "weight": 0.7, "description": "NVD引用"}, {"edge_id": "3eca5346-15b7-4797-b71e-a8ab520994dd", "source_id": "66e3a85c-1c13-4ff4-8911-a5c269d5138c", "target_id": "78087598-2ef4-40c7-b95d-b5014b23904d", "weight": 1.0, "description": "引用"}, {"edge_id": "8bb5a31a-a913-409a-a48f-bc414db3a09f", "source_id": "ca64c6da-a8ee-4045-85b6-df123de620ba", "target_id": "78087598-2ef4-40c7-b95d-b5014b23904d", "weight": 0.7, "description": "NVD引用"}, {"edge_id": "55cfa0ec-fc6b-4720-9503-bafa928a2ebf", "source_id": "66e3a85c-1c13-4ff4-8911-a5c269d5138c", "target_id": "57f05c08-c86a-429a-b7f5-b305bc217bf3", "weight": 1.0, "description": "引用"}, {"edge_id": "d5a16a9a-5345-42c9-8a59-c868b323bd84", "source_id": "ca64c6da-a8ee-4045-85b6-df123de620ba", "target_id": "57f05c08-c86a-429a-b7f5-b305bc217bf3", "weight": 0.7, "description": "NVD引用"}, {"edge_id": "d6dde6fc-15ee-4921-b128-cd6acca76a0a", "source_id": "553277db-91cb-4ae9-a7be-d9228eda2d2c", "target_id": "57f05c08-c86a-429a-b7f5-b305bc217bf3", "weight": 0.9, "description": "直接补丁"}, {"edge_id": "9210595e-fcd1-4851-8014-0f786d85d9bd", "source_id": "68a7452c-aca6-4bef-8a2e-96ffb074c091", "target_id": "f6e56be1-c3db-4eba-a76e-ba4236cceb1d", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "a7749223-f6e1-4f50-bd23-8c4ae69fc8de", "source_id": "66e3a85c-1c13-4ff4-8911-a5c269d5138c", "target_id": "f6e56be1-c3db-4eba-a76e-ba4236cceb1d", "weight": 0.9, "description": "来源引用"}, {"edge_id": "cbeb1ab0-4231-415d-be4d-82076b1c4e29", "source_id": "553277db-91cb-4ae9-a7be-d9228eda2d2c", "target_id": "f6e56be1-c3db-4eba-a76e-ba4236cceb1d", "weight": 0.8, "description": "关联补丁"}, {"edge_id": "1abc5369-e489-4d71-afb9-a189b60cba<PERSON>", "source_id": "68a7452c-aca6-4bef-8a2e-96ffb074c091", "target_id": "6a4e172b-66bd-4e7f-acff-29bba024974f", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "3f87d696-dd8c-43a0-a4d0-5d3b66695038", "source_id": "66e3a85c-1c13-4ff4-8911-a5c269d5138c", "target_id": "6a4e172b-66bd-4e7f-acff-29bba024974f", "weight": 0.9, "description": "来源引用"}, {"edge_id": "1a3ceaa4-d6d4-4c70-b61b-6e6600078017", "source_id": "68a7452c-aca6-4bef-8a2e-96ffb074c091", "target_id": "af375ba6-dac4-4a2a-b59c-e856976db779", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "a80881ac-7555-4feb-81c6-c1d4f668ab17", "source_id": "66e3a85c-1c13-4ff4-8911-a5c269d5138c", "target_id": "af375ba6-dac4-4a2a-b59c-e856976db779", "weight": 0.9, "description": "来源引用"}, {"edge_id": "db678ee7-28d0-44b9-ac61-5a078b4a64b2", "source_id": "59da0f3d-f900-4020-8670-e436189f609b", "target_id": "a8f8ba8d-c055-4fb2-a78e-71521084a6d9", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "e608e361-7a84-4771-83d9-23800ff54d8d", "source_id": "66e3a85c-1c13-4ff4-8911-a5c269d5138c", "target_id": "a8f8ba8d-c055-4fb2-a78e-71521084a6d9", "weight": 0.9, "description": "来源引用"}, {"edge_id": "83178757-2858-44a0-81c3-2f852509869d", "source_id": "59da0f3d-f900-4020-8670-e436189f609b", "target_id": "e5b91186-ebfb-4e04-ab61-f053eda7ea5b", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "3a2c0d95-0593-4167-acd8-1d6b5d766d69", "source_id": "66e3a85c-1c13-4ff4-8911-a5c269d5138c", "target_id": "e5b91186-ebfb-4e04-ab61-f053eda7ea5b", "weight": 0.9, "description": "来源引用"}]}