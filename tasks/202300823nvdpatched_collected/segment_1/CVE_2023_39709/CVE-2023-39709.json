{"cve_id": "CVE-2023-39709", "published_date": "2023-08-28T19:15:07.830", "last_modified_date": "2024-11-21T08:15:51.440", "descriptions": [{"lang": "en", "value": "Multiple cross-site scripting (XSS) vulnerabilities in Free and Open Source Inventory Management System v1.0 allows attackers to execute arbitrary web scripts or HTML via injecting a crafted payload into the Name, Address, and Company parameters under the Add Member section."}], "references": [{"url": "https://gist.github.com/Arajawat007/4cb86f9239c73ccfeaf466352513b188#file-cve-2023-39709", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://www.sourcecodester.com/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://www.sourcecodester.com/php/16741/free-and-open-source-inventory-management-system-php-source-code.html", "source": "<EMAIL>", "tags": ["Product"]}]}