{"cve_id": "CVE-2023-38201", "published_date": "2023-08-25T17:15:08.530", "last_modified_date": "2024-11-21T08:13:04.463", "descriptions": [{"lang": "en", "value": "A flaw was found in the Keylime registrar that could allow a bypass of the challenge-response protocol during agent registration. This issue may allow an attacker to impersonate an agent and hide the true status of a monitored machine if the fake agent is added to the verifier list by a legitimate user, resulting in a breach of the integrity of the registrar database."}, {"lang": "es", "value": "Se encontró una falla en el registrador de Keylime que podría permitir una omisión del protocolo de desafío-respuesta durante el registro del agente. Este problema puede permitir a un atacante suplantar a un agente y ocultar el verdadero estado de un equipo supervisado si un usuario legítimo agrega el agente falso a la lista de verificadores, lo que provoca una violación de la integridad de la base de datos del registrador."}], "references": [{"url": "https://access.redhat.com/errata/RHSA-2023:5080", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://access.redhat.com/security/cve/CVE-2023-38201", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://bugzilla.redhat.com/show_bug.cgi?id=2222693", "source": "<EMAIL>", "tags": ["Issue Tracking", "Patch", "Third Party Advisory"]}, {"url": "https://github.com/keylime/keylime/commit/9e5ac9f25cd400b16d5969f531cee28290543f2a", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/keylime/keylime/security/advisories/GHSA-f4r5-q63f-gcww", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://lists.fedoraproject.org/archives/list/<EMAIL>/message/ZIZZB5NHNCS5D2AEH3ZAO6OQC72IK7WS/", "source": "af854a3a-2127-422b-91ae-364da2661108", "tags": []}]}