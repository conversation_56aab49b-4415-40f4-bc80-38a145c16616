{"cve_id": "CVE-2023-20234", "published_date": "2023-08-23T19:15:08.277", "last_modified_date": "2024-11-21T07:40:57.383", "descriptions": [{"lang": "en", "value": "A vulnerability in the CLI of Cisco FXOS Software could allow an authenticated, local attacker to create a file or overwrite any file on the filesystem of an affected device, including system files.\r\n\r The vulnerability occurs because there is no validation of parameters when a specific CLI command is used. An attacker could exploit this vulnerability by authenticating to an affected device and using the command at the CLI. A successful exploit could allow the attacker to overwrite any file on the disk of the affected device, including system files. The attacker must have valid administrative credentials on the affected device to exploit this vulnerability."}], "references": [{"url": "https://sec.cloudapps.cisco.com/security/center/content/CiscoSecurityAdvisory/cisco-sa-fxos-arbitrary-file-BLk6YupL", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}