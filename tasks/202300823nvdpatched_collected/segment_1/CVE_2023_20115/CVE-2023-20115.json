{"cve_id": "CVE-2023-20115", "published_date": "2023-08-23T19:15:07.587", "last_modified_date": "2024-11-21T07:40:35.413", "descriptions": [{"lang": "en", "value": "A vulnerability in the SFTP server implementation for Cisco Nexus 3000 Series Switches and 9000 Series Switches in standalone NX-OS mode could allow an authenticated, remote attacker to download or overwrite files from the underlying operating system of an affected device. \r\n\r This vulnerability is due to a logic error when verifying the user role when an SFTP connection is opened to an affected device. An attacker could exploit this vulnerability by connecting and authenticating via SFTP as a valid, non-administrator user. A successful exploit could allow the attacker to read or overwrite files from the underlying operating system with the privileges of the authenticated user.\r\n\r   There are workarounds that address this vulnerability."}], "references": [{"url": "https://sec.cloudapps.cisco.com/security/center/content/CiscoSecurityAdvisory/cisco-sa-nxos-sftp-xVAp5Hfd", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}