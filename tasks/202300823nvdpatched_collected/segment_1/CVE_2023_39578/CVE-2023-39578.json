{"cve_id": "CVE-2023-39578", "published_date": "2023-08-28T20:15:08.207", "last_modified_date": "2024-11-21T08:15:40.950", "descriptions": [{"lang": "en", "value": "A stored cross-site scripting (XSS) vulnerability in the Create function of Zenario CMS v9.4 allows attackers to execute arbitrary web scripts or HTML via a crafted payload injected into the Menu navigation text field."}], "references": [{"url": "https://github.com/anh91/Zenario-xss/issues/1", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Vendor Advisory"]}, {"url": "https://panda002.hashnode.dev/a-stored-cross-site-scripting-xss-vulnerability-in-the-create-the-function-of-zenario-cms-v94", "source": "<EMAIL>", "tags": []}]}