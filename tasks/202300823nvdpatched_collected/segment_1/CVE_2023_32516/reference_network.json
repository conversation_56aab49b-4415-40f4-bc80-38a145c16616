{"cve_id": "CVE-2023-32516", "cve_published_date": "2023-08-24T12:15:07.990", "cve_affected_cpes": [], "nodes": [{"node_id": "83bbc7db-709a-48ed-a0d7-191d3bddcec6", "url": "https://nvd.nist.gov/vuln/detail/CVE-2023-32516", "title": "CVE-2023-32516", "node_type": "ROOT", "source": "ROOT", "depth": 0, "category_in_type": "", "importance_score": 1.0}, {"node_id": "ead0c57f-12ee-4640-a9fb-99ea3a8003d3", "url": "https://nvd.nist.gov", "title": "NVD", "node_type": "SOURCE", "source": "NVD", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "97914994-9cab-44fc-a1c5-05df6c2ec2a6", "url": "https://github", "title": "GITHUB", "node_type": "SOURCE", "source": "GITHUB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "ca2d7c9d-e316-4822-a28b-a14068075e7e", "url": "https://gitlab", "title": "GITLAB", "node_type": "SOURCE", "source": "GITLAB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "4a9537b4-af71-4d48-9eb2-5c138148c1a0", "url": "https://debian", "title": "DEBIAN", "node_type": "SOURCE", "source": "DEBIAN", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "2ccf1a6a-1a55-4379-b6ca-3f5a3365148d", "url": "https://redhat", "title": "REDHAT", "node_type": "SOURCE", "source": "REDHAT", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "fa30bc82-1ff7-405f-8b6a-d5a9fc6ebb26", "url": "https://apache", "title": "APACHE", "node_type": "SOURCE", "source": "APACHE", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "9148eaba-b30c-4857-8134-6ee724c2d6d7", "url": "https://other.source", "title": "OTHER", "node_type": "SOURCE", "source": "OTHER", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "b52ee393-9fd0-428c-9e19-7e95e909741e", "url": "https://patchstack.com/database/vulnerability/menu-ordering-reservations/wordpress-restaurant-menu-food-ordering-system-table-reservation-plugin-2-3-6-reflected-cross-site-scripting-xss-vulnerability", "title": "Wordpress restaurant menu food ordering system table reservation plugin 2 3 6 reflected cross site scripting xss vulnerability", "node_type": "COMMON", "source": "OTHER", "depth": 2, "category_in_type": "common_url", "importance_score": 1.0}, {"node_id": "3ce07780-6614-4232-bc5d-8dd309163f16", "url": "https://patchstack.com/wordpress-security", "title": "", "node_type": "COMMON", "source": "OTHER", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}, {"node_id": "372ad973-0d6a-4fa0-aa0f-f499c94187b7", "url": "https://patchstack.com/for-hosts", "title": "", "node_type": "COMMON", "source": "OTHER", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}], "edges": [{"edge_id": "0619a1d7-0a29-4975-ae5b-2ee4a33e5276", "source_id": "83bbc7db-709a-48ed-a0d7-191d3bddcec6", "target_id": "ead0c57f-12ee-4640-a9fb-99ea3a8003d3", "weight": 0.8, "description": "来源"}, {"edge_id": "ce28f540-2859-4fe0-a225-34bac8f27dde", "source_id": "83bbc7db-709a-48ed-a0d7-191d3bddcec6", "target_id": "97914994-9cab-44fc-a1c5-05df6c2ec2a6", "weight": 0.8, "description": "来源"}, {"edge_id": "b11077b3-28f0-493f-ad93-a2f8ffce9773", "source_id": "83bbc7db-709a-48ed-a0d7-191d3bddcec6", "target_id": "ca2d7c9d-e316-4822-a28b-a14068075e7e", "weight": 0.8, "description": "来源"}, {"edge_id": "2dad4b8b-bae5-4105-ba6e-0cc92d0b7a60", "source_id": "83bbc7db-709a-48ed-a0d7-191d3bddcec6", "target_id": "4a9537b4-af71-4d48-9eb2-5c138148c1a0", "weight": 0.8, "description": "来源"}, {"edge_id": "92487df5-dcbd-4a1c-9e68-0147b94bed4e", "source_id": "83bbc7db-709a-48ed-a0d7-191d3bddcec6", "target_id": "2ccf1a6a-1a55-4379-b6ca-3f5a3365148d", "weight": 0.8, "description": "来源"}, {"edge_id": "e338b794-6b19-4229-b3d2-f37173a25961", "source_id": "83bbc7db-709a-48ed-a0d7-191d3bddcec6", "target_id": "fa30bc82-1ff7-405f-8b6a-d5a9fc6ebb26", "weight": 0.8, "description": "来源"}, {"edge_id": "032b6372-b9bd-4422-aab3-a5f1f45fc9cd", "source_id": "83bbc7db-709a-48ed-a0d7-191d3bddcec6", "target_id": "9148eaba-b30c-4857-8134-6ee724c2d6d7", "weight": 0.5, "description": "其他来源"}, {"edge_id": "c273f7a4-756b-442a-a2f0-ec93523d7363", "source_id": "ead0c57f-12ee-4640-a9fb-99ea3a8003d3", "target_id": "83bbc7db-709a-48ed-a0d7-191d3bddcec6", "weight": 1.0, "description": "引用"}, {"edge_id": "b73bc3d2-1a10-4be5-a7a0-ccff94bcf31f", "source_id": "9148eaba-b30c-4857-8134-6ee724c2d6d7", "target_id": "b52ee393-9fd0-428c-9e19-7e95e909741e", "weight": 1.0, "description": "引用"}, {"edge_id": "f3070a46-ae76-4dac-ae6c-6c557873fb2c", "source_id": "ead0c57f-12ee-4640-a9fb-99ea3a8003d3", "target_id": "b52ee393-9fd0-428c-9e19-7e95e909741e", "weight": 0.7, "description": "NVD引用"}, {"edge_id": "f9042696-20dd-4d23-b8af-b104065a31c9", "source_id": "b52ee393-9fd0-428c-9e19-7e95e909741e", "target_id": "3ce07780-6614-4232-bc5d-8dd309163f16", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "8ba1844b-9d8b-488b-962d-bc46efa599c0", "source_id": "b52ee393-9fd0-428c-9e19-7e95e909741e", "target_id": "372ad973-0d6a-4fa0-aa0f-f499c94187b7", "weight": 0.3333333333333333, "description": "引用"}]}