{"cve_id": "CVE-2023-32516", "published_date": "2023-08-24T12:15:07.990", "last_modified_date": "2024-11-21T08:03:31.027", "descriptions": [{"lang": "en", "value": "Unauth. Reflected Cross-Site Scripting (XSS) vulnerability in GloriaFood Restaurant Menu – Food Ordering System – Table Reservation plugin <= 2.3.6 versions."}], "references": [{"url": "https://patchstack.com/database/vulnerability/menu-ordering-reservations/wordpress-restaurant-menu-food-ordering-system-table-reservation-plugin-2-3-6-reflected-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}