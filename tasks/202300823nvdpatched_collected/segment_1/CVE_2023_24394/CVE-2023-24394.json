{"cve_id": "CVE-2023-24394", "published_date": "2023-08-25T11:15:07.363", "last_modified_date": "2024-11-21T07:47:46.650", "descriptions": [{"lang": "en", "value": "Auth. (admin+) Stored Cross-Site Scripting (XSS) vulnerability in Gopi Ramasamy iframe popup plugin <= 3.3 versions."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Scripting (XSS) Almacenada en el plugin iframe popup de <PERSON><PERSON> que afecta a las versiones 3.3 e inferiores. Para explotar esta vulnerabilidad hace falta estar autenticado y tener permisos de administrador o superior."}], "references": [{"url": "https://patchstack.com/database/vulnerability/iframe-popup/wordpress-iframe-popup-plugin-3-3-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}