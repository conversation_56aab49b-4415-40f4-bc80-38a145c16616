{"cve_id": "CVE-2023-32119", "cve_published_date": "2023-08-23T13:15:07.617", "cve_affected_cpes": [], "nodes": [{"node_id": "827db526-cb83-4d73-823d-2c3aebd1135a", "url": "https://nvd.nist.gov/vuln/detail/CVE-2023-32119", "title": "CVE-2023-32119", "node_type": "ROOT", "source": "ROOT", "depth": 0, "category_in_type": "", "importance_score": 1.0}, {"node_id": "34b7e2dd-bb62-400f-966f-bf350f95e41d", "url": "https://nvd.nist.gov", "title": "NVD", "node_type": "SOURCE", "source": "NVD", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "761a0cb8-27db-40c2-a888-d3206af95380", "url": "https://github", "title": "GITHUB", "node_type": "SOURCE", "source": "GITHUB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "28d55cf9-b8c8-4f0b-b824-35d8fa758d16", "url": "https://gitlab", "title": "GITLAB", "node_type": "SOURCE", "source": "GITLAB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "d49a7c2d-08ae-469d-a0b2-21b74fa3cd98", "url": "https://debian", "title": "DEBIAN", "node_type": "SOURCE", "source": "DEBIAN", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "f4916a7d-2abf-49fc-b0df-e8c15d50b6e5", "url": "https://redhat", "title": "REDHAT", "node_type": "SOURCE", "source": "REDHAT", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "31a8f2d0-d0f4-42ca-bde4-2f028b2d628b", "url": "https://apache", "title": "APACHE", "node_type": "SOURCE", "source": "APACHE", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "9c95b815-9a3f-4ea6-98c0-7e7a7f41077f", "url": "https://other.source", "title": "OTHER", "node_type": "SOURCE", "source": "OTHER", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "b55c78ad-3f2b-481c-bce4-0741a58fc929", "url": "https://patchstack.com/database/vulnerability/mail-integration-365/wordpress-wpo365-mail-integration-for-office-365-outlook-plugin-1-9-0-cross-site-scripting-xss-vulnerability", "title": "Wordpress wpo365 mail integration for office 365 outlook plugin 1 9 0 cross site scripting xss vulnerability", "node_type": "COMMON", "source": "OTHER", "depth": 2, "category_in_type": "common_url", "importance_score": 1.0}, {"node_id": "c992dabb-ef27-4bc4-b41a-52c180f55d2c", "url": "https://patchstack.com/wordpress-security", "title": "", "node_type": "COMMON", "source": "OTHER", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}, {"node_id": "a85f4d59-a1e3-48e1-a9d3-1834e240b2cc", "url": "https://patchstack.com/for-hosts", "title": "", "node_type": "COMMON", "source": "OTHER", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}], "edges": [{"edge_id": "3c57e423-5ad2-4024-b7ac-4ead046465db", "source_id": "827db526-cb83-4d73-823d-2c3aebd1135a", "target_id": "34b7e2dd-bb62-400f-966f-bf350f95e41d", "weight": 0.8, "description": "来源"}, {"edge_id": "d0d45702-d4d1-4065-a498-b7a9527164b4", "source_id": "827db526-cb83-4d73-823d-2c3aebd1135a", "target_id": "761a0cb8-27db-40c2-a888-d3206af95380", "weight": 0.8, "description": "来源"}, {"edge_id": "4c8ca3c6-c088-41f9-9073-bd869639624d", "source_id": "827db526-cb83-4d73-823d-2c3aebd1135a", "target_id": "28d55cf9-b8c8-4f0b-b824-35d8fa758d16", "weight": 0.8, "description": "来源"}, {"edge_id": "75013437-7194-4df2-9ca9-686c6dbab1ec", "source_id": "827db526-cb83-4d73-823d-2c3aebd1135a", "target_id": "d49a7c2d-08ae-469d-a0b2-21b74fa3cd98", "weight": 0.8, "description": "来源"}, {"edge_id": "429375dc-0ce8-4e31-9066-03ae8efa3e2a", "source_id": "827db526-cb83-4d73-823d-2c3aebd1135a", "target_id": "f4916a7d-2abf-49fc-b0df-e8c15d50b6e5", "weight": 0.8, "description": "来源"}, {"edge_id": "ca8ad943-8ed0-4ee3-8979-fddd31895fe5", "source_id": "827db526-cb83-4d73-823d-2c3aebd1135a", "target_id": "31a8f2d0-d0f4-42ca-bde4-2f028b2d628b", "weight": 0.8, "description": "来源"}, {"edge_id": "ec8177b8-841b-4cc1-8f50-1c8cfbda1501", "source_id": "827db526-cb83-4d73-823d-2c3aebd1135a", "target_id": "9c95b815-9a3f-4ea6-98c0-7e7a7f41077f", "weight": 0.5, "description": "其他来源"}, {"edge_id": "0d6ddd23-1c5f-4f93-a7c9-ad46be9c3f47", "source_id": "34b7e2dd-bb62-400f-966f-bf350f95e41d", "target_id": "827db526-cb83-4d73-823d-2c3aebd1135a", "weight": 1.0, "description": "引用"}, {"edge_id": "4de3bfc7-6924-4ad5-b63e-774b2a68a447", "source_id": "9c95b815-9a3f-4ea6-98c0-7e7a7f41077f", "target_id": "b55c78ad-3f2b-481c-bce4-0741a58fc929", "weight": 1.0, "description": "引用"}, {"edge_id": "d68e9448-91b9-4b92-8560-2b0de64c8ef6", "source_id": "34b7e2dd-bb62-400f-966f-bf350f95e41d", "target_id": "b55c78ad-3f2b-481c-bce4-0741a58fc929", "weight": 0.7, "description": "NVD引用"}, {"edge_id": "62b6d0ed-9eac-4451-ab38-f8ebc894be62", "source_id": "b55c78ad-3f2b-481c-bce4-0741a58fc929", "target_id": "c992dabb-ef27-4bc4-b41a-52c180f55d2c", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "cf914ff1-d601-4a36-93fa-4061c96f5700", "source_id": "b55c78ad-3f2b-481c-bce4-0741a58fc929", "target_id": "a85f4d59-a1e3-48e1-a9d3-1834e240b2cc", "weight": 0.3333333333333333, "description": "引用"}]}