{"cve_id": "CVE-2023-39801", "published_date": "2023-08-24T20:15:08.657", "last_modified_date": "2024-11-21T08:15:56.793", "descriptions": [{"lang": "en", "value": "A lack of exception handling in the Renault Easy Link Multimedia System Software Version 283C35519R allows attackers to cause a Denial of Service (DoS) via supplying crafted WMA files when connecting a device to the vehicle's USB plug and play feature."}], "references": [{"url": "https://github.com/zj3t/Automotive-vulnerabilities/blob/main/RENAULT/ZOE_EV_2021/Vuln%232/README.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}