{"cve_id": "CVE-2020-11711", "published_date": "2023-08-25T16:15:07.857", "last_modified_date": "2024-11-21T04:58:27.460", "descriptions": [{"lang": "en", "value": "An issue was discovered in Stormshield SNS 3.8.0. Authenticated Stored XSS in the admin login panel leads to SSL VPN credential theft. A malicious disclaimer file can be uploaded from the admin panel. The resulting file is rendered on the authentication interface of the admin panel. It is possible to inject malicious HTML content in order to execute JavaScript inside a victim's browser. This results in a stored XSS on the authentication interface of the admin panel. Moreover, an unsecured authentication form is present on the authentication interface of the SSL VPN captive portal. Users are allowed to save their credentials inside the browser. If an administrator saves his credentials through this unsecured form, these credentials could be stolen via the stored XSS on the admin panel without user interaction. Another possible exploitation would be modification of the authentication form of the admin panel into a malicious form."}, {"lang": "es", "value": "Se ha descubierto un problema en Stormshield SNS 3.8.0. El XSS almacenado autenticado en el panel de inicio de sesión del administrador conduce al robo de credenciales SSL VPN. Se puede cargar un archivo de renuncia malicioso desde el panel de administración. El archivo resultante se muestra en la interfaz de autenticación del panel de administración. Es posible inyectar contenido HTML malicioso para ejecutar JavaScript dentro del navegador de la víctima. Esto resulta en un XSS almacenado en la interfaz de autenticación del panel de administración. Además, hay un formulario de autenticación no seguro en la interfaz de autenticación del portal cautivo VPN SSL. Se permite a los usuarios guardar sus credenciales dentro del navegador. Si un administrador guarda sus credenciales a través de este formulario no seguro, estas credenciales podrían ser robadas a través del XSS almacenado en el panel de administración sin interacción del usuario. Otra posible explotación sería la modificación del formulario de autenticación del panel de administración en un formulario malicioso."}], "references": [{"url": "https://advisories.stormshield.eu/2020-011/", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://twitter.com/_ACKNAK_", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://www.digitemis.com/category/blog/actualite/", "source": "<EMAIL>", "tags": ["Not Applicable"]}]}