{"cve_id": "CVE-2022-46884", "published_date": "2023-08-24T17:15:08.380", "last_modified_date": "2024-11-21T07:31:14.470", "descriptions": [{"lang": "en", "value": "A potential use-after-free vulnerability existed in SVG Images if the Refresh Driver was destroyed at an inopportune time.  This could have lead to memory corruption or a potentially exploitable crash.\n*Note*: This advisory was added on December 13th, 2022 after discovering it was inadvertently left out of the original advisory. The fix was included in the original release of Firefox 106. This vulnerability affects Firefox < 106."}], "references": [{"url": "https://bugzilla.mozilla.org/show_bug.cgi?id=1786818", "source": "<EMAIL>", "tags": ["Issue Tracking", "Vendor Advisory"]}, {"url": "https://www.mozilla.org/security/advisories/mfsa2022-44/", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}