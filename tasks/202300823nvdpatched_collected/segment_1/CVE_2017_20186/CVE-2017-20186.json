{"cve_id": "CVE-2017-20186", "published_date": "2023-08-28T13:15:08.937", "last_modified_date": "2024-11-21T03:22:50.210", "descriptions": [{"lang": "en", "value": "** UNSUPPORTED WHEN ASSIGNED ** A vulnerability was found in nikooo777 ckSurf up to 1.19.2. It has been declared as problematic. This vulnerability affects the function SpecListMenuDead of the file csgo/addons/sourcemod/scripting/ckSurf/misc.sp of the component Spectator List Name Handler. The manipulation of the argument cleanName leads to denial of service. Upgrading to version 1.21.0 is able to address this issue. The name of the patch is fd6318d99083a06363091441a0614bd2f21068e6. It is recommended to upgrade the affected component. The identifier of this vulnerability is VDB-238156. NOTE: This vulnerability only affects products that are no longer supported by the maintainer."}], "references": [{"url": "https://forums.alliedmods.net/showthread.php?t=297179", "source": "<EMAIL>", "tags": ["Issue Tracking", "Third Party Advisory"]}, {"url": "https://github.com/nikooo777/ckSurf/commit/fd6318d99083a06363091441a0614bd2f21068e6", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/nikooo777/ckSurf/releases/tag/1.21.0", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://vuldb.com/?ctiid.238156", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://vuldb.com/?id.238156", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}