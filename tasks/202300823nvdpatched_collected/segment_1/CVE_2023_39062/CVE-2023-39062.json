{"cve_id": "CVE-2023-39062", "published_date": "2023-08-28T18:15:08.863", "last_modified_date": "2024-11-21T08:14:42.360", "descriptions": [{"lang": "en", "value": "Cross Site Scripting vulnerability in Spipu HTML2PDF before v.5.2.8 allows a remote attacker to execute arbitrary code via a crafted script to the forms.php."}], "references": [{"url": "https://github.com/afine-com/CVE-2023-39062", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://github.com/sectroyer/CVEs/tree/main/CVE-2023-39062", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://github.com/spipu/html2pdf/blob/92afd81823d62ad95eb9d034858311bb63aeb4ac/CHANGELOG.md", "source": "<EMAIL>", "tags": ["Release Notes"]}]}