{"cve_id": "CVE-2023-34758", "published_date": "2023-08-28T12:15:09.007", "last_modified_date": "2024-11-21T08:07:34.370", "descriptions": [{"lang": "en", "value": "Sliver from v1.5.x to v1.5.39 has an improper cryptographic implementation, which allows attackers to execute a man-in-the-middle attack via intercepted and crafted responses."}, {"lang": "es", "value": "Sliver desde la versión 1.5.x hasta la versión 1.5.39 tiene una implementación criptográfica incorrecta, que permite a los atacantes ejecutar un ataque man-in-the-middle a través de respuestas interceptadas y manipuladas."}], "references": [{"url": "https://github.com/BishopFox/sliver/releases/tag/v1.5.40", "source": "<EMAIL>", "tags": ["Release Notes"]}, {"url": "https://github.com/advisories/GHSA-8jxm-xp43-qh3q", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://github.com/tangent65536/Slivjacker", "source": "<EMAIL>", "tags": ["Broken Link"]}, {"url": "https://www.chtsecurity.com/news/04f41dcc-1851-463c-93bc-551323ad8091", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}