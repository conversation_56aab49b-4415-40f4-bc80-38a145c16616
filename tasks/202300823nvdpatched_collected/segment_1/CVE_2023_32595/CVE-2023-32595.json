{"cve_id": "CVE-2023-32595", "published_date": "2023-08-25T11:15:08.333", "last_modified_date": "2024-11-21T08:03:40.083", "descriptions": [{"lang": "en", "value": "Auth. (admin+) Stored Cross-Site Scripting (XSS) vulnerability in Palasthotel by <PERSON>, <PERSON><PERSON><PERSON> Search plugin <= 1.0.2 versions."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Scripting (XSS) Almacenado en el plugin Sunny Search de Palasthotel by <PERSON>, <PERSON><PERSON><PERSON> que afecta a las versiones 1.0.2 e inferiores. Para explotar esta vulnerabilidad hace falta estar autenticado y tener permisos de administrador o superior."}], "references": [{"url": "https://patchstack.com/database/vulnerability/fast-search-powered-by-solr/wordpress-sunny-search-plugin-1-0-2-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}