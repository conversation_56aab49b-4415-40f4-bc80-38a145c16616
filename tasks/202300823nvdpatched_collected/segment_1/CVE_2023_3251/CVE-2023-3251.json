{"cve_id": "CVE-2023-3251", "published_date": "2023-08-29T19:15:27.023", "last_modified_date": "2024-11-21T08:16:48.053", "descriptions": [{"lang": "en", "value": "\nA pass-back vulnerability exists where an authenticated, remote attacker with administrator privileges could uncover stored SMTP credentials within the Nessus application.This issue affects Nessus: before 10.6.0.\n\n\n\n\n\n\n\n"}], "references": [{"url": "https://www.tenable.com/security/tns-2023-29", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}