{"cve_id": "CVE-2023-26270", "published_date": "2023-08-28T01:15:09.117", "last_modified_date": "2024-11-21T07:51:01.310", "descriptions": [{"lang": "en", "value": "IBM Security Guardium Data Encryption (IBM Guardium Cloud Key Manager (GCKM) 1.10.3)) could allow a remote attacker to execute arbitrary code on the system, caused by an angular template injection flaw. By sending specially crafted request, an attacker could exploit this vulnerability to execute arbitrary code on the system.  IBM X-Force ID:  248119."}], "references": [{"url": "https://exchange.xforce.ibmcloud.com/vulnerabilities/248119", "source": "<EMAIL>", "tags": ["VDB Entry", "Vendor Advisory"]}, {"url": "https://www.ibm.com/support/pages/node/6995161", "source": "<EMAIL>", "tags": ["Patch", "Vendor Advisory"]}]}