{"cve_id": "CVE-2023-32603", "published_date": "2023-08-25T12:15:08.323", "last_modified_date": "2024-11-21T08:03:40.933", "descriptions": [{"lang": "en", "value": "Unauth. Reflected Cross-Site Scripting (XSS) vulnerability in RedNao Donations Made Easy – Smart Donations plugin <= 4.0.12 versions."}, {"lang": "es", "value": "Vulnerabilidad de Cross-Site Scripting (XSS) Reflejada en el plugin Donations Made Easy - Smart Donations de RedNao."}], "references": [{"url": "https://patchstack.com/database/vulnerability/smart-donations/wordpress-donations-made-easy-smart-donations-plugin-4-0-12-reflected-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}