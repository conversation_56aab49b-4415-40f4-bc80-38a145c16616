{"cve_id": "CVE-2023-0654", "published_date": "2023-08-29T16:15:08.747", "last_modified_date": "2024-11-21T07:37:33.873", "descriptions": [{"lang": "en", "value": "Due to a misconfiguration, the WARP Mobile Client (< 6.29) for Android was susceptible to a tapjacking attack. In the event that an attacker built a malicious application and managed to install it on a victim's device, the attacker would be able to trick the user into believing that the app shown on the screen was the WARP client when in reality it was the attacker's app.\n\n"}], "references": [{"url": "https://developers.cloudflare.com/warp-client/", "source": "<EMAIL>", "tags": ["Product"]}, {"url": "https://github.com/cloudflare/advisories/security/advisories/GHSA-5r97-pqv6-xpx7", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}