{"cve_id": "CVE-2023-34040", "published_date": "2023-08-24T13:15:07.453", "last_modified_date": "2024-11-21T08:06:27.340", "descriptions": [{"lang": "en", "value": "In Spring for Apache Kafka 3.0.9 and earlier and versions 2.9.10 and earlier, a possible deserialization attack vector existed, but only if unusual configuration was applied. An attacker would have to construct a malicious serialized object in one of the deserialization exception record headers.\n\nSpecifically, an application is vulnerable when all of the following are true:\n\n  *  The user does not configure an ErrorHandlingDeserializer for the key and/or value of the record\n  *  The user explicitly sets container properties checkDeserExWhenKeyNull and/or checkDeserExWhenValueNull container properties to true.\n  *  The user allows untrusted sources to publish to a Kafka topic\n\n\nBy default, these properties are false, and the container only attempts to deserialize the headers if an ErrorHandlingDeserializer is configured. The ErrorHandlingDeserializer prevents the vulnerability by removing any such malicious headers before processing the record.\n\n\n"}], "references": [{"url": "https://spring.io/security/cve-2023-34040", "source": "<EMAIL>", "tags": ["Mitigation", "Vendor Advisory"]}]}