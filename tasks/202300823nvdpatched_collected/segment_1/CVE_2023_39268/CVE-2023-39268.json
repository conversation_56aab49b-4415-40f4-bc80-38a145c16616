{"cve_id": "CVE-2023-39268", "published_date": "2023-08-29T20:15:09.830", "last_modified_date": "2024-11-21T08:15:01.323", "descriptions": [{"lang": "en", "value": "A memory corruption vulnerability in ArubaOS-Switch could lead to unauthenticated remote code execution by receiving specially crafted packets. Successful exploitation of this vulnerability results in the ability to execute arbitrary code as a privileged user on the underlying operating system."}, {"lang": "es", "value": "Una vulnerabilidad de corrupción de memoria en ArubaOS-Switch podría provocar la ejecución remota de código no autenticado al recibir paquetes especialmente manipulados. La explotación exitosa de esta vulnerabilidad da como resultado la capacidad de ejecutar código arbitrario como usuario privilegiado en el sistema operativo subyacente."}], "references": [{"url": "https://www.arubanetworks.com/assets/alert/ARUBA-PSA-2023-013.txt", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}