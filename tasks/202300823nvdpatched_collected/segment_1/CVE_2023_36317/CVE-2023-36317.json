{"cve_id": "CVE-2023-36317", "published_date": "2023-08-23T22:15:08.550", "last_modified_date": "2024-11-21T08:09:32.450", "descriptions": [{"lang": "en", "value": "Cross Site Scripting (XSS) vulnerability in sourcecodester Student Study Center Desk Management System 1.0 allows attackers to run arbitrary code via crafted GET request to web application URL."}, {"lang": "es", "value": "Una vulnerabilidad de Cross-Site Scripting (XSS) en sourcecodester Student Study Center Desk Management System v1.0 permite a los atacantes ejecutar código arbitrario a través de una solicitud GET a la URL de la aplicación web. "}], "references": [{"url": "https://github.com/oye-ujjwal/CVE/blob/main/CVE-2023-36317", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://www.sourcecodester.com", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://www.sourcecodester.com/php/16298/student-study-center-desk-management-system-using-php-oop-and-mysql-db-free-source-code", "source": "<EMAIL>", "tags": ["Product"]}]}