{"cve_id": "CVE-2023-36317", "cve_published_date": "2023-08-23T22:15:08.550", "cve_affected_cpes": [], "nodes": [{"node_id": "c31c3124-3688-4b76-95e8-fb0fd08a467a", "url": "https://nvd.nist.gov/vuln/detail/CVE-2023-36317", "title": "CVE-2023-36317", "node_type": "ROOT", "source": "ROOT", "depth": 0, "category_in_type": "", "importance_score": 1.0}, {"node_id": "ab5426a7-d828-4630-bed6-7d4597122d0f", "url": "https://nvd.nist.gov", "title": "NVD", "node_type": "SOURCE", "source": "NVD", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "2f93f074-6fe5-4d3d-ae71-642d015d995f", "url": "https://github", "title": "GITHUB", "node_type": "SOURCE", "source": "GITHUB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "ecbd1b33-f758-4f60-9b14-6ebac1cf12e8", "url": "https://gitlab", "title": "GITLAB", "node_type": "SOURCE", "source": "GITLAB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "698f4c2f-f39d-4a52-a02d-66d52277af5c", "url": "https://debian", "title": "DEBIAN", "node_type": "SOURCE", "source": "DEBIAN", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "31c16487-e1d2-490d-9b55-90be86c2f1b6", "url": "https://redhat", "title": "REDHAT", "node_type": "SOURCE", "source": "REDHAT", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "93a7c21c-3507-4816-9669-6696d853d762", "url": "https://apache", "title": "APACHE", "node_type": "SOURCE", "source": "APACHE", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "38e98da9-8531-4f9d-8e3c-8627ea34aba8", "url": "https://other.source", "title": "OTHER", "node_type": "SOURCE", "source": "OTHER", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "27fd3c12-3102-4590-8fd2-27e2db7fe54f", "url": "https://github.com/oye-ujjwal/CVE/blob/main/CVE-2023-36317", "title": "Cve 2023 36317", "node_type": "COMMON", "source": "GITHUB", "depth": 2, "category_in_type": "", "importance_score": 1.0}, {"node_id": "0412c6ac-f5a3-4ed6-ae2b-8c9bfbd4cfc0", "url": "https://www.sourcecodester.com/php/16298/student-study-center-desk-management-system-using-php-oop-and-mysql-db-free-source-code", "title": "Student study center desk management system using php oop and mysql db free source code", "node_type": "COMMON", "source": "OTHER", "depth": 2, "category_in_type": "common_url", "importance_score": 1.0}, {"node_id": "6654f6a9-aaa4-4119-aaa3-2954d56f546d", "url": "https://www.sourcecodester.com/", "title": "Free Source Code Projects and Tutorials - sourcecodester.com", "node_type": "COMMON", "source": "OTHER", "depth": 2, "category_in_type": "common_url", "importance_score": 1.0}, {"node_id": "31207d6d-9500-4449-a38d-a76b76808bfc", "url": "https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax", "title": "", "node_type": "COMMON", "source": "GITHUB", "depth": 3, "category_in_type": "common_github_url", "importance_score": 0.0}, {"node_id": "bf340fa0-cb8d-4c04-9c60-a79ea5b10e1e", "url": "https://github.com/oye-ujjwal", "title": "", "node_type": "COMMON", "source": "GITHUB", "depth": 3, "category_in_type": "common_github_url", "importance_score": 0.0}, {"node_id": "b7b3b249-5ac6-4f4d-a219-68d2cac0c637", "url": "https://www.sourcecodester.com/blog", "title": "", "node_type": "COMMON", "source": "OTHER", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}, {"node_id": "a1644636-9315-4bb6-9a4c-409522a1a1e9", "url": "https://www.sourcecodester.com/project", "title": "", "node_type": "COMMON", "source": "OTHER", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}, {"node_id": "35bf3eb4-e6b3-42cf-84f8-e88b9f5db04b", "url": "https://www.sourcecodester.com/users/razormist", "title": "", "node_type": "COMMON", "source": "OTHER", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}, {"node_id": "50def6cf-b4b7-438b-bf8b-0c8a570a912e", "url": "https://www.sourcecodester.com/user/login", "title": "", "node_type": "COMMON", "source": "OTHER", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}], "edges": [{"edge_id": "50dfaca2-c4c8-4367-bb63-4cc006f0148a", "source_id": "c31c3124-3688-4b76-95e8-fb0fd08a467a", "target_id": "ab5426a7-d828-4630-bed6-7d4597122d0f", "weight": 0.8, "description": "来源"}, {"edge_id": "f90b3910-14e3-4296-9c56-1117fa551a8c", "source_id": "c31c3124-3688-4b76-95e8-fb0fd08a467a", "target_id": "2f93f074-6fe5-4d3d-ae71-642d015d995f", "weight": 0.8, "description": "来源"}, {"edge_id": "fcbaf4a4-0625-4d98-8232-909ee69580bf", "source_id": "c31c3124-3688-4b76-95e8-fb0fd08a467a", "target_id": "ecbd1b33-f758-4f60-9b14-6ebac1cf12e8", "weight": 0.8, "description": "来源"}, {"edge_id": "6a860a99-fca3-487b-ab0a-baa1e7433d7b", "source_id": "c31c3124-3688-4b76-95e8-fb0fd08a467a", "target_id": "698f4c2f-f39d-4a52-a02d-66d52277af5c", "weight": 0.8, "description": "来源"}, {"edge_id": "038c443b-9cd0-49f6-bc99-d810773c7213", "source_id": "c31c3124-3688-4b76-95e8-fb0fd08a467a", "target_id": "31c16487-e1d2-490d-9b55-90be86c2f1b6", "weight": 0.8, "description": "来源"}, {"edge_id": "725e4d4a-fb09-4117-b907-6a622f5eb319", "source_id": "c31c3124-3688-4b76-95e8-fb0fd08a467a", "target_id": "93a7c21c-3507-4816-9669-6696d853d762", "weight": 0.8, "description": "来源"}, {"edge_id": "16938fcc-c341-4835-bf4c-dac433aebc5a", "source_id": "c31c3124-3688-4b76-95e8-fb0fd08a467a", "target_id": "38e98da9-8531-4f9d-8e3c-8627ea34aba8", "weight": 0.5, "description": "其他来源"}, {"edge_id": "de5c74f2-e64d-4d98-b625-8a71eba7d35a", "source_id": "ab5426a7-d828-4630-bed6-7d4597122d0f", "target_id": "c31c3124-3688-4b76-95e8-fb0fd08a467a", "weight": 1.0, "description": "引用"}, {"edge_id": "d2dc790f-d76e-41be-98b8-d015b9f92159", "source_id": "2f93f074-6fe5-4d3d-ae71-642d015d995f", "target_id": "27fd3c12-3102-4590-8fd2-27e2db7fe54f", "weight": 1.0, "description": "引用"}, {"edge_id": "85170010-bc31-4dea-85ad-84e8dabc5ec4", "source_id": "ab5426a7-d828-4630-bed6-7d4597122d0f", "target_id": "27fd3c12-3102-4590-8fd2-27e2db7fe54f", "weight": 0.7, "description": "NVD引用"}, {"edge_id": "0f7ebe17-0171-4e9a-8ae1-e193dd570646", "source_id": "38e98da9-8531-4f9d-8e3c-8627ea34aba8", "target_id": "0412c6ac-f5a3-4ed6-ae2b-8c9bfbd4cfc0", "weight": 1.0, "description": "引用"}, {"edge_id": "952a7305-acbd-4e5e-bf3d-fb94f8aa786b", "source_id": "ab5426a7-d828-4630-bed6-7d4597122d0f", "target_id": "0412c6ac-f5a3-4ed6-ae2b-8c9bfbd4cfc0", "weight": 0.7, "description": "NVD引用"}, {"edge_id": "c4560d24-6a83-438f-b06c-5af19f2ac440", "source_id": "38e98da9-8531-4f9d-8e3c-8627ea34aba8", "target_id": "6654f6a9-aaa4-4119-aaa3-2954d56f546d", "weight": 1.0, "description": "引用"}, {"edge_id": "d7215501-76f3-4506-808f-4ae2977cfad6", "source_id": "ab5426a7-d828-4630-bed6-7d4597122d0f", "target_id": "6654f6a9-aaa4-4119-aaa3-2954d56f546d", "weight": 0.7, "description": "NVD引用"}, {"edge_id": "65fe615d-b78a-4b7c-90ea-fa5b056a181f", "source_id": "27fd3c12-3102-4590-8fd2-27e2db7fe54f", "target_id": "31207d6d-9500-4449-a38d-a76b76808bfc", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "8f854db6-f12d-4916-9de7-cb7ed3d53c02", "source_id": "2f93f074-6fe5-4d3d-ae71-642d015d995f", "target_id": "31207d6d-9500-4449-a38d-a76b76808bfc", "weight": 0.9, "description": "来源引用"}, {"edge_id": "7c3b44c0-9b26-46a1-8364-f81d188fe776", "source_id": "27fd3c12-3102-4590-8fd2-27e2db7fe54f", "target_id": "bf340fa0-cb8d-4c04-9c60-a79ea5b10e1e", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "c0a8c9fc-0b2c-46f3-b22e-746101a6b149", "source_id": "2f93f074-6fe5-4d3d-ae71-642d015d995f", "target_id": "bf340fa0-cb8d-4c04-9c60-a79ea5b10e1e", "weight": 0.9, "description": "来源引用"}, {"edge_id": "4137021c-65a0-434f-9b20-81f4d0c6a85d", "source_id": "0412c6ac-f5a3-4ed6-ae2b-8c9bfbd4cfc0", "target_id": "b7b3b249-5ac6-4f4d-a219-68d2cac0c637", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "9c5acf05-2250-47ba-bda1-a6c32b10989a", "source_id": "0412c6ac-f5a3-4ed6-ae2b-8c9bfbd4cfc0", "target_id": "a1644636-9315-4bb6-9a4c-409522a1a1e9", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "8f744fa4-a575-410a-b3a8-b9e90a132de1", "source_id": "6654f6a9-aaa4-4119-aaa3-2954d56f546d", "target_id": "35bf3eb4-e6b3-42cf-84f8-e88b9f5db04b", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "2983296e-3cf8-4c6a-8926-2c77f19f567e", "source_id": "6654f6a9-aaa4-4119-aaa3-2954d56f546d", "target_id": "50def6cf-b4b7-438b-bf8b-0c8a570a912e", "weight": 0.3333333333333333, "description": "引用"}]}