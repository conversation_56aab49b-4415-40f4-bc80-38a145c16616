{"cve_id": "CVE-2023-34971", "published_date": "2023-08-24T17:15:08.597", "last_modified_date": "2024-11-21T08:07:44.907", "descriptions": [{"lang": "en", "value": "An inadequate encryption strength vulnerability has been reported to affect QNAP operating systems. If exploited, the vulnerability possibly allows local network clients to decrypt the data using brute force attacks via unspecified vectors.\n\nWe have already fixed the vulnerability in the following versions:\nQTS 5.0.1.2425 build 20230609 and later\nQTS 5.1.0.2444 build 20230629 and later\nQTS 4.5.4.2467 build 20230718 and later\nQuTS hero h5.1.0.2424 build 20230609 and later\nQuTS hero h4.5.4.2476 build 20230728 and later\n"}], "references": [{"url": "https://www.qnap.com/en/security-advisory/qsa-23-60", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}