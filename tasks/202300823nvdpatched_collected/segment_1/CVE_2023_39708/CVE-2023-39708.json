{"cve_id": "CVE-2023-39708", "published_date": "2023-08-28T14:15:09.033", "last_modified_date": "2024-11-21T08:15:51.280", "descriptions": [{"lang": "en", "value": "A stored cross-site scripting (XSS) vulnerability in Free and Open Source Inventory Management System v1.0 allows attackers to execute arbitrary web scripts or HTML via injecting a crafted payload into the Add New parameter under the New Buy section."}], "references": [{"url": "https://gist.github.com/Arajawat007/6c544ae8bebd2a36926fd3fdc8d4d5c2#file-cve-2023-39708", "source": "<EMAIL>", "tags": ["Exploit"]}, {"url": "https://www.sourcecodester.com", "source": "<EMAIL>", "tags": ["Broken Link"]}, {"url": "https://www.sourcecodester.com/php/16741/free-and-open-source-inventory-management-system-php-source-code.html", "source": "<EMAIL>", "tags": ["Product"]}]}