{"cve_id": "CVE-2023-38974", "published_date": "2023-08-25T01:15:08.720", "last_modified_date": "2024-11-21T08:14:33.500", "descriptions": [{"lang": "en", "value": "A stored cross-site scripting (XSS) vulnerability in the Edit Category function of Badaso v2.9.7 allows attackers to execute arbitrary web scripts or HTML via a crafted payload injected into the Title parameter."}, {"lang": "es", "value": "Una vulnerabilidad de Cross-Site Scripting (XSS) en la función \"Edit Category\" de Badaso v2.9.7 permite a los atacantes ejecutar scripts web o scripts HTML arbitrarios a través de un payload manipulado inyectado en el parámetro \"Title\". "}], "references": [{"url": "https://github.com/anh91/uasoft-indonesia--badaso/blob/main/XSS4.md", "source": "<EMAIL>", "tags": ["Exploit"]}]}