{"cve_id": "CVE-2023-32509", "cve_published_date": "2023-08-23T15:15:08.403", "cve_affected_cpes": [], "nodes": [{"node_id": "50fd6d86-bc61-4106-8a6d-4caafc54a97b", "url": "https://nvd.nist.gov/vuln/detail/CVE-2023-32509", "title": "CVE-2023-32509", "node_type": "ROOT", "source": "ROOT", "depth": 0, "category_in_type": "", "importance_score": 1.0}, {"node_id": "123b8f1d-4b27-4aae-a726-7360a49f2884", "url": "https://nvd.nist.gov", "title": "NVD", "node_type": "SOURCE", "source": "NVD", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "680d75ed-bf7b-4070-8f7c-efe426a16f88", "url": "https://github", "title": "GITHUB", "node_type": "SOURCE", "source": "GITHUB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "279690cc-7951-4eae-abad-636c802c2385", "url": "https://gitlab", "title": "GITLAB", "node_type": "SOURCE", "source": "GITLAB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "ad11ff9f-cf79-4555-adeb-be6ae5d1fc9e", "url": "https://debian", "title": "DEBIAN", "node_type": "SOURCE", "source": "DEBIAN", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "71ee5873-0555-491c-9501-a8f6ea5092c0", "url": "https://redhat", "title": "REDHAT", "node_type": "SOURCE", "source": "REDHAT", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "53ffb780-b7b5-4838-b044-2beca5<PERSON>fe", "url": "https://apache", "title": "APACHE", "node_type": "SOURCE", "source": "APACHE", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "c9cf847c-d620-4648-9e78-bbff450de1dd", "url": "https://other.source", "title": "OTHER", "node_type": "SOURCE", "source": "OTHER", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "684ac1a2-6df0-4729-89a7-4128568f2930", "url": "https://patchstack.com/database/vulnerability/order-your-posts-manually/wordpress-order-your-posts-manually-plugin-2-2-5-reflected-cross-site-scripting-xss-vulnerability", "title": "Wordpress order your posts manually plugin 2 2 5 reflected cross site scripting xss vulnerability", "node_type": "COMMON", "source": "OTHER", "depth": 2, "category_in_type": "common_url", "importance_score": 1.0}, {"node_id": "9ab97b05-49ac-4663-ad0e-3e8f8ca0a1ed", "url": "https://patchstack.com/wordpress-security", "title": "", "node_type": "COMMON", "source": "OTHER", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}, {"node_id": "bb270945-83a0-46ff-bf5f-0323fe5ffe13", "url": "https://patchstack.com/for-hosts", "title": "", "node_type": "COMMON", "source": "OTHER", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}], "edges": [{"edge_id": "5f5a879b-847f-4743-ac2a-c984f1a2008b", "source_id": "50fd6d86-bc61-4106-8a6d-4caafc54a97b", "target_id": "123b8f1d-4b27-4aae-a726-7360a49f2884", "weight": 0.8, "description": "来源"}, {"edge_id": "cda89335-eb27-421a-b176-4484df32e71b", "source_id": "50fd6d86-bc61-4106-8a6d-4caafc54a97b", "target_id": "680d75ed-bf7b-4070-8f7c-efe426a16f88", "weight": 0.8, "description": "来源"}, {"edge_id": "0df81516-8a36-45f7-b1d1-c7bca3d79cb5", "source_id": "50fd6d86-bc61-4106-8a6d-4caafc54a97b", "target_id": "279690cc-7951-4eae-abad-636c802c2385", "weight": 0.8, "description": "来源"}, {"edge_id": "62398cfb-d3bb-42be-858a-5b36e87128d3", "source_id": "50fd6d86-bc61-4106-8a6d-4caafc54a97b", "target_id": "ad11ff9f-cf79-4555-adeb-be6ae5d1fc9e", "weight": 0.8, "description": "来源"}, {"edge_id": "e03f76bd-8a20-494f-9c11-03103b586d19", "source_id": "50fd6d86-bc61-4106-8a6d-4caafc54a97b", "target_id": "71ee5873-0555-491c-9501-a8f6ea5092c0", "weight": 0.8, "description": "来源"}, {"edge_id": "c1da9aa6-4f4c-4113-ac21-2c92f86fbc74", "source_id": "50fd6d86-bc61-4106-8a6d-4caafc54a97b", "target_id": "53ffb780-b7b5-4838-b044-2beca5<PERSON>fe", "weight": 0.8, "description": "来源"}, {"edge_id": "476f4bbd-3bdf-4162-91ac-0880f86534cd", "source_id": "50fd6d86-bc61-4106-8a6d-4caafc54a97b", "target_id": "c9cf847c-d620-4648-9e78-bbff450de1dd", "weight": 0.5, "description": "其他来源"}, {"edge_id": "9c472605-57d5-434b-b443-d38bd40f2959", "source_id": "123b8f1d-4b27-4aae-a726-7360a49f2884", "target_id": "50fd6d86-bc61-4106-8a6d-4caafc54a97b", "weight": 1.0, "description": "引用"}, {"edge_id": "160c9ed0-5d97-456d-b8ab-ce5c70e6f224", "source_id": "c9cf847c-d620-4648-9e78-bbff450de1dd", "target_id": "684ac1a2-6df0-4729-89a7-4128568f2930", "weight": 1.0, "description": "引用"}, {"edge_id": "e4d1eb8f-a432-4c97-ae28-bc640185ea4e", "source_id": "123b8f1d-4b27-4aae-a726-7360a49f2884", "target_id": "684ac1a2-6df0-4729-89a7-4128568f2930", "weight": 0.7, "description": "NVD引用"}, {"edge_id": "eee82cc6-0861-425c-96bb-c95934a1d444", "source_id": "684ac1a2-6df0-4729-89a7-4128568f2930", "target_id": "9ab97b05-49ac-4663-ad0e-3e8f8ca0a1ed", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "98562763-9263-41b9-a632-8ab45466f4c3", "source_id": "684ac1a2-6df0-4729-89a7-4128568f2930", "target_id": "bb270945-83a0-46ff-bf5f-0323fe5ffe13", "weight": 0.3333333333333333, "description": "引用"}]}