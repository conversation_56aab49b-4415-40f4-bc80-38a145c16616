{"cve_id": "CVE-2023-39707", "published_date": "2023-08-25T20:15:08.443", "last_modified_date": "2024-11-21T08:15:51.120", "descriptions": [{"lang": "en", "value": "A stored cross-site scripting (XSS) vulnerability in Free and Open Source Inventory Management System v1.0 allows attackers to execute arbitrary web scripts or HTML via injecting a crafted payload into the Add Expense parameter under the Expense section."}, {"lang": "es", "value": "Una vulnerabilidad de Cross-Site Scripting (XSS) Almacenada en Free and Open Source Inventory Management System v1.0 permite a los atacantes ejecutar secuencias de comandos web o HTML arbitrarias mediante la inyección de una carga útil diseñada en el parámetro Add Expense de la sección Expense."}], "references": [{"url": "https://gist.github.com/Arajawat007/b94d7ce74fcf16014e282a9b525f4555#file-cve-2023-39707", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://www.sourcecodester.com/", "source": "<EMAIL>", "tags": ["Not Applicable"]}, {"url": "https://www.sourcecodester.com/php/16741/free-and-open-source-inventory-management-system-php-source-code.html", "source": "<EMAIL>", "tags": ["Product"]}]}