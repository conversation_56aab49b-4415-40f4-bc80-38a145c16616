{"cve_id": "CVE-2023-39348", "published_date": "2023-08-28T20:15:08.107", "last_modified_date": "2024-11-21T08:15:12.497", "descriptions": [{"lang": "en", "value": "Spinnaker is an open source, multi-cloud continuous delivery platform. Log output when updating GitHub status is improperly set to FULL always.  It's recommended to apply the patch and rotate the GitHub token used for github status notifications.  Given that this would output github tokens to a log system, the risk is slightly higher than a \"low\" since token exposure could grant elevated access to repositories outside of control.  If using READ restricted tokens, the exposure is such that the token itself could be used to access resources otherwise restricted from reads. This only affects users of GitHub Status Notifications. This issue has been addressed in pull request 1316. Users are advised to upgrade. Users unable to upgrade should disable GH Status Notifications, Filter their logs for Echo log data and use read-only tokens that are limited in scope."}], "references": [{"url": "https://github.com/spinnaker/echo/pull/1316", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/spinnaker/spinnaker/security/advisories/GHSA-rq5c-hvw6-8pr7", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}