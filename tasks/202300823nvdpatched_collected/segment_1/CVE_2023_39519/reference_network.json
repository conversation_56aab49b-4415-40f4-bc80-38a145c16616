{"cve_id": "CVE-2023-39519", "cve_published_date": "2023-08-24T23:15:08.907", "cve_affected_cpes": [], "nodes": [{"node_id": "5f99cb45-b042-4ae5-8581-c1b403d5d610", "url": "https://nvd.nist.gov/vuln/detail/CVE-2023-39519", "title": "CVE-2023-39519", "node_type": "ROOT", "source": "ROOT", "depth": 0, "category_in_type": "", "importance_score": 1.0}, {"node_id": "90224c2f-a9f4-482b-b977-5d6a74753428", "url": "https://nvd.nist.gov", "title": "NVD", "node_type": "SOURCE", "source": "NVD", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "eec78721-390a-45dd-b2d1-ffccb2948d62", "url": "https://github", "title": "GITHUB", "node_type": "SOURCE", "source": "GITHUB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "67a518ee-8ac2-426b-90e9-2c35ecbbc7e3", "url": "https://gitlab", "title": "GITLAB", "node_type": "SOURCE", "source": "GITLAB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "a40550e0-9698-46ef-9a8f-504f518e028e", "url": "https://debian", "title": "DEBIAN", "node_type": "SOURCE", "source": "DEBIAN", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "3ee86896-fc69-4074-98b2-503a22c3ebc1", "url": "https://redhat", "title": "REDHAT", "node_type": "SOURCE", "source": "REDHAT", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "812a3eec-bb74-4b2d-8a25-e3e4ce99d5be", "url": "https://apache", "title": "APACHE", "node_type": "SOURCE", "source": "APACHE", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "cb390187-25e4-437f-afeb-5516b0caafe3", "url": "https://other.source", "title": "OTHER", "node_type": "SOURCE", "source": "OTHER", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "3c147727-e7e4-4fc3-ae9a-39ba9e2a9e81", "url": "https://github.com/CloudExplorer-Dev/CloudExplorer-Lite/security/advisories/GHSA-hh2g-77xq-x4vq", "title": "Ghsa hh2g 77xq x4vq", "node_type": "COMMON", "source": "GITHUB", "depth": 2, "category_in_type": "common_github_url", "importance_score": 1.0}, {"node_id": "0da47a56-5bcc-451d-bc74-140014382c07", "url": "https://github.com/CloudExplorer-Dev/CloudExplorer-Lite/releases/tag/v1.4.0", "title": "V1", "node_type": "COMMON", "source": "GITHUB", "depth": 2, "category_in_type": "common_github_url", "importance_score": 1.0}, {"node_id": "ead485ea-8b5d-4847-b91f-084518a9cc1d", "url": "https://github.com/CloudExplorer-Dev/CloudExplorer-Lite/commit/f852948d093f819cc9cb93cc41a6579cbcb8b721", "title": "", "node_type": "PATCH", "source": "GITHUB", "depth": 3, "category_in_type": "git_commit", "importance_score": 0.0}, {"node_id": "22b587ae-5e97-403e-a90a-c0e6a5c195c9", "url": "https://docs.github.com/search-github/github-code-search/understanding-github-code-search-syntax", "title": "", "node_type": "COMMON", "source": "GITHUB", "depth": 3, "category_in_type": "common_github_url", "importance_score": 0.0}, {"node_id": "2ab5ff5b-a56b-4af9-9d91-43efc67ff160", "url": "https://github.com/CloudExplorer-Dev/CloudExplorer-Lite/tree/v1.4.0", "title": "", "node_type": "COMMON", "source": "GITHUB", "depth": 3, "category_in_type": "common_github_url", "importance_score": 0.0}], "edges": [{"edge_id": "33b88b4a-6697-4b60-a852-e78edd88f879", "source_id": "5f99cb45-b042-4ae5-8581-c1b403d5d610", "target_id": "90224c2f-a9f4-482b-b977-5d6a74753428", "weight": 0.8, "description": "来源"}, {"edge_id": "a96bd408-217f-4ae0-8bf8-4fb9433da599", "source_id": "5f99cb45-b042-4ae5-8581-c1b403d5d610", "target_id": "eec78721-390a-45dd-b2d1-ffccb2948d62", "weight": 0.8, "description": "来源"}, {"edge_id": "313e1659-c87c-43fc-9dd4-f2c261a0eabb", "source_id": "5f99cb45-b042-4ae5-8581-c1b403d5d610", "target_id": "67a518ee-8ac2-426b-90e9-2c35ecbbc7e3", "weight": 0.8, "description": "来源"}, {"edge_id": "d0c1909a-7613-4055-90f5-a582afe8546d", "source_id": "5f99cb45-b042-4ae5-8581-c1b403d5d610", "target_id": "a40550e0-9698-46ef-9a8f-504f518e028e", "weight": 0.8, "description": "来源"}, {"edge_id": "55c156c8-50b7-4d53-a762-3844d410eccd", "source_id": "5f99cb45-b042-4ae5-8581-c1b403d5d610", "target_id": "3ee86896-fc69-4074-98b2-503a22c3ebc1", "weight": 0.8, "description": "来源"}, {"edge_id": "2c0e98e6-b9fd-410b-9f1a-9c73ffb23b22", "source_id": "5f99cb45-b042-4ae5-8581-c1b403d5d610", "target_id": "812a3eec-bb74-4b2d-8a25-e3e4ce99d5be", "weight": 0.8, "description": "来源"}, {"edge_id": "52e636aa-3420-44bb-a3bb-4059afe5b524", "source_id": "5f99cb45-b042-4ae5-8581-c1b403d5d610", "target_id": "cb390187-25e4-437f-afeb-5516b0caafe3", "weight": 0.5, "description": "其他来源"}, {"edge_id": "6b515b8c-6765-4167-aabe-20b5ed9fa000", "source_id": "90224c2f-a9f4-482b-b977-5d6a74753428", "target_id": "5f99cb45-b042-4ae5-8581-c1b403d5d610", "weight": 1.0, "description": "引用"}, {"edge_id": "4054e9c3-1ff7-4b54-a655-5ad5dfc289e7", "source_id": "eec78721-390a-45dd-b2d1-ffccb2948d62", "target_id": "3c147727-e7e4-4fc3-ae9a-39ba9e2a9e81", "weight": 1.0, "description": "引用"}, {"edge_id": "bd009238-d7ab-48d4-8d95-10dc669a8e6b", "source_id": "90224c2f-a9f4-482b-b977-5d6a74753428", "target_id": "3c147727-e7e4-4fc3-ae9a-39ba9e2a9e81", "weight": 0.7, "description": "NVD引用"}, {"edge_id": "053fca2f-6a57-470c-a682-33b8e768f322", "source_id": "eec78721-390a-45dd-b2d1-ffccb2948d62", "target_id": "0da47a56-5bcc-451d-bc74-140014382c07", "weight": 1.0, "description": "引用"}, {"edge_id": "ce401995-5e2f-456c-8d79-5fac90cb6c39", "source_id": "90224c2f-a9f4-482b-b977-5d6a74753428", "target_id": "0da47a56-5bcc-451d-bc74-140014382c07", "weight": 0.7, "description": "NVD引用"}, {"edge_id": "7511c732-a002-46e9-a84e-25c9b6a70f23", "source_id": "0da47a56-5bcc-451d-bc74-140014382c07", "target_id": "ead485ea-8b5d-4847-b91f-084518a9cc1d", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "27f20380-4b6b-4c5f-8557-d10c56356cf1", "source_id": "eec78721-390a-45dd-b2d1-ffccb2948d62", "target_id": "ead485ea-8b5d-4847-b91f-084518a9cc1d", "weight": 0.9, "description": "来源引用"}, {"edge_id": "f8acde90-a5d8-4a6e-93ed-fc30a83e275e", "source_id": "5f99cb45-b042-4ae5-8581-c1b403d5d610", "target_id": "ead485ea-8b5d-4847-b91f-084518a9cc1d", "weight": 0.8, "description": "关联补丁"}, {"edge_id": "30dd5bc8-3992-4a3a-88cf-a0525b97f4dd", "source_id": "0da47a56-5bcc-451d-bc74-140014382c07", "target_id": "22b587ae-5e97-403e-a90a-c0e6a5c195c9", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "0118939b-00c6-4332-bdd8-4ba85bd33032", "source_id": "eec78721-390a-45dd-b2d1-ffccb2948d62", "target_id": "22b587ae-5e97-403e-a90a-c0e6a5c195c9", "weight": 0.9, "description": "来源引用"}, {"edge_id": "9761ca6e-cc98-423c-9281-793cc30af554", "source_id": "0da47a56-5bcc-451d-bc74-140014382c07", "target_id": "2ab5ff5b-a56b-4af9-9d91-43efc67ff160", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "c4dacf98-f9c6-419d-a916-9492b93088a7", "source_id": "eec78721-390a-45dd-b2d1-ffccb2948d62", "target_id": "2ab5ff5b-a56b-4af9-9d91-43efc67ff160", "weight": 0.9, "description": "来源引用"}]}