{"cve_id": "CVE-2023-24621", "published_date": "2023-08-25T20:15:07.983", "last_modified_date": "2024-11-21T07:48:14.927", "descriptions": [{"lang": "en", "value": "An issue was discovered in Esoteric YamlBeans through 1.15. It allows untrusted deserialisation to Java classes by default, where the data and class are controlled by the author of the YAML document being processed."}, {"lang": "es", "value": "Se descubrió un problema en YamlBeans de Esoteric a través de 1.15. Permite la deserialización no confiable a clases Java de forma predeterminada, donde los datos y la clase son controlados por el autor del documento YAML que se está procesando.\n"}], "references": [{"url": "https://contrastsecurity.com", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://github.com/Contrast-Security-OSS/yamlbeans/blob/main/SECURITY.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://github.com/EsotericSoftware", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}