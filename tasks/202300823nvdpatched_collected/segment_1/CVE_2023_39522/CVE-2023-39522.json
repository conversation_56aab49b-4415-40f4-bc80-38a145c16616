{"cve_id": "CVE-2023-39522", "published_date": "2023-08-29T18:15:08.753", "last_modified_date": "2024-11-21T08:15:35.757", "descriptions": [{"lang": "en", "value": "goauthentik is an open-source Identity Provider. In affected versions using a recovery flow with an identification stage an attacker is able to determine if a username exists. Only setups configured with a recovery flow are impacted by this. Anyone with a user account on a system with the recovery flow described above is susceptible to having their username/email revealed as existing. An attacker can easily enumerate and check users' existence using the recovery flow, as a clear message is shown when a user doesn't exist. Depending on configuration this can either be done by username, email, or both. This issue has been addressed in versions 2023.5.6 and 2023.6.2. Users are advised to upgrade. There are no known workarounds for this issue."}], "references": [{"url": "https://github.com/goauthentik/authentik/commit/aa874dd92a770d5f8cd8f265b7cdd31cd73a4599", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/goauthentik/authentik/security/advisories/GHSA-vmf9-6pcv-xr87", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}