{"cve_id": "CVE-2023-32077", "published_date": "2023-08-24T22:15:08.077", "last_modified_date": "2024-11-21T08:02:39.973", "descriptions": [{"lang": "en", "value": "Netmaker makes networks with WireGuard. Prior to versions 0.17.1 and 0.18.6, hardcoded DNS key usage has been found in Netmaker allowing unauth users to interact with DNS API endpoints. The issue is patched in 0.17.1 and fixed in 0.18.6.  If users are using 0.17.1, they should run `docker pull gravitl/netmaker:v0.17.1` and `docker-compose up -d`. This will switch them to the patched users. If users are using v0.18.0-0.18.5, they should upgrade to v0.18.6 or later. As a workaround, someone who is using version 0.17.1 can pull the latest docker image of the backend and restart the server."}], "references": [{"url": "https://github.com/gravitl/netmaker/commit/1621c27c1d176b639e9768b2acad7693e387fd51", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/gravitl/netmaker/commit/9362c39a9a822f0e07361aa7c77af2610597e657", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/gravitl/netmaker/pull/2170", "source": "<EMAIL>", "tags": ["Patch"]}, {"url": "https://github.com/gravitl/netmaker/security/advisories/GHSA-8x8h-hcq8-jwwx", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}