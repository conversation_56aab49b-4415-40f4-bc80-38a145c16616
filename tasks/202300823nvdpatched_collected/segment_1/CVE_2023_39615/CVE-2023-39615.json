{"cve_id": "CVE-2023-39615", "published_date": "2023-08-29T17:15:12.527", "last_modified_date": "2024-11-21T08:15:42.583", "descriptions": [{"lang": "en", "value": "Xmlsoft Libxml2 v2.11.0 was discovered to contain an out-of-bounds read via the xmlSAX2StartElement() function at /libxml2/SAX2.c. This vulnerability allows attackers to cause a Denial of Service (DoS) via supplying a crafted XML file. NOTE: the vendor's position is that the product does not support the legacy SAX1 interface with custom callbacks; there is a crash even without crafted input."}], "references": [{"url": "https://gitlab.gnome.org/GNOME/libxml2/-/issues/535", "source": "<EMAIL>", "tags": ["Exploit", "Issue Tracking", "Patch"]}]}