{"cve_id": "CVE-2023-30435", "published_date": "2023-08-27T23:15:33.813", "last_modified_date": "2024-11-21T08:00:10.667", "descriptions": [{"lang": "en", "value": "IBM Security Guardium 11.3, 11.4, and 11.5 is vulnerable to stored cross-site scripting. This vulnerability allows users to embed arbitrary JavaScript code in the Web UI thus altering the intended functionality potentially leading to credentials disclosure within a trusted session.  IBM X-Force ID:  252291."}, {"lang": "es", "value": "IBM Security Guardium v11.3, v11.4 y v11.5 es vulnerable a Cross-Site Scripting (XSS) almacenado. Esta vulnerabilidad permite a los usuarios incrustar código JavaScript arbitrario en la interfaz de usuario web, lo que altera la funcionalidad prevista y puede conducir a la divulgación de credenciales en una sesión de confianza. ID de IBM X-Force: 252291."}], "references": [{"url": "https://exchange.xforce.ibmcloud.com/vulnerabilities/252291", "source": "<EMAIL>", "tags": ["VDB Entry", "Vendor Advisory"]}, {"url": "https://www.ibm.com/support/pages/node/7028506", "source": "<EMAIL>", "tags": ["Patch", "Vendor Advisory"]}]}