{"cve_id": "CVE-2023-32496", "cve_published_date": "2023-08-23T14:15:08.350", "cve_affected_cpes": [], "nodes": [{"node_id": "a04b2d87-392c-440e-b0bb-06565790132a", "url": "https://nvd.nist.gov/vuln/detail/CVE-2023-32496", "title": "CVE-2023-32496", "node_type": "ROOT", "source": "ROOT", "depth": 0, "category_in_type": "", "importance_score": 1.0}, {"node_id": "fb5a6d53-ddca-427a-86e5-3ce0a5ccb478", "url": "https://nvd.nist.gov", "title": "NVD", "node_type": "SOURCE", "source": "NVD", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "6b491d6f-22c8-4f2b-961c-f73c516e4b2c", "url": "https://github", "title": "GITHUB", "node_type": "SOURCE", "source": "GITHUB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "b7491363-4d1a-4113-955d-6e06be159f76", "url": "https://gitlab", "title": "GITLAB", "node_type": "SOURCE", "source": "GITLAB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "6d6f3b4f-6f7b-4455-93bd-c1dee8a6289a", "url": "https://debian", "title": "DEBIAN", "node_type": "SOURCE", "source": "DEBIAN", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "8be67156-8f3e-41b2-b1c2-36da0b7a5461", "url": "https://redhat", "title": "REDHAT", "node_type": "SOURCE", "source": "REDHAT", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "a1993031-5d07-4606-b16e-2156e105a66c", "url": "https://apache", "title": "APACHE", "node_type": "SOURCE", "source": "APACHE", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "0503d684-b082-470d-b2d4-c4dd7e435cbb", "url": "https://other.source", "title": "OTHER", "node_type": "SOURCE", "source": "OTHER", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "dbc1fe90-7b47-4c20-94b9-ccffdadb91f0", "url": "https://patchstack.com/database/vulnerability/stopbadbots/wordpress-block-bad-bots-and-stop-bad-bots-crawlers-and-spiders-and-anti-spam-protection-plugin-7-31-cross-site-scripting-xss-vulnerability", "title": "Wordpress block bad bots and stop bad bots crawlers and spiders and anti spam protection plugin 7 31 cross site scripting xss vulnerability", "node_type": "COMMON", "source": "OTHER", "depth": 2, "category_in_type": "common_url", "importance_score": 1.0}, {"node_id": "340d0c84-5967-4895-bf31-aa63c7fee429", "url": "https://patchstack.com/wordpress-security", "title": "", "node_type": "COMMON", "source": "OTHER", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}, {"node_id": "def401be-898f-4472-bbd5-8c430f9e9dd8", "url": "https://patchstack.com/for-hosts", "title": "", "node_type": "COMMON", "source": "OTHER", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}], "edges": [{"edge_id": "3f38a91a-7b74-4a55-84bb-8f53f6105c0c", "source_id": "a04b2d87-392c-440e-b0bb-06565790132a", "target_id": "fb5a6d53-ddca-427a-86e5-3ce0a5ccb478", "weight": 0.8, "description": "来源"}, {"edge_id": "7ecf4fb8-85ed-4bb8-8a6e-51f59ddd7479", "source_id": "a04b2d87-392c-440e-b0bb-06565790132a", "target_id": "6b491d6f-22c8-4f2b-961c-f73c516e4b2c", "weight": 0.8, "description": "来源"}, {"edge_id": "d9adca7c-1d23-44d7-8844-8231914d9cbf", "source_id": "a04b2d87-392c-440e-b0bb-06565790132a", "target_id": "b7491363-4d1a-4113-955d-6e06be159f76", "weight": 0.8, "description": "来源"}, {"edge_id": "2ace13af-5a97-464c-94f1-f4296820599f", "source_id": "a04b2d87-392c-440e-b0bb-06565790132a", "target_id": "6d6f3b4f-6f7b-4455-93bd-c1dee8a6289a", "weight": 0.8, "description": "来源"}, {"edge_id": "b68cf396-96bf-465e-bf7c-edaeb12be42f", "source_id": "a04b2d87-392c-440e-b0bb-06565790132a", "target_id": "8be67156-8f3e-41b2-b1c2-36da0b7a5461", "weight": 0.8, "description": "来源"}, {"edge_id": "077a1e16-3f76-4949-8e93-fd932af8ddaf", "source_id": "a04b2d87-392c-440e-b0bb-06565790132a", "target_id": "a1993031-5d07-4606-b16e-2156e105a66c", "weight": 0.8, "description": "来源"}, {"edge_id": "e682954d-d66c-402c-b00d-fd796aa2a79a", "source_id": "a04b2d87-392c-440e-b0bb-06565790132a", "target_id": "0503d684-b082-470d-b2d4-c4dd7e435cbb", "weight": 0.5, "description": "其他来源"}, {"edge_id": "5714c974-e2fd-43cf-b748-0f1efb66bd54", "source_id": "fb5a6d53-ddca-427a-86e5-3ce0a5ccb478", "target_id": "a04b2d87-392c-440e-b0bb-06565790132a", "weight": 1.0, "description": "引用"}, {"edge_id": "8b89c9ad-74ab-4857-911d-2a2d9329dc73", "source_id": "0503d684-b082-470d-b2d4-c4dd7e435cbb", "target_id": "dbc1fe90-7b47-4c20-94b9-ccffdadb91f0", "weight": 1.0, "description": "引用"}, {"edge_id": "e39ba309-28ec-4871-acc2-e16eabf762ff", "source_id": "fb5a6d53-ddca-427a-86e5-3ce0a5ccb478", "target_id": "dbc1fe90-7b47-4c20-94b9-ccffdadb91f0", "weight": 0.7, "description": "NVD引用"}, {"edge_id": "4cb0ff8d-5921-4755-b774-fe81329134a0", "source_id": "dbc1fe90-7b47-4c20-94b9-ccffdadb91f0", "target_id": "340d0c84-5967-4895-bf31-aa63c7fee429", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "ddc4709c-5773-43b2-a5c2-0a1f79b626fe", "source_id": "dbc1fe90-7b47-4c20-94b9-ccffdadb91f0", "target_id": "def401be-898f-4472-bbd5-8c430f9e9dd8", "weight": 0.3333333333333333, "description": "引用"}]}