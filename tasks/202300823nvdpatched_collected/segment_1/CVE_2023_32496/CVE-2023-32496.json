{"cve_id": "CVE-2023-32496", "published_date": "2023-08-23T14:15:08.350", "last_modified_date": "2024-11-21T08:03:28.680", "descriptions": [{"lang": "en", "value": "Auth. (admin+) Stored Cross-Site Scripting (XSS) vulnerability in Bill Minozzi Block Bad Bots and Stop Bad Bots Crawlers and Spiders and Anti Spam Protection plugin <= 7.31 versions."}], "references": [{"url": "https://patchstack.com/database/vulnerability/stopbadbots/wordpress-block-bad-bots-and-stop-bad-bots-crawlers-and-spiders-and-anti-spam-protection-plugin-7-31-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}