{"cve_id": "CVE-2023-32559", "published_date": "2023-08-24T02:15:09.210", "last_modified_date": "2025-07-01T14:15:28.967", "descriptions": [{"lang": "en", "value": "A privilege escalation vulnerability exists in the experimental policy mechanism in all active release lines: 16.x, 18.x and, 20.x. The use of the deprecated API `process.binding()` can bypass the policy mechanism by requiring internal modules and eventually take advantage of `process.binding('spawn_sync')` run arbitrary code, outside of the limits defined in a `policy.json` file. Please note that at the time this CVE was issued, the policy is an experimental feature of Node.js."}, {"lang": "es", "value": "Existe una vulnerabilidad de escalada de privilegios en el mecanismo de directiva experimental en todas las líneas de versión activas: 16.x, 18.x y 20.x. El uso de la API obsoleta 'process.binding()' puede omitir el mecanismo de la política al requerir módulos internos y, finalmente, aprovechar 'process.binding('spawn_sync')' ejecutar código arbitrario, fuera de los límites definidos en un archivo 'policy.json'. Tenga en cuenta que en el momento en que se emitió este CVE, la política es una funcionalidad experimental de Node.js.\n"}], "references": [{"url": "https://hackerone.com/reports/1946470", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://security.netapp.com/advisory/ntap-20231006-0006/", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}