{"cve_id": "CVE-2023-20230", "published_date": "2023-08-23T19:15:08.133", "last_modified_date": "2024-11-21T07:40:56.890", "descriptions": [{"lang": "en", "value": "A vulnerability in the restricted security domain implementation of Cisco Application Policy Infrastructure Controller (APIC) could allow an authenticated, remote attacker to read, modify, or delete non-tenant policies (for example, access policies) created by users associated with a different security domain on an affected system.\r\n\r This vulnerability is due to improper access control when restricted security domains are used to implement multi-tenancy for policies outside the tenant boundaries. An attacker with a valid user account associated with a restricted security domain could exploit this vulnerability. A successful exploit could allow the attacker to read, modify, or delete policies created by users associated with a different security domain. Exploitation is not possible for policies under tenants that an attacker has no authorization to access."}], "references": [{"url": "https://sec.cloudapps.cisco.com/security/center/content/CiscoSecurityAdvisory/cisco-sa-apic-uapa-F4TAShk", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}