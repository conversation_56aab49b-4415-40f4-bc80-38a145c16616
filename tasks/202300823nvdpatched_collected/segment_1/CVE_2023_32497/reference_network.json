{"cve_id": "CVE-2023-32497", "cve_published_date": "2023-08-23T14:15:08.693", "cve_affected_cpes": [], "nodes": [{"node_id": "d967411a-5718-44ff-884c-88e17988e725", "url": "https://nvd.nist.gov/vuln/detail/CVE-2023-32497", "title": "CVE-2023-32497", "node_type": "ROOT", "source": "ROOT", "depth": 0, "category_in_type": "", "importance_score": 1.0}, {"node_id": "5ab6a15c-6f6f-472f-b59d-7bca8ab3f413", "url": "https://nvd.nist.gov", "title": "NVD", "node_type": "SOURCE", "source": "NVD", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "a7c00ad6-8954-4927-863b-bf50a2dba978", "url": "https://github", "title": "GITHUB", "node_type": "SOURCE", "source": "GITHUB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "eddf5935-6947-4978-acab-8ea86d13bab1", "url": "https://gitlab", "title": "GITLAB", "node_type": "SOURCE", "source": "GITLAB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "d2a05546-c82a-4f88-a99d-93aafcfd33a4", "url": "https://debian", "title": "DEBIAN", "node_type": "SOURCE", "source": "DEBIAN", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "dc3e4f90-9c71-4cac-9aa5-f7bc34b0813f", "url": "https://redhat", "title": "REDHAT", "node_type": "SOURCE", "source": "REDHAT", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "738463c9-c486-4a8e-830d-04076ce61ffb", "url": "https://apache", "title": "APACHE", "node_type": "SOURCE", "source": "APACHE", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "02c44bda-8100-4b9e-bd39-4a1b69e80194", "url": "https://other.source", "title": "OTHER", "node_type": "SOURCE", "source": "OTHER", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "acc77cb1-c8bb-4953-8e38-d5321f5a95db", "url": "https://patchstack.com/database/vulnerability/block-referer-spam/wordpress-block-referer-spam-plugin-1-1-9-4-cross-site-scripting-xss-vulnerability", "title": "Wordpress block referer spam plugin 1 1 9 4 cross site scripting xss vulnerability", "node_type": "COMMON", "source": "OTHER", "depth": 2, "category_in_type": "common_url", "importance_score": 1.0}, {"node_id": "7f2706dd-bd83-403d-855e-36bcc1c0c708", "url": "https://patchstack.com/wordpress-security", "title": "", "node_type": "COMMON", "source": "OTHER", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}, {"node_id": "ae652d6d-d36b-45df-8bda-d5c6aff53ab6", "url": "https://patchstack.com/for-hosts", "title": "", "node_type": "COMMON", "source": "OTHER", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}], "edges": [{"edge_id": "3dce5ec5-35cf-4006-b8c1-29908db72ebe", "source_id": "d967411a-5718-44ff-884c-88e17988e725", "target_id": "5ab6a15c-6f6f-472f-b59d-7bca8ab3f413", "weight": 0.8, "description": "来源"}, {"edge_id": "9105399d-a46a-4948-b3e9-1e2e0c7461bd", "source_id": "d967411a-5718-44ff-884c-88e17988e725", "target_id": "a7c00ad6-8954-4927-863b-bf50a2dba978", "weight": 0.8, "description": "来源"}, {"edge_id": "9fad1564-8d15-4f22-95dd-25fb61d7749c", "source_id": "d967411a-5718-44ff-884c-88e17988e725", "target_id": "eddf5935-6947-4978-acab-8ea86d13bab1", "weight": 0.8, "description": "来源"}, {"edge_id": "a3cc5f7e-1ef6-45d4-b939-9c2e685166ca", "source_id": "d967411a-5718-44ff-884c-88e17988e725", "target_id": "d2a05546-c82a-4f88-a99d-93aafcfd33a4", "weight": 0.8, "description": "来源"}, {"edge_id": "777b5d07-b507-4d36-a37a-91bfa1d8cb8b", "source_id": "d967411a-5718-44ff-884c-88e17988e725", "target_id": "dc3e4f90-9c71-4cac-9aa5-f7bc34b0813f", "weight": 0.8, "description": "来源"}, {"edge_id": "58520a70-d91f-4bc1-9ddf-44c43557bf8d", "source_id": "d967411a-5718-44ff-884c-88e17988e725", "target_id": "738463c9-c486-4a8e-830d-04076ce61ffb", "weight": 0.8, "description": "来源"}, {"edge_id": "5d36914b-668a-448c-ab4c-0ac57f064256", "source_id": "d967411a-5718-44ff-884c-88e17988e725", "target_id": "02c44bda-8100-4b9e-bd39-4a1b69e80194", "weight": 0.5, "description": "其他来源"}, {"edge_id": "a1b03aef-cba8-432c-9bd9-624de024f836", "source_id": "5ab6a15c-6f6f-472f-b59d-7bca8ab3f413", "target_id": "d967411a-5718-44ff-884c-88e17988e725", "weight": 1.0, "description": "引用"}, {"edge_id": "f519030e-2152-4a5f-afcc-5cbf926f4551", "source_id": "02c44bda-8100-4b9e-bd39-4a1b69e80194", "target_id": "acc77cb1-c8bb-4953-8e38-d5321f5a95db", "weight": 1.0, "description": "引用"}, {"edge_id": "88b02e72-331c-4bf7-9eb1-d346ea3f372f", "source_id": "5ab6a15c-6f6f-472f-b59d-7bca8ab3f413", "target_id": "acc77cb1-c8bb-4953-8e38-d5321f5a95db", "weight": 0.7, "description": "NVD引用"}, {"edge_id": "2b9bf380-e484-40a3-863c-f2418ab798a1", "source_id": "acc77cb1-c8bb-4953-8e38-d5321f5a95db", "target_id": "7f2706dd-bd83-403d-855e-36bcc1c0c708", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "8bab55d1-e3d5-46fa-b81a-b3a95dd18ad3", "source_id": "acc77cb1-c8bb-4953-8e38-d5321f5a95db", "target_id": "ae652d6d-d36b-45df-8bda-d5c6aff53ab6", "weight": 0.3333333333333333, "description": "引用"}]}