{"cve_id": "CVE-2023-32497", "published_date": "2023-08-23T14:15:08.693", "last_modified_date": "2024-11-21T08:03:28.813", "descriptions": [{"lang": "en", "value": "Auth. (admin+) Stored Cross-Site Scripting (XSS) vulnerability in Supersoju Block Referer Spam plugin <= ******* versions."}], "references": [{"url": "https://patchstack.com/database/vulnerability/block-referer-spam/wordpress-block-referer-spam-plugin-1-1-9-4-cross-site-scripting-xss-vulnerability?_s_id=cve", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}