{"cve_id": "CVE-2016-15035", "published_date": "2023-08-28T04:15:08.573", "last_modified_date": "2024-11-21T02:45:32.793", "descriptions": [{"lang": "en", "value": "A vulnerability was found in Doc2k RE-Chat 1.0. It has been classified as problematic. This affects an unknown part of the file js_on_radio-emergency.de_/re_chat.js. The manipulation leads to cross site scripting. It is possible to initiate the attack remotely. The patch is named bd17d497ddd3bab4ef9c6831c747c37cc016c570. It is recommended to apply a patch to fix this issue. The associated identifier of this vulnerability is VDB-238155."}, {"lang": "es", "value": "Se ha encontrado una vulnerabilidad en Doc2k RE-Chat v1.0. Se ha clasificado como problemática. Afecta a una parte desconocida del archivo \"js_on_radio-emergency.de_/re_chat.js\". La manipulación conduce a Cross-Site Scripting (XSS). Es posible iniciar el ataque de forma remota. El parche se llama bd17d497ddd3bab4ef9c6831c747c37cc016c570. Se recomienda aplicar el parche para solucionar este problema. El identificador asociado a esta vulnerabilidad es VDB-238155."}], "references": [{"url": "https://github.com/Doc2k/re-chat/commit/bd17d497ddd3bab4ef9c6831c747c37cc016c570", "source": "<EMAIL>", "tags": ["Patch", "Vendor Advisory"]}, {"url": "https://vuldb.com/?ctiid.238155", "source": "<EMAIL>", "tags": ["Permissions Required", "Third Party Advisory"]}, {"url": "https://vuldb.com/?id.238155", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}