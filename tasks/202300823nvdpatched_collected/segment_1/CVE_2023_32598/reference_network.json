{"cve_id": "CVE-2023-32598", "cve_published_date": "2023-08-25T12:15:07.880", "cve_affected_cpes": [], "nodes": [{"node_id": "155fd763-f87c-4566-8558-3f227c6fdf4c", "url": "https://nvd.nist.gov/vuln/detail/CVE-2023-32598", "title": "CVE-2023-32598", "node_type": "ROOT", "source": "ROOT", "depth": 0, "category_in_type": "", "importance_score": 1.0}, {"node_id": "706bdd57-f019-4fc3-957b-16a7ce4cce5b", "url": "https://nvd.nist.gov", "title": "NVD", "node_type": "SOURCE", "source": "NVD", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "1d83a607-e24a-4b9e-b16d-b257bed7be9d", "url": "https://github", "title": "GITHUB", "node_type": "SOURCE", "source": "GITHUB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "4e35a896-77c0-4447-b5e8-83d35039a07e", "url": "https://gitlab", "title": "GITLAB", "node_type": "SOURCE", "source": "GITLAB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "d07a704e-6384-45ba-b264-3b422852a4e1", "url": "https://debian", "title": "DEBIAN", "node_type": "SOURCE", "source": "DEBIAN", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "3dea4433-991e-4276-b1fe-c9304f58a3db", "url": "https://redhat", "title": "REDHAT", "node_type": "SOURCE", "source": "REDHAT", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "17e61055-5836-4acd-9417-3e87933bb70f", "url": "https://apache", "title": "APACHE", "node_type": "SOURCE", "source": "APACHE", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "eacd7711-49ca-4eb4-81f0-8fc1aa1973ff", "url": "https://other.source", "title": "OTHER", "node_type": "SOURCE", "source": "OTHER", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "0ce1dcec-6c2d-42d4-b44b-0fb7339b4454", "url": "https://patchstack.com/database/vulnerability/featured-image-pro/wordpress-featured-image-pro-post-grid-plugin-5-14-reflected-cross-site-scripting-xss-vulnerability", "title": "Wordpress featured image pro post grid plugin 5 14 reflected cross site scripting xss vulnerability", "node_type": "COMMON", "source": "OTHER", "depth": 2, "category_in_type": "common_url", "importance_score": 1.0}, {"node_id": "8223babf-2235-4e74-af39-152197ed2950", "url": "https://patchstack.com/wordpress-security", "title": "", "node_type": "COMMON", "source": "OTHER", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}, {"node_id": "250cc447-2658-42ff-8a0f-91e7bedafc82", "url": "https://patchstack.com/for-hosts", "title": "", "node_type": "COMMON", "source": "OTHER", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}], "edges": [{"edge_id": "8b62fae1-570c-4a4a-8fb5-f8d015111fab", "source_id": "155fd763-f87c-4566-8558-3f227c6fdf4c", "target_id": "706bdd57-f019-4fc3-957b-16a7ce4cce5b", "weight": 0.8, "description": "来源"}, {"edge_id": "a7853014-c5c7-4114-a305-2220e834300b", "source_id": "155fd763-f87c-4566-8558-3f227c6fdf4c", "target_id": "1d83a607-e24a-4b9e-b16d-b257bed7be9d", "weight": 0.8, "description": "来源"}, {"edge_id": "bd44d666-2376-4733-bebc-ede8aad70ef9", "source_id": "155fd763-f87c-4566-8558-3f227c6fdf4c", "target_id": "4e35a896-77c0-4447-b5e8-83d35039a07e", "weight": 0.8, "description": "来源"}, {"edge_id": "082d392a-4307-4a7a-828f-9bf3177495d5", "source_id": "155fd763-f87c-4566-8558-3f227c6fdf4c", "target_id": "d07a704e-6384-45ba-b264-3b422852a4e1", "weight": 0.8, "description": "来源"}, {"edge_id": "7e16e05b-cb8d-42fe-86b5-60813cc62bbf", "source_id": "155fd763-f87c-4566-8558-3f227c6fdf4c", "target_id": "3dea4433-991e-4276-b1fe-c9304f58a3db", "weight": 0.8, "description": "来源"}, {"edge_id": "9c08aca7-8ad8-41be-95e6-dce28f525370", "source_id": "155fd763-f87c-4566-8558-3f227c6fdf4c", "target_id": "17e61055-5836-4acd-9417-3e87933bb70f", "weight": 0.8, "description": "来源"}, {"edge_id": "6a0bd42b-dc1a-4aea-9c33-08c3466ba5d9", "source_id": "155fd763-f87c-4566-8558-3f227c6fdf4c", "target_id": "eacd7711-49ca-4eb4-81f0-8fc1aa1973ff", "weight": 0.5, "description": "其他来源"}, {"edge_id": "8260d1ea-c28b-4936-ba82-3a0876373cc2", "source_id": "706bdd57-f019-4fc3-957b-16a7ce4cce5b", "target_id": "155fd763-f87c-4566-8558-3f227c6fdf4c", "weight": 1.0, "description": "引用"}, {"edge_id": "652de7d4-ade1-4ab0-a365-1746da4c5878", "source_id": "eacd7711-49ca-4eb4-81f0-8fc1aa1973ff", "target_id": "0ce1dcec-6c2d-42d4-b44b-0fb7339b4454", "weight": 1.0, "description": "引用"}, {"edge_id": "b1204a04-bd8e-4a34-91c4-fa802bc4ef85", "source_id": "706bdd57-f019-4fc3-957b-16a7ce4cce5b", "target_id": "0ce1dcec-6c2d-42d4-b44b-0fb7339b4454", "weight": 0.7, "description": "NVD引用"}, {"edge_id": "ecd3731f-6a1a-46bf-9404-c9dbbede4579", "source_id": "0ce1dcec-6c2d-42d4-b44b-0fb7339b4454", "target_id": "8223babf-2235-4e74-af39-152197ed2950", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "41d6df7b-910a-46c8-9c2d-cc9b1cd7dea6", "source_id": "0ce1dcec-6c2d-42d4-b44b-0fb7339b4454", "target_id": "250cc447-2658-42ff-8a0f-91e7bedafc82", "weight": 0.3333333333333333, "description": "引用"}]}