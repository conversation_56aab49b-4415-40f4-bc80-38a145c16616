{"cve_id": "CVE-2023-39699", "published_date": "2023-08-25T00:15:09.283", "last_modified_date": "2024-11-21T08:15:50.640", "descriptions": [{"lang": "en", "value": "IceWarp Mail Server v10.4.5 was discovered to contain a local file inclusion (LFI) vulnerability via the component /calendar/minimizer/index.php. This vulnerability allows attackers to include or execute files from the local file system of the targeted server."}], "references": [{"url": "https://cwe.mitre.org/data/definitions/98.html", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}, {"url": "https://drive.google.com/file/d/1NkqL4ySJApyPy8B-zDC7vE-QMBQAu8OU", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://owasp.org/www-project-web-security-testing-guide/v42/4-Web_Application_Security_Testing/07-Input_Validation_Testing/11.1-Testing_for_Local_File_Inclusion", "source": "<EMAIL>", "tags": ["Third Party Advisory"]}]}