{"cve_id": "CVE-2023-23772", "published_date": "2023-08-29T09:15:09.193", "last_modified_date": "2024-11-21T07:46:47.970", "descriptions": [{"lang": "en", "value": "Motorola MBTS Site Controller fails to check firmware update authenticity. The Motorola MBTS Site Controller lacks cryptographic signature validation for firmware update packages, allowing an authenticated attacker to gain arbitrary code execution, extract secret key material, and/or leave a persistent implant on the device."}, {"lang": "es", "value": "Motorola MBTS Site Controller no verifica la autenticidad de la actualización del firmware. El controlador de sitio MBTS de Motorola carece de validación de firma criptográfica para los paquetes de actualización de firmware, lo que permite a un atacante autenticado obtener la ejecución de código arbitrario, extraer material de clave secreta y/o dejar un implante persistente en el dispositivo."}], "references": [{"url": "https://tetraburst.com/", "source": "<EMAIL>", "tags": ["Not Applicable"]}]}