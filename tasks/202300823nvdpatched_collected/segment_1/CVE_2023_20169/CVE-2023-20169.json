{"cve_id": "CVE-2023-20169", "published_date": "2023-08-23T19:15:07.893", "last_modified_date": "2024-11-21T07:40:44.107", "descriptions": [{"lang": "en", "value": "A vulnerability in the Intermediate System-to-Intermediate System (IS-IS) protocol of Cisco NX-OS Software for the Cisco Nexus 3000 Series Switches and Cisco Nexus 9000 Series Switches in standalone NX-OS mode could allow an unauthenticated, adjacent attacker to cause the IS-IS process to unexpectedly restart, which could cause an affected device to reload.\r\n\r This vulnerability is due to insufficient input validation when parsing an ingress IS-IS packet. An attacker could exploit this vulnerability by sending a crafted IS-IS packet to an affected device. A successful exploit could allow the attacker to cause a denial of service (DoS) condition due to the unexpected restart of the IS-IS process, which could cause the affected device to reload. Note: The IS-IS protocol is a routing protocol. To exploit this vulnerability, an attacker must be Layer 2 adjacent to the affected device."}], "references": [{"url": "https://sec.cloudapps.cisco.com/security/center/content/CiscoSecurityAdvisory/cisco-sa-nxos-n3_9k-isis-dos-FTCXB4Vb", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}