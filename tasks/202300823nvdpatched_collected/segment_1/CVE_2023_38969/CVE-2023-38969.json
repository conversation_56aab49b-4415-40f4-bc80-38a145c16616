{"cve_id": "CVE-2023-38969", "published_date": "2023-08-28T21:15:07.667", "last_modified_date": "2024-11-21T08:14:32.810", "descriptions": [{"lang": "en", "value": "Cross Site Scripting vulnerabiltiy in Badaso v.2.9.7 allows a remote attacker to execute arbitrary code via a crafted payload to the title parameter in the new book and edit book function."}, {"lang": "es", "value": "Una vulnerabilidad de Cross-Site Scripting (XSS) en Badaso v2.9.7 permite a un atacante remoto ejecutar código arbitrario a través de un payload manipulado en el parámetro \"title\" de la funciones \"new book\" y \"edit book\"."}], "references": [{"url": "https://github.com/anh91/uasoft-indonesia--badaso/blob/main/XSS2.md", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}, {"url": "https://panda002.hashnode.dev/badaso-version-297-has-an-xss-vulnerability-in-add-books", "source": "<EMAIL>", "tags": ["Exploit", "Third Party Advisory"]}]}