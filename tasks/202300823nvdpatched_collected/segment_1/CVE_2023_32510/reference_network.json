{"cve_id": "CVE-2023-32510", "cve_published_date": "2023-08-24T12:15:07.740", "cve_affected_cpes": [], "nodes": [{"node_id": "48b5ed06-2473-4f6f-87b7-16c07a08ec4d", "url": "https://nvd.nist.gov/vuln/detail/CVE-2023-32510", "title": "CVE-2023-32510", "node_type": "ROOT", "source": "ROOT", "depth": 0, "category_in_type": "", "importance_score": 1.0}, {"node_id": "53471bfa-ee06-4981-82f1-79ebe8312623", "url": "https://nvd.nist.gov", "title": "NVD", "node_type": "SOURCE", "source": "NVD", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "efa52049-e81c-4e76-a961-65bdbd261a55", "url": "https://github", "title": "GITHUB", "node_type": "SOURCE", "source": "GITHUB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "f9adc7b8-80c0-4c37-878c-be3cec930998", "url": "https://gitlab", "title": "GITLAB", "node_type": "SOURCE", "source": "GITLAB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "6991cce1-c491-417e-a31a-a121457f3de2", "url": "https://debian", "title": "DEBIAN", "node_type": "SOURCE", "source": "DEBIAN", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "073076cf-cc19-46af-80de-c32aaafd32dd", "url": "https://redhat", "title": "REDHAT", "node_type": "SOURCE", "source": "REDHAT", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "560fbd28-9ee0-417e-9cf9-c7bfd244ae63", "url": "https://apache", "title": "APACHE", "node_type": "SOURCE", "source": "APACHE", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "4f36f71b-d8c8-4722-a0f0-2df7cd693f75", "url": "https://other.source", "title": "OTHER", "node_type": "SOURCE", "source": "OTHER", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "5e2aaa61-0cd6-4478-9c7c-9058bf8496bc", "url": "https://patchstack.com/database/vulnerability/order-your-posts-manually/wordpress-order-your-posts-manually-plugin-2-2-5-reflected-cross-site-scripting-xss-vulnerability-2", "title": "Wordpress order your posts manually plugin 2 2 5 reflected cross site scripting xss vulnerability 2", "node_type": "COMMON", "source": "OTHER", "depth": 2, "category_in_type": "common_url", "importance_score": 1.0}, {"node_id": "3efe5c29-50b7-498c-9b65-59fc76b35f5a", "url": "https://patchstack.com/wordpress-security", "title": "", "node_type": "COMMON", "source": "OTHER", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}, {"node_id": "e2056939-6322-4e8c-b328-6fec1aeedbe4", "url": "https://patchstack.com/for-hosts", "title": "", "node_type": "COMMON", "source": "OTHER", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}], "edges": [{"edge_id": "a3d770ae-1815-4a5e-8481-0418f0ced579", "source_id": "48b5ed06-2473-4f6f-87b7-16c07a08ec4d", "target_id": "53471bfa-ee06-4981-82f1-79ebe8312623", "weight": 0.8, "description": "来源"}, {"edge_id": "f697a4a2-c9fc-4f39-8416-86ad97436c1f", "source_id": "48b5ed06-2473-4f6f-87b7-16c07a08ec4d", "target_id": "efa52049-e81c-4e76-a961-65bdbd261a55", "weight": 0.8, "description": "来源"}, {"edge_id": "7ddba4b1-3c3f-4b9f-8320-8953811f301f", "source_id": "48b5ed06-2473-4f6f-87b7-16c07a08ec4d", "target_id": "f9adc7b8-80c0-4c37-878c-be3cec930998", "weight": 0.8, "description": "来源"}, {"edge_id": "b92a7ec7-dbbd-411a-bcab-97914669617b", "source_id": "48b5ed06-2473-4f6f-87b7-16c07a08ec4d", "target_id": "6991cce1-c491-417e-a31a-a121457f3de2", "weight": 0.8, "description": "来源"}, {"edge_id": "4ee6f08b-cd6f-4acd-b97a-6a63b83ce9dd", "source_id": "48b5ed06-2473-4f6f-87b7-16c07a08ec4d", "target_id": "073076cf-cc19-46af-80de-c32aaafd32dd", "weight": 0.8, "description": "来源"}, {"edge_id": "caddc540-b0ca-4778-9e2e-a99f9a77e2b0", "source_id": "48b5ed06-2473-4f6f-87b7-16c07a08ec4d", "target_id": "560fbd28-9ee0-417e-9cf9-c7bfd244ae63", "weight": 0.8, "description": "来源"}, {"edge_id": "ae1675ce-a142-49f9-b7cd-be1788724c46", "source_id": "48b5ed06-2473-4f6f-87b7-16c07a08ec4d", "target_id": "4f36f71b-d8c8-4722-a0f0-2df7cd693f75", "weight": 0.5, "description": "其他来源"}, {"edge_id": "e7c3ce06-fad5-4b23-ac04-7c68a0e56754", "source_id": "53471bfa-ee06-4981-82f1-79ebe8312623", "target_id": "48b5ed06-2473-4f6f-87b7-16c07a08ec4d", "weight": 1.0, "description": "引用"}, {"edge_id": "753dc954-6ce2-4af7-a8d9-619d0ebff2db", "source_id": "4f36f71b-d8c8-4722-a0f0-2df7cd693f75", "target_id": "5e2aaa61-0cd6-4478-9c7c-9058bf8496bc", "weight": 1.0, "description": "引用"}, {"edge_id": "d84f80ee-e898-493c-aa09-a820df754017", "source_id": "53471bfa-ee06-4981-82f1-79ebe8312623", "target_id": "5e2aaa61-0cd6-4478-9c7c-9058bf8496bc", "weight": 0.7, "description": "NVD引用"}, {"edge_id": "81d541be-4690-4bf0-84ac-e391c77e3036", "source_id": "5e2aaa61-0cd6-4478-9c7c-9058bf8496bc", "target_id": "3efe5c29-50b7-498c-9b65-59fc76b35f5a", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "335fab4b-0f89-4dd7-9144-c20b3272707c", "source_id": "5e2aaa61-0cd6-4478-9c7c-9058bf8496bc", "target_id": "e2056939-6322-4e8c-b328-6fec1aeedbe4", "weight": 0.3333333333333333, "description": "引用"}]}