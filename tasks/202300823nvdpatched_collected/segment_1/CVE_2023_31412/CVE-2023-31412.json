{"cve_id": "CVE-2023-31412", "published_date": "2023-08-24T19:15:33.977", "last_modified_date": "2024-11-21T08:01:48.617", "descriptions": [{"lang": "en", "value": "The LMS5xx uses weak hash generation methods, resulting in the creation of insecure hashs. If an attacker manages to retrieve the hash, it could lead to collision attacks and the potential retrieval of the password."}], "references": [{"url": "https://sick.com/.well-known/csaf/white/2023/sca-2023-0007.json", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://sick.com/.well-known/csaf/white/2023/sca-2023-0007.pdf", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}, {"url": "https://sick.com/psirt", "source": "<EMAIL>", "tags": ["Vendor Advisory"]}]}