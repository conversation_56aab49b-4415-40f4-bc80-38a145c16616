{"cve_id": "CVE-2023-28994", "cve_published_date": "2023-08-23T15:15:07.890", "cve_affected_cpes": [], "nodes": [{"node_id": "ce71d832-19bc-46ff-b347-21392a22f1c5", "url": "https://nvd.nist.gov/vuln/detail/CVE-2023-28994", "title": "CVE-2023-28994", "node_type": "ROOT", "source": "ROOT", "depth": 0, "category_in_type": "", "importance_score": 1.0}, {"node_id": "7c99ce46-fe3f-4fca-8dca-53cf820d227e", "url": "https://nvd.nist.gov", "title": "NVD", "node_type": "SOURCE", "source": "NVD", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "8c074d50-f634-41b4-bd9c-1dddd0acde51", "url": "https://github", "title": "GITHUB", "node_type": "SOURCE", "source": "GITHUB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "73e1d6d9-6327-4cf0-bad8-d72e44e0c200", "url": "https://gitlab", "title": "GITLAB", "node_type": "SOURCE", "source": "GITLAB", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "213c5ec2-7938-4cfb-9f24-db0529313b2a", "url": "https://debian", "title": "DEBIAN", "node_type": "SOURCE", "source": "DEBIAN", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "e63226ee-42e1-4588-bca7-8e556c40cf41", "url": "https://redhat", "title": "REDHAT", "node_type": "SOURCE", "source": "REDHAT", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "7e933dea-9001-460d-bf88-c036a4200c60", "url": "https://apache", "title": "APACHE", "node_type": "SOURCE", "source": "APACHE", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "3a03b613-d20f-4c2d-80f4-1b0c1082bd75", "url": "https://other.source", "title": "OTHER", "node_type": "SOURCE", "source": "OTHER", "depth": 1, "category_in_type": "", "importance_score": 1.0}, {"node_id": "7f7d0543-e95d-4b09-b25e-a2969d4fde10", "url": "https://patchstack.com/database/vulnerability/flatsome/wordpress-flatsome-theme-3-16-8-reflected-cross-site-scripting-xss-vulnerability", "title": "Wordpress flatsome theme 3 16 8 reflected cross site scripting xss vulnerability", "node_type": "COMMON", "source": "OTHER", "depth": 2, "category_in_type": "common_url", "importance_score": 1.0}, {"node_id": "cdfa0707-18f0-4604-9920-2bbec5ddbe39", "url": "https://patchstack.com/wordpress-security", "title": "", "node_type": "COMMON", "source": "OTHER", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}, {"node_id": "724c804a-4452-4956-93c1-c88f532d5c9d", "url": "https://patchstack.com/for-hosts", "title": "", "node_type": "COMMON", "source": "OTHER", "depth": 3, "category_in_type": "common_url", "importance_score": 0.0}], "edges": [{"edge_id": "76e8fb6f-ebf6-4e55-b9d6-8f582e710c98", "source_id": "ce71d832-19bc-46ff-b347-21392a22f1c5", "target_id": "7c99ce46-fe3f-4fca-8dca-53cf820d227e", "weight": 0.8, "description": "来源"}, {"edge_id": "79f93a41-d228-472c-91fa-81f4021aafb2", "source_id": "ce71d832-19bc-46ff-b347-21392a22f1c5", "target_id": "8c074d50-f634-41b4-bd9c-1dddd0acde51", "weight": 0.8, "description": "来源"}, {"edge_id": "d2b34b47-fc92-488c-99c5-dd7a41a6b9e7", "source_id": "ce71d832-19bc-46ff-b347-21392a22f1c5", "target_id": "73e1d6d9-6327-4cf0-bad8-d72e44e0c200", "weight": 0.8, "description": "来源"}, {"edge_id": "3fe8f1cc-d7d0-47c0-8d40-7822cca6b861", "source_id": "ce71d832-19bc-46ff-b347-21392a22f1c5", "target_id": "213c5ec2-7938-4cfb-9f24-db0529313b2a", "weight": 0.8, "description": "来源"}, {"edge_id": "8f7fe5da-70ce-43c1-99fd-0cd5064fa24e", "source_id": "ce71d832-19bc-46ff-b347-21392a22f1c5", "target_id": "e63226ee-42e1-4588-bca7-8e556c40cf41", "weight": 0.8, "description": "来源"}, {"edge_id": "8336cd6c-6e67-4a99-a195-e571d2eea69e", "source_id": "ce71d832-19bc-46ff-b347-21392a22f1c5", "target_id": "7e933dea-9001-460d-bf88-c036a4200c60", "weight": 0.8, "description": "来源"}, {"edge_id": "0fdf988f-88dc-4818-b73a-c2dd98c8ddd0", "source_id": "ce71d832-19bc-46ff-b347-21392a22f1c5", "target_id": "3a03b613-d20f-4c2d-80f4-1b0c1082bd75", "weight": 0.5, "description": "其他来源"}, {"edge_id": "93d5e516-83a7-4900-8544-595dde096f0c", "source_id": "7c99ce46-fe3f-4fca-8dca-53cf820d227e", "target_id": "ce71d832-19bc-46ff-b347-21392a22f1c5", "weight": 1.0, "description": "引用"}, {"edge_id": "a03d995c-041c-45e3-96c0-14eb8e153d63", "source_id": "3a03b613-d20f-4c2d-80f4-1b0c1082bd75", "target_id": "7f7d0543-e95d-4b09-b25e-a2969d4fde10", "weight": 1.0, "description": "引用"}, {"edge_id": "67e08289-3249-4efb-a44a-651c0129fcd9", "source_id": "7c99ce46-fe3f-4fca-8dca-53cf820d227e", "target_id": "7f7d0543-e95d-4b09-b25e-a2969d4fde10", "weight": 0.7, "description": "NVD引用"}, {"edge_id": "21e51423-9bad-4c2c-a4a0-b611d914271f", "source_id": "7f7d0543-e95d-4b09-b25e-a2969d4fde10", "target_id": "cdfa0707-18f0-4604-9920-2bbec5ddbe39", "weight": 0.3333333333333333, "description": "引用"}, {"edge_id": "63c0ba83-06fb-41ff-be50-0dbd06ed6642", "source_id": "7f7d0543-e95d-4b09-b25e-a2969d4fde10", "target_id": "724c804a-4452-4956-93c1-c88f532d5c9d", "weight": 0.3333333333333333, "description": "引用"}]}