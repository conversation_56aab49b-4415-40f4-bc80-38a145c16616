#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
等价补丁搜索调试脚本

用于测试和调试补丁搜索逻辑中的关键问题
"""

import sys
import os
import json
import urllib.parse
import re
from typing import List, Dict

# 添加项目路径
project_root = "/home/<USER>"
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def test_url_parsing():
    """测试URL解析逻辑"""
    print("=== URL解析测试 ===")
    
    # 从CVE-2010-0296中提取的实际URL
    test_url = "http://sourceware.org/git/?p=glibc.git%3Ba=commit%3Bh=ab00f4eac8f4932211259ff87be83144f5211540"
    
    print(f"原始URL: {test_url}")
    
    # 测试URL解码
    decoded_url = urllib.parse.unquote(test_url)
    print(f"解码后URL: {decoded_url}")
    
    # 测试是否包含Git相关关键词
    git_keywords = ['git', 'commit', 'patch']
    found_keywords = [kw for kw in git_keywords if kw in decoded_url.lower()]
    print(f"发现的Git关键词: {found_keywords}")
    
    # 测试提取commit hash
    commit_pattern = r'[hH]=([a-f0-9]{40})'
    commit_match = re.search(commit_pattern, decoded_url)
    if commit_match:
        commit_hash = commit_match.group(1)
        print(f"提取的commit hash: {commit_hash}")
    else:
        print("未找到commit hash")
    
    return decoded_url

def test_cve_references():
    """测试CVE引用数据"""
    print("\n=== CVE引用测试 ===")
    
    cve_file = "/home/<USER>/data/patch/CVE_2010_0296/CVE-2010-0296.json"
    
    if not os.path.exists(cve_file):
        print(f"CVE文件不存在: {cve_file}")
        return
    
    with open(cve_file, 'r') as f:
        cve_data = json.load(f)
    
    print(f"CVE ID: {cve_data.get('cve_id', 'Unknown')}")
    print(f"引用总数: {len(cve_data.get('references', []))}")
    
    # 分析引用类型
    git_refs = []
    patch_refs = []
    advisory_refs = []
    
    for ref in cve_data.get('references', []):
        url = ref['url']
        decoded_url = urllib.parse.unquote(url)
        
        if any(kw in decoded_url.lower() for kw in ['git', 'commit']):
            git_refs.append(decoded_url)
        elif 'patch' in decoded_url.lower():
            patch_refs.append(decoded_url)
        elif any(kw in decoded_url.lower() for kw in ['advisory', 'security']):
            advisory_refs.append(decoded_url)
    
    print(f"Git相关引用数: {len(git_refs)}")
    print(f"补丁相关引用数: {len(patch_refs)}")
    print(f"安全公告引用数: {len(advisory_refs)}")
    
    if git_refs:
        print("\n发现的Git引用:")
        for i, ref in enumerate(git_refs, 1):
            print(f"  {i}. {ref}")
    
    return git_refs

def test_patch_tracer_config():
    """测试补丁跟踪器配置"""
    print("\n=== 补丁跟踪器配置测试 ===")
    
    # 检查配置文件
    config_files = [
        "/home/<USER>/src/data_collection/config/sources.json",
        "/home/<USER>/config/sources.json"
    ]
    
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"找到配置文件: {config_file}")
            try:
                with open(config_file, 'r') as f:
                    config = json.load(f)
                    print(f"配置内容: {json.dumps(config, indent=2, ensure_ascii=False)}")
            except Exception as e:
                print(f"配置文件读取错误: {e}")
        else:
            print(f"配置文件不存在: {config_file}")

def analyze_search_failure():
    """分析搜索失败的原因"""
    print("\n=== 搜索失败原因分析 ===")
    
    # 检查trace_result.json
    trace_file = "/home/<USER>/data/patch/CVE_2010_0296/trace_result.json"
    
    if os.path.exists(trace_file):
        with open(trace_file, 'r') as f:
            trace_result = json.load(f)
        
        print("跟踪结果统计:")
        stats = trace_result.get('network_stats', {})
        for key, value in stats.items():
            print(f"  {key}: {value}")
        
        print(f"发现的所有补丁数: {len(trace_result.get('all_patches', []))}")
        print(f"高置信度补丁数: {len(trace_result.get('high_confidence_patches', []))}")
        print(f"补丁摘要: {trace_result.get('patch_summary', 'N/A')}")
        
        # 如果网络统计显示有节点但没有补丁节点，说明识别有问题
        if stats.get('total_nodes', 0) > 0 and stats.get('patch_nodes', 0) == 0:
            print("\n⚠️ 问题诊断: 网络中有节点但没有补丁节点，可能是补丁识别逻辑有问题")
            
        if stats.get('total_nodes', 0) == 0:
            print("\n⚠️ 问题诊断: 网络中没有节点，可能是URL抓取或解析失败")
    else:
        print(f"跟踪结果文件不存在: {trace_file}")

def test_node_type_identification():
    """测试节点类型识别"""
    print("\n=== 节点类型识别测试 ===")

    # 导入必要的模块
    from src.data_collection.services.patch_tracer.reference_node import ReferenceNode
    from src.data_collection.services.patch_tracer.utils import normalize_url

    # 测试URL
    test_url = "http://sourceware.org/git/?p=glibc.git%3Ba=commit%3Bh=ab00f4eac8f4932211259ff87be83144f5211540"

    print(f"原始URL: {test_url}")

    # 测试URL规范化
    normalized_url = normalize_url(test_url)
    print(f"规范化后URL: {normalized_url}")

    # 测试节点类型识别
    node_type, category = ReferenceNode.identify_node_type(normalized_url)
    print(f"识别的节点类型: {node_type}")
    print(f"识别的类别: {category}")

    # 检查是否为补丁类型
    from src.data_collection.services.patch_tracer.reference_node import NodeType
    if node_type == NodeType.PATCH:
        print("✅ 正确识别为补丁节点")
    else:
        print("❌ 未能识别为补丁节点")

    return node_type, category

def test_priority_ranking():
    """测试优先级排序"""
    print("\n=== 优先级排序测试 ===")

    import re

    # 测试URL
    test_url = "http://sourceware.org/git/?p=glibc.git;a=commit;h=ab00f4eac8f4932211259ff87be83144f5211540"

    print(f"测试URL: {test_url}")

    # 测试补丁模式匹配
    patch_patterns = [
        r'github\.com/[^/]+/[^/]+/commit/[0-9a-f]{7,40}',
        r'github\.com/[^/]+/[^/]+/pull/\d+/commits/[0-9a-f]{7,40}',
        r'github\.com/[^/]+/[^/]+/pull/\d+/commit/[0-9a-f]{7,40}',
        r'gitlab\.com/[^/]+/[^/]+/commit/[0-9a-f]{7,40}',
        r'/svn/[^/]+/trunk/[^/]+',
        r'/gitea.+/commit/[0-9a-f]{7,40}',
        r'^https?://(?!github).+/git/.+/commit/[0-9a-f]',
        r'sourceware\.org/git/.*[?&;]a=commit.*[?&;]h=[0-9a-f]{7,40}',  # sourceware.org Git URLs
        r'git\.kernel\.org/.*[?&;]id=[0-9a-f]{7,40}',  # Linux kernel Git URLs
        r'git\.openssl\.org/.*[?&;]h=[0-9a-f]{7,40}',  # OpenSSL Git URLs
        r'\.diff$',
        r'\.patch$'
    ]

    matched_patterns = []
    for i, pattern in enumerate(patch_patterns):
        if re.search(pattern, test_url, re.IGNORECASE):
            matched_patterns.append((i, pattern))
            print(f"✅ 匹配模式 {i}: {pattern}")

    if not matched_patterns:
        print("❌ 未匹配任何补丁模式")

    return matched_patterns

def main():
    """主函数"""
    print("开始调试等价补丁搜索逻辑...")

    # 运行所有测试
    test_url_parsing()
    git_refs = test_cve_references()
    test_patch_tracer_config()
    test_node_type_identification()
    test_priority_ranking()
    analyze_search_failure()

    print("\n=== 建议修复措施 ===")
    print("1. 在URL规范化函数中添加urllib.parse.unquote()解码")
    print("2. 扩展Git平台识别，包括sourceware.org等")
    print("3. 降低置信度阈值到0.5")
    print("4. 增加详细的调试日志输出")
    print("5. 检查网络构建过程中的错误处理")

if __name__ == "__main__":
    main() 