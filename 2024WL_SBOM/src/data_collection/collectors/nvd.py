#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
NVD漏洞数据采集器模块

实现了从美国国家漏洞数据库(NVD)采集漏洞数据的功能。
使用NVD REST API 2.0版本。
API文档：https://nvd.nist.gov/developers/vulnerabilities
"""

from datetime import datetime
from typing import Dict, List, Any
import logging

from .base import BaseVulnerabilityCollector

logger = logging.getLogger(__name__)

class NVDCollector(BaseVulnerabilityCollector):
    """
    NVD漏洞数据采集器
    
    通过NVD REST API获取CVE漏洞数据。
    支持按时间范围查询和增量更新。
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化NVD采集器
        
        Args:
            config: 配置信息，除基类配置外，还可包含：
                - results_per_page: 每页结果数（默认2000）
                - max_results: 最大结果数（默认无限制）
                - max_entries_per_page: 每页返回的最大条目数（默认无限制）
        """
        super().__init__(config)
        self.results_per_page = config.get('results_per_page', 2000)
        self.max_entries_per_page = config.get('max_entries_per_page', self.results_per_page)
        self.max_results = config.get('max_results', float('inf'))  # 默认无限制
        
        # 记录限制
        if self.max_results < float('inf'):
            logger.info(f"【限制设置】最大结果数限制: {self.max_results}条")
        if self.max_entries_per_page < self.results_per_page:
            logger.info(f"【限制设置】每页最大条目数: {self.max_entries_per_page}条")
        
    def fetch_data(self, start_date: datetime, end_date: datetime) -> List[Dict[str, Any]]:
        """
        获取指定时间范围内的NVD漏洞数据
        
        Args:
            start_date: 开始日期
            end_date: 结束日期
            
        Returns:
            包含漏洞信息的字典列表
            
        Raises:
            ValueError: 日期范围无效时
            RequestError: API请求失败时
        """
        # 验证日期范围
        self.validate_date_range(start_date, end_date)
        
        all_vulnerabilities = []
        start_index = 0
        
        try:
            logger.info(f"【开始采集】NVD漏洞数据 - 时间范围: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
            if self.max_results < float('inf'):
                logger.info(f"【限制设置】最大结果数限制: {self.max_results}条")
            
            while True:
                # 检查是否达到最大结果数限制
                if len(all_vulnerabilities) >= self.max_results:
                    logger.info(f"【达到限制】已达到最大结果数限制 ({self.max_results}条)，停止采集")
                    break
                
                # 计算本次请求的每页结果数
                current_per_page = min(self.results_per_page, self.max_entries_per_page)
                if self.max_results < float('inf'):
                    # 如果设置了最大结果数限制，确保不超过限制
                    current_per_page = min(current_per_page, self.max_results - len(all_vulnerabilities))
                
                # 构建请求参数
                params = {
                    'lastModStartDate': start_date.strftime('%Y-%m-%dT%H:%M:%S.000'),
                    'lastModEndDate': end_date.strftime('%Y-%m-%dT%H:%M:%S.999'),
                    'resultsPerPage': current_per_page,
                    'startIndex': start_index
                }
                
                logger.info(f"【请求数据】偏移量: {start_index}, 每页结果数: {current_per_page}")
                
                # 发送API请求 - 直接使用根URL
                response_data = self.make_api_request(
                    endpoint='',  # 这里使用空字符串，因为完整路径已经在配置的URL中指定
                    params=params
                )
                
                # 提取漏洞数据
                vulnerabilities = response_data.get('vulnerabilities', [])
                current_count = len(vulnerabilities)
                logger.info(f"【获取数据】本次获取 {current_count} 条漏洞数据")
                
                if not vulnerabilities:
                    logger.info("【采集完成】没有更多数据，结束获取")
                    break
                    
                # 清理并添加数据
                for i, vuln in enumerate(vulnerabilities):
                    if len(all_vulnerabilities) >= self.max_results:
                        logger.info(f"【达到限制】已达到最大结果数限制 ({self.max_results}条)，停止处理")
                        break
                        
                    cleaned_data = self.clean_data(vuln)
                    all_vulnerabilities.append(cleaned_data)
                    
                    # 定期打印进度
                    if (i+1) % 10 == 0 or i+1 == current_count:
                        logger.info(f"【处理进度】已处理: {i+1}/{current_count}, 总进度: {len(all_vulnerabilities)}")
                
                # 检查是否还有更多数据
                total_results = response_data.get('totalResults', 0)
                if total_results > 0:
                    logger.info(f"【数据统计】总计 {total_results} 条漏洞数据，已获取 {len(all_vulnerabilities)} 条")
                
                if start_index + len(vulnerabilities) >= total_results:
                    logger.info("【采集完成】已获取全部数据，结束获取")
                    break
                
                # 检查是否达到最大结果数限制
                if len(all_vulnerabilities) >= self.max_results:
                    logger.info(f"【达到限制】已达到最大结果数限制 ({self.max_results}条)，停止采集")
                    break
                    
                start_index += len(vulnerabilities)
                logger.info(f"【采集进度】已获取 {len(all_vulnerabilities)}/{total_results} 条漏洞数据")
                
            logger.info(f"【采集完成】NVD数据采集结束，共获取 {len(all_vulnerabilities)} 条漏洞数据")
            return all_vulnerabilities
            
        except Exception as e:
            self.handle_error(e, "获取NVD数据失败")
            return []
            
    def clean_data(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        清理和标准化NVD漏洞数据
        
        Args:
            data: 原始NVD数据
            
        Returns:
            清理后的数据
        """
        try:
            cve = data.get('cve', {})
            
            # 提取基本信息
            cleaned = {
                'source': 'NVD',
                'id': cve.get('id'),
                'published_date': cve.get('published'),
                'last_modified_date': cve.get('lastModified'),
                'vuln_status': cve.get('vulnStatus'),
                
                # 提取描述
                'descriptions': [
                    {
                        'lang': desc.get('lang'),
                        'value': desc.get('value')
                    }
                    for desc in cve.get('descriptions', [])
                ],
                
                # 提取参考链接
                'references': [
                    {
                        'url': ref.get('url'),
                        'source': ref.get('source'),
                        'tags': ref.get('tags', [])
                    }
                    for ref in cve.get('references', [])
                ],
                
                # 提取CVSS评分
                'metrics': {
                    'cvss_v3': self._extract_cvss_v3(cve),
                    'cvss_v2': self._extract_cvss_v2(cve)
                },
                
                # 提取受影响的产品配置
                'configurations': cve.get('configurations', []),
                
                # 原始数据
                'raw_data': data
            }
            
            return cleaned
            
        except Exception as e:
            logger.error(f"清理NVD数据失败: {str(e)}")
            return data
            
    def _extract_cvss_v3(self, cve: Dict[str, Any]) -> Dict[str, Any]:
        """提取CVSS v3评分信息"""
        metrics = cve.get('metrics', {})
        cvss_v3 = metrics.get('cvssMetricV31', [])  # 优先使用v3.1
        if not cvss_v3:
            cvss_v3 = metrics.get('cvssMetricV30', [])
            
        if cvss_v3:
            cvss_data = cvss_v3[0].get('cvssData', {})
            return {
                'version': cvss_data.get('version'),
                'vector_string': cvss_data.get('vectorString'),
                'base_score': cvss_data.get('baseScore'),
                'base_severity': cvss_data.get('baseSeverity'),
                'exploitability_score': cvss_v3[0].get('exploitabilityScore'),
                'impact_score': cvss_v3[0].get('impactScore')
            }
        return {}
        
    def _extract_cvss_v2(self, cve: Dict[str, Any]) -> Dict[str, Any]:
        """提取CVSS v2评分信息"""
        metrics = cve.get('metrics', {})
        cvss_v2 = metrics.get('cvssMetricV2', [])
        
        if cvss_v2:
            cvss_data = cvss_v2[0].get('cvssData', {})
            return {
                'version': '2.0',
                'vector_string': cvss_data.get('vectorString'),
                'base_score': cvss_data.get('baseScore'),
                'exploitability_score': cvss_v2[0].get('exploitabilityScore'),
                'impact_score': cvss_v2[0].get('impactScore'),
                'severity': cvss_v2[0].get('baseSeverity')
            }
        return {} 