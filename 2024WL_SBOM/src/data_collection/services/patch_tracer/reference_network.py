#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
参考网络模块

构建和管理补丁参考网络，提供网络分析和可视化功能。
"""

import os
import re
import time
import logging
import networkx as nx
import matplotlib.pyplot as plt
import matplotlib.patches as mpatches
from dataclasses import dataclass
from typing import List, Dict, Set, Tuple, Any, Optional, Iterator
import uuid
import random
from urllib.parse import urlparse
import datetime
import requests

from .reference_node import ReferenceNode, NodeType

logger = logging.getLogger(__name__)

# Font configuration for CJK characters
# 使用系统中更可能存在的通用字体
plt.rcParams['font.sans-serif'] = ['DejaVu Sans', 'Verdana', 'Arial', 'Helvetica', 'sans-serif']
plt.rcParams['font.family'] = 'sans-serif'
plt.rcParams['axes.unicode_minus'] = False  # Display minus sign correctly

@dataclass
class Edge:
    """
    网络边类
    
    表示参考网络中的一条边，连接两个节点
    """
    source_id: str  # 源节点ID
    target_id: str  # 目标节点ID
    weight: float = 1.0  # 边权重
    description: Optional[str] = None  # 边描述
    
    def __post_init__(self):
        # 确保源节点和目标节点不同
        if self.source_id == self.target_id:
            raise ValueError("边的源节点和目标节点不能相同")

class ReferenceEdge:
    """参考网络边"""
    
    def __init__(self, source_id: str, target_id: str, weight: float = 1.0, description: str = ""):
        """
        初始化参考网络边
        
        Args:
            source_id: 源节点ID
            target_id: 目标节点ID
            weight: 边权重
            description: 边描述
        """
        self.edge_id = str(uuid.uuid4())
        self.source_id = source_id
        self.target_id = target_id
        self.weight = weight
        self.description = description
        
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典
        
        Returns:
            Dict[str, Any]: 边的字典表示
        """
        return {
            'edge_id': self.edge_id,
            'source_id': self.source_id,
            'target_id': self.target_id,
            'weight': self.weight,
            'description': self.description
        }

class ReferenceNetwork:
    """
    参考网络类
    
    构建和管理补丁参考网络图，提供网络分析和可视化功能
    """
    
    def __init__(self, cve_id: str, cve_published_date: Optional[str] = None, cve_affected_cpes: Optional[List[str]] = None):
        """
        初始化参考网络
        
        Args:
            cve_id: CVE ID
            cve_published_date: CVE发布日期 (ISO 8601格式字符串, e.g., '2023-01-15T10:00:00Z')
            cve_affected_cpes: 受影响的CPE列表 (字符串列表)
        """
        self.cve_id = cve_id
        self.cve_published_date = cve_published_date
        self.cve_affected_cpes = cve_affected_cpes or []
        self.nodes: List[ReferenceNode] = []
        self.edges: List[ReferenceEdge] = []
        self.node_map: Dict[str, ReferenceNode] = {}
        self.graph = None # NetworkX 图对象
        self.root_node: Optional[ReferenceNode] = None
    
    def add_node(self, node: ReferenceNode) -> str:
        """
        添加节点，确保没有重复URL
        
        Args:
            node: 要添加的节点
            
        Returns:
            str: 节点ID
        """
        # 确保节点有ID
        if node.node_id is None:
            node.node_id = str(uuid.uuid4())
        
        # 检查深度和节点类型，确保深度大于1的节点不是SOURCE类型
        if node.depth > 1 and node.node_type == NodeType.SOURCE:
            # 将深度大于1的SOURCE节点重新分类为COMMON节点
            node.node_type = NodeType.COMMON
            # 调整类别名称，从source_platform_xxx改为source_xxx
            if node.category_in_type and node.category_in_type.startswith('source_platform_'):
                platform_name = node.category_in_type.replace('source_platform_', '')
                node.category_in_type = f'source_{platform_name}'
            logger.debug(f"调整深度为{node.depth}的节点类型: {node.url} (SOURCE -> COMMON)")
        
        # 检查节点是否已存在
        existing_node = self.get_node_by_url(node.url)
        if existing_node:
            # 如果找到相同URL的节点，合并一些属性
            if not existing_node.title and node.title:
                existing_node.title = node.title
            # 更新节点重要性评分，取最大值
            if hasattr(node, 'importance_score') and node.importance_score > existing_node.importance_score:
                existing_node.importance_score = node.importance_score
            return existing_node.node_id
            
        # 将节点添加到列表和映射
        self.nodes.append(node)
        self.node_map[node.node_id] = node
        
        logger.debug(f"添加节点: {node.url} ({node.node_type.name if node.node_type else '未知类型'})")
        
        return node.node_id
    
    def add_edge(self, 
                source_node: ReferenceNode, 
                target_node: ReferenceNode, 
                weight: float = 1.0, 
                description: str = "") -> str:
        """
        添加边
        
        Args:
            source_node: 源节点
            target_node: 目标节点
            weight: 边权重
            description: 边描述
            
        Returns:
            str: 边ID
        """
        # 确保节点已添加到网络中
        source_id = self.add_node(source_node)
        target_id = self.add_node(target_node)
        
        # 防止自环
        if source_id == target_id:
            logger.warning(f"尝试为节点 {source_id} 添加自环")
            return None
            
        # 检查边是否已存在
        for edge in self.edges:
            if edge.source_id == source_id and edge.target_id == target_id:
                return edge.edge_id
                
        # 创建新边
        edge = ReferenceEdge(source_id, target_id, weight, description)
        
        # 添加到边列表
        self.edges.append(edge)
        
        logger.debug(f"添加边: {source_node.url} -> {target_node.url}")
        
        return edge.edge_id
    
    def build_graph(self) -> nx.DiGraph:
        """
        构建NetworkX图
        
        确保所有节点都有有效的节点ID，并将节点和边添加到图中
        
        Returns:
            nx.DiGraph: 构建的有向图
        """
        self.graph = nx.DiGraph()
        
        # 确保所有节点都有ID
        for node in self.nodes:
            if node.node_id is None:
                # 给没有ID的节点分配新ID
                node.node_id = str(uuid.uuid4())
                # 将节点添加到映射表
                self.node_map[node.node_id] = node
        
        # 添加节点到图
        for node in self.nodes:
            # 确保节点有有效ID
            if node.node_id:
                self.graph.add_node(node.node_id, **node.to_dict())
            else:
                logger.warning(f"跳过ID为空的节点: {node.url}")
        
        # 添加边到图
        for edge in self.edges:
            # 确保源节点和目标节点都存在
            if edge.source_id and edge.target_id:
                self.graph.add_edge(
                    edge.source_id, 
                    edge.target_id, 
                    weight=edge.weight, 
                    description=edge.description
                )
            else:
                logger.warning(f"跳过源节点或目标节点为空的边: {edge.source_id} -> {edge.target_id}")
        
        return self.graph
    
    def get_node_by_id(self, node_id: str) -> Optional[ReferenceNode]:
        """
        根据ID获取节点
        
        Args:
            node_id: 节点ID
            
        Returns:
            Optional[ReferenceNode]: 找到的节点，如果不存在则返回None
        """
        return self.node_map.get(node_id)
    
    def get_node_by_url(self, url: str) -> Optional[ReferenceNode]:
        """
        根据URL获取节点，尝试多种URL变体进行匹配
        
        Args:
            url: 节点URL
            
        Returns:
            Optional[ReferenceNode]: 找到的节点，如果不存在则返回None
        """
        # 直接查找
        for node in self.nodes:
            if node.url == url:
                return node
        
        # 尝试去除尾部斜杠
        url_no_slash = url.rstrip('/')
        for node in self.nodes:
            node_url_no_slash = node.url.rstrip('/')
            if node_url_no_slash == url_no_slash:
                return node
        
        # 尝试忽略大小写比较（对于非GitHub/GitLab URLs）
        if not ('github.com' in url or 'gitlab' in url):
            url_lower = url.lower()
            for node in self.nodes:
                if not ('github.com' in node.url or 'gitlab' in node.url):
                    if node.url.lower() == url_lower:
                        return node
        
        return None
    
    def get_patch_nodes(self) -> List[ReferenceNode]:
        """
        获取所有补丁节点
        
        Returns:
            List[ReferenceNode]: 补丁节点列表
        """
        return [node for node in self.nodes if node.node_type == NodeType.PATCH]
    
    def get_issue_nodes(self) -> List[ReferenceNode]:
        """
        获取所有问题节点
        
        Returns:
            List[ReferenceNode]: 问题节点列表
        """
        return [node for node in self.nodes if node.node_type == NodeType.ISSUE]
    
    def calculate_patch_connectivity(self, patch_node: ReferenceNode) -> float:
        """
        计算补丁节点的连接度
        
        连接度表示从根节点到补丁节点的路径强度，考虑路径长度和边权重
        
        Args:
            patch_node: 补丁节点
            
        Returns:
            float: 连接度得分 (0.0-1.0)
        """
        if not self.graph:
            self.build_graph()
            
        if not self.root_node:
            return 0.0
            
        # 确保根节点和补丁节点都在图中
        if self.root_node.node_id not in self.graph or patch_node.node_id not in self.graph:
            return 0.0
            
        # 检查是否存在路径
        if not nx.has_path(self.graph, self.root_node.node_id, patch_node.node_id):
            return 0.0
            
        # 获取所有简单路径（无环路径）
        try:
            # 限制最大路径长度为6（深度限制）
            all_paths = list(nx.all_simple_paths(
                self.graph, 
                self.root_node.node_id, 
                patch_node.node_id,
                cutoff=6
            ))
        except nx.NetworkXNoPath:
            return 0.0
            
        if not all_paths:
            return 0.0
            
        # 计算路径得分
        path_scores = []
        for path in all_paths:
            # 路径长度得分: 1.0 / (路径长度）
            # 越短的路径得分越高
            length_score = 1.0 / len(path)
            
            # 计算路径边权重的几何平均值
            edge_weights = []
            for i in range(len(path) - 1):
                source_id = path[i]
                target_id = path[i + 1]
                weight = self.graph[source_id][target_id].get('weight', 1.0)
                edge_weights.append(weight)
                
            if edge_weights:
                # 几何平均值
                import math
                weight_score = math.pow(
                    math.prod(edge_weights), 
                    1.0 / len(edge_weights)
                )
            else:
                weight_score = 1.0
                
            # 综合得分 = 长度得分 * 权重得分
            path_score = length_score * weight_score
            path_scores.append(path_score)
            
        # 返回最高得分
        return max(path_scores) if path_scores else 0.0
    
    def get_all_paths(self, source_node: ReferenceNode, target_node: ReferenceNode) -> List[List[str]]:
        """
        获取从源节点到目标节点的所有路径
        
        Args:
            source_node: 源节点
            target_node: 目标节点
            
        Returns:
            List[List[str]]: 路径列表，每个路径是节点ID列表
        """
        if not self.graph:
            self.build_graph()
            
        # 确保两个节点都在图中
        if source_node.node_id not in self.graph or target_node.node_id not in self.graph:
            return []
            
        # 检查是否存在路径
        if not nx.has_path(self.graph, source_node.node_id, target_node.node_id):
            return []
            
        # 获取所有简单路径（无环路径）
        try:
            # 限制最大路径长度为6（深度限制）
            return list(nx.all_simple_paths(
                self.graph, 
                source_node.node_id, 
                target_node.node_id,
                cutoff=6
            ))
        except nx.NetworkXNoPath:
            return []
    
    def get_high_confidence_patches(self, confidence_threshold: float = 0.7) -> List[ReferenceNode]:
        """
        获取高置信度补丁节点
        
        Args:
            confidence_threshold: 置信度阈值 (0.0-1.0)
            
        Returns:
            List[ReferenceNode]: 高置信度补丁节点列表
        """
        patch_nodes = self.get_patch_nodes()
        
        # 计算每个补丁节点的连接度
        high_confidence_patches = []
        for patch_node in patch_nodes:
            score = self.calculate_patch_connectivity(patch_node)
            # 对直接从NVD引用的补丁加权
            if any(edge.source_id == self.root_node.node_id and 
                   edge.target_id == patch_node.node_id for edge in self.edges):
                score = max(score, confidence_threshold)
                
            if score >= confidence_threshold:
                high_confidence_patches.append(patch_node)
                
        return high_confidence_patches
    
    def visualize(self, node_confidences=None, output_path=None, show=False):
        """
        Visualize the reference network with customized layout and node attributes

        Args:
            node_confidences: Dictionary of node confidence scores (optional)
            output_path: Path to save the visualization
            show: Whether to display the visualization
        """
        # 禁用matplotlib的调试输出
        import matplotlib as mpl
        mpl.set_loglevel('error')  # 只显示错误级别日志
        
        # Create a matplotlib figure with larger size
        plt.figure(figsize=(18, 12), dpi=300)  # 增加图像尺寸
        
        # Extract nodes and edges
        G = self.build_graph()
        
        # Set node colors based on node type
        colors = []
        sizes = []
        node_labels = {}
        
        # Categorize nodes for layout and coloring
        root_nodes = []
        platform_nodes = []
        patch_nodes = []
        issue_nodes = []
        common_nodes = []
        repo_nodes = []
        
        # 强制层级化布局 - 将所有节点按depth分组
        nodes_by_depth = {}
        max_depth = 0
        
        for node_id, attrs in G.nodes(data=True):
            node_type = attrs.get('node_type', '')
            depth = attrs.get('depth', 0)
            max_depth = max(max_depth, depth)
            
            if depth not in nodes_by_depth:
                nodes_by_depth[depth] = []
            nodes_by_depth[depth].append(node_id)
            
            # Categorize based on node type
            if node_type == 'ROOT':
                root_nodes.append(node_id)
            elif node_type == 'SOURCE':
                platform_nodes.append(node_id)
            elif node_type == 'PATCH':
                patch_nodes.append(node_id)
            elif node_type == 'ISSUE':
                issue_nodes.append(node_id)
            elif node_type == 'REPO':
                repo_nodes.append(node_id)
            else:
                common_nodes.append(node_id)
        
        # 创建强制性的层级化布局
        pos = {}
        vertical_spacing = 1000  # 固定的垂直间距
        
        # 第0层 - 根节点 (顶部中心)
        if 0 in nodes_by_depth:
            for i, node_id in enumerate(nodes_by_depth[0]):
                pos[node_id] = (0, 0)  # 根节点在顶部中心
        
        # 第1层 - 平台节点 (均匀分布在第1层)
        if 1 in nodes_by_depth:
            nodes = nodes_by_depth[1]
            count = len(nodes)
            if count > 0:
                width = 3500 if count > 5 else 3000
                spacing = width / (count + 1)
                
                # 为平台节点定义固定顺序，确保布局一致性
                platform_order = {
                    "NVD": 1,
                    "GITHUB": 2,
                    "GITLAB": 3,
                    "APACHE": 4,
                    "DEBIAN": 5,
                    "REDHAT": 6,
                    "OTHER": 7
                }
                
                # 按平台类型排序
                def get_platform_sort_key(node_id):
                    source = G.nodes[node_id].get('source', '')
                    return platform_order.get(source, 999)
                    
                sorted_platform_nodes = sorted(nodes, key=get_platform_sort_key)
                
                for i, node_id in enumerate(sorted_platform_nodes):
                    pos[node_id] = (-width/2 + spacing * (i + 1), -vertical_spacing)
        
        # 其他层级 - 均匀分布，不考虑父节点位置
        for depth in range(2, max_depth + 1):
            if depth not in nodes_by_depth:
                continue
                
            nodes = nodes_by_depth[depth]
            count = len(nodes)
            
            # 如果该层只有一个节点，放在中心
            if count == 1:
                node_id = nodes[0]
                pos[node_id] = (0, -vertical_spacing * depth)
                continue
            
            # 计算此深度层级的宽度和间距
            width = 4000 + (depth - 2) * 500  # 随深度增加宽度
            spacing = width / (count + 1)
            
            # 根据节点类型和标签进行排序，确保一致的顺序
            def get_node_sort_key(node_id):
                node_type = G.nodes[node_id].get('node_type', '')
                node_title = G.nodes[node_id].get('title', '')
                node_url = G.nodes[node_id].get('url', '')
                
                # 排序优先级: 1.类型 2.标题 3.URL
                type_order = {
                    'PATCH': 0,   # 补丁优先
                    'ISSUE': 1,   # 问题其次
                    'REPO': 2,    # 仓库
                    'COMMON': 3,  # 普通节点
                    '': 4         # 未知类型最后
                }
                type_value = type_order.get(node_type, 999)
                return (type_value, node_title, node_url)
                
            # 按照定义的顺序排序节点
            sorted_nodes = sorted(nodes, key=get_node_sort_key)
            
            # 简单均匀分布
            for i, node_id in enumerate(sorted_nodes):
                pos[node_id] = (-width/2 + spacing * (i + 1), -vertical_spacing * depth)

        # Draw edges with width based on weight
        edge_weights = [G[u][v].get('weight', 1.0) for u, v in G.edges()]
        weights_normalized = [w*2 for w in edge_weights]
        
        # 增强边的视觉效果
        nx.draw_networkx_edges(
            G, pos, 
            width=weights_normalized, 
            alpha=0.6, 
            edge_color='lightgray', 
            arrows=True,
            arrowsize=15,
            connectionstyle='arc3,rad=0.1'  # 增加弧度
        )
        
        # 设置节点大小
        # 放大根节点和平台节点
        root_node_size = 1500  # 增加根节点尺寸（原来是800）
        platform_node_size = 1200  # 增加平台节点尺寸（原来是600）
        issue_node_size = 800  
        repo_node_size = 1000
        common_node_size = 400
        patch_node_size = 800
        
        # Draw nodes
        # Root nodes (red)
        nx.draw_networkx_nodes(G, pos, 
                              nodelist=root_nodes, 
                              node_color='red', 
                              node_size=root_node_size, 
                              alpha=0.9)
        
        # Platform nodes (blue) - all platform nodes are included
        nx.draw_networkx_nodes(G, pos, 
                              nodelist=platform_nodes, 
                              node_color='blue', 
                              node_size=platform_node_size, 
                              alpha=0.8)
        
        # Issue nodes (changed from orange to light blue-purple #B2B2FF)
        nx.draw_networkx_nodes(G, pos, 
                              nodelist=issue_nodes,
                              node_color='#B2B2FF', 
                              node_size=issue_node_size, 
                              alpha=0.8)
        
        # Repo nodes (changed from orange to light blue-purple #B2B2FF)
        nx.draw_networkx_nodes(G, pos, 
                              nodelist=repo_nodes,
                              node_color='#2F2F2F', 
                              node_size=repo_node_size, 
                              alpha=0.8)

        # Patch nodes (yellow/green)
        patch_node_colors = []
        for node_id in patch_nodes:
            # 根据置信度着色
            score = 0.0
            if node_confidences and node_id in node_confidences:
                score = node_confidences[node_id]
            if score >= 0.8:
                patch_node_colors.append('lime')  # 高置信度(>=0.8): 亮绿色
            elif score >= 0.5:
                patch_node_colors.append('yellowgreen')  # 中等置信度(>=0.5): 黄绿色
            else:
                patch_node_colors.append('yellow')  # 低置信度(<0.5): 黄色
        
        nx.draw_networkx_nodes(G, pos, 
                              nodelist=patch_nodes,
                              node_color=patch_node_colors, 
                              node_size=patch_node_size, 
                              alpha=0.8)
        
        # Common nodes (gray)
        nx.draw_networkx_nodes(G, pos, 
                              nodelist=common_nodes,
                              node_color='gray', 
                              node_size=common_node_size, 
                              alpha=0.7)

        # Optimize node labels
        for node_id in G.nodes():
            if node_id in root_nodes:
                # CVE root node displays CVE ID
                node_labels[node_id] = self.cve_id
            elif node_id in platform_nodes:
                # Platform nodes display source name
                source = G.nodes[node_id].get('source', '')
                if source:
                    node_labels[node_id] = source
                else:
                    url = G.nodes[node_id].get('url', '')
                    node_labels[node_id] = url.split('/')[2] if '/' in url else url
            elif node_id in patch_nodes:
                # Optimize patch node display
                node_url = G.nodes[node_id].get('url', '')
                
                # 使用节点ID的前四位作为哈希值
                node_hash = node_id[:4]
                
                # GitHub/GitLab commit - 格式为 "commit@四位哈希"
                if 'commit' in node_url and ('github.com' in node_url or 'gitlab' in node_url):
                    node_labels[node_id] = f"commit@{node_hash}"
                # SVN commit - 格式为 "svn@四位哈希"
                elif 'svn' in node_url and ('revision=' in node_url or '/r' in node_url):
                    node_labels[node_id] = f"svn@{node_hash}"
                # 补丁/diff文件 - 格式为 "diff@四位哈希"
                elif node_url.endswith('.patch') or node_url.endswith('.diff'):
                    node_labels[node_id] = f"diff@{node_hash}"
                # 其他补丁类型 - 格式为 "patch@四位哈希"
                else:
                    node_labels[node_id] = f"patch@{node_hash}"
            elif node_id in issue_nodes:
                # Optimize Issue node display
                node_url = G.nodes[node_id].get('url', '')
                
                # 使用节点ID的前四位作为哈希值
                node_hash = node_id[:4]
                
                # GitHub/GitLab issues - 格式为 "git@四位哈希"
                if re.search(r'(?:github|gitlab)\.com/[^/]+/[^/]+/issues/\d+', node_url, re.IGNORECASE) or \
                   re.search(r'(?:github|gitlab)\.com/[^/]+/[^/]+/pull/\d+', node_url, re.IGNORECASE):
                    node_labels[node_id] = f"git@{node_hash}"
                    continue
                
                # JIRA issues - 格式为 "jira@四位哈希"
                if re.search(r'browse/[A-Z]+-\d+', node_url):
                    node_labels[node_id] = f"jira@{node_hash}"
                    continue
                
                # 其他类型 - 格式为 "主域名@四位哈希"
                # 提取主域名
                domain = ""
                if '/' in node_url:
                    parts = node_url.split('/')
                    if len(parts) > 2:
                        domain_parts = parts[2].split('.')
                        if len(domain_parts) > 1:
                            domain = domain_parts[0] if domain_parts[0] != 'www' else domain_parts[1]
                        else:
                            domain = domain_parts[0]
                
                # 使用节点ID的前四位作为哈希值
                node_hash = node_id[:4]
                
                # 创建标签："主域名@哈希"
                if domain:
                    node_labels[node_id] = f"{domain}@{node_hash}"
                else:
                    # 如果无法提取域名，使用节点类型+哈希
                    node_type = G.nodes[node_id].get('node_type', 'unknown')
                    node_labels[node_id] = f"{node_type}@{node_hash}"
            else:
                # 处理普通节点，特别是对最后一层的节点
                depth = G.nodes[node_id].get('depth', 0)
                url = G.nodes[node_id].get('url', '')
                
                # 找出最大深度 - 最后一层
                max_depth_in_graph = max([G.nodes[n].get('depth', 0) for n in G.nodes()])
                is_deepest_layer = (depth == max_depth_in_graph)
                
                # 对最后一层的节点使用特殊格式："主域名@节点哈希前4位"
                if is_deepest_layer:
                    # 提取主域名
                    domain = ""
                    if '/' in url:
                        parts = url.split('/')
                        if len(parts) > 2:
                            domain_parts = parts[2].split('.')
                            if len(domain_parts) > 1:
                                domain = domain_parts[0] if domain_parts[0] != 'www' else domain_parts[1]
                            else:
                                domain = domain_parts[0]
                    
                    # 使用节点ID的前四位作为哈希值
                    node_hash = node_id[:4]
                    
                    # 创建标签："主域名@哈希"
                    if domain:
                        node_labels[node_id] = f"{domain}@{node_hash}"
                    else:
                        # 如果无法提取域名，使用节点类型+哈希
                        node_type = G.nodes[node_id].get('node_type', 'unknown')
                        node_labels[node_id] = f"{node_type}@{node_hash}"
                
                # 第3-4层的节点保持不变
                elif depth >= 2 and depth < max_depth_in_graph:
                    domain = url.split('/')[2] if '/' in url and len(url.split('/')) > 2 else ''
                    
                    # 简化第三层和第四层节点标签
                    if domain:
                        # 提取主域名部分，例如从 github.com 提取 github
                        main_domain = domain.split('.')[0]
                        # 过滤掉一些常见的域名前缀
                        if main_domain in ['www', 'docs', 'help', 'support']:
                            main_parts = domain.split('.')
                            if len(main_parts) > 1:
                                main_domain = main_parts[1]
                        
                        # 特殊处理一些域名
                        if 'cisco' in domain:
                            node_labels[node_id] = "Cisco"
                        elif 'apache' in domain:
                            node_labels[node_id] = "Apache"
                        elif 'github' in domain:
                            # 如果是GitHub仓库，只显示仓库名
                            parts = url.split('/')
                            if len(parts) >= 5:
                                node_labels[node_id] = parts[4]  # 仓库名
                            else:
                                node_labels[node_id] = "GitHub"
                        elif 'gitlab' in domain:
                            node_labels[node_id] = "GitLab"
                        elif 'debian' in domain:
                            node_labels[node_id] = "Debian"
                        elif 'ubuntu' in domain:
                            node_labels[node_id] = "Ubuntu"
                        elif 'redhat' in domain or 'access.redhat' in domain:
                            node_labels[node_id] = "RedHat"
                        elif 'netapp' in domain:
                            node_labels[node_id] = "NetApp"
                        elif 'intel' in domain:
                            node_labels[node_id] = "Intel"
                        elif 'microsoft' in domain or 'msrc' in domain:
                            node_labels[node_id] = "Microsoft"
                        elif 'oracle' in domain:
                            node_labels[node_id] = "Oracle"
                        elif 'ibm' in domain:
                            node_labels[node_id] = "IBM"
                        elif 'hp' in domain or 'hpe' in domain:
                            node_labels[node_id] = "HP"
                        elif 'vmware' in domain:
                            node_labels[node_id] = "VMware"
                        elif 'sap' in domain:
                            node_labels[node_id] = "SAP"
                        elif 'adobe' in domain:
                            node_labels[node_id] = "Adobe"
                        elif 'apple' in domain:
                            node_labels[node_id] = "Apple"
                        elif 'google' in domain:
                            node_labels[node_id] = "Google"
                        elif 'facebook' in domain or 'meta' in domain:
                            node_labels[node_id] = "Meta"
                        elif 'amazon' in domain or 'aws' in domain:
                            node_labels[node_id] = "AWS"
                        else:
                            # 对于其他域名，显示首字母大写的域名
                            node_labels[node_id] = main_domain.capitalize()
                    else:
                        # 如果无法提取主域名，使用标题
                        title = G.nodes[node_id].get('title', '')
                        if title and len(title) <= 10:
                            node_labels[node_id] = title
                        else:
                            node_labels[node_id] = ""
                else:
                    # 第1-2层的其他节点保持原有逻辑
                    if domain.startswith('github.com'):
                        parts = url.split('/')
                        if len(parts) >= 5:
                            repo = '/'.join(parts[3:5])
                            if len(parts) > 5:
                                node_labels[node_id] = f"{repo}/{parts[-1]}"
                            else:
                                node_labels[node_id] = repo
                    else:
                        path = url.split('/')[-1] if '/' in url else url
                        if domain:
                            if path and path != domain and path != '':
                                node_labels[node_id] = f"{domain.split('.')[0]}/../{path}"
                            else:
                                node_labels[node_id] = domain.split('.')[0]
                        else:
                            title = G.nodes[node_id].get('title', '')
                            node_labels[node_id] = title if title else path
        
        # 绘制节点标签，调整标签位置和样式
        text_kwargs = {
            'font_size': 12,
            'font_weight': 'bold',
            'font_family': 'sans-serif',
            'bbox': dict(facecolor='white', alpha=0.7, edgecolor='none', boxstyle='round,pad=0.3'),
            'horizontalalignment': 'center',
            'verticalalignment': 'center'
        }
        
        nx.draw_networkx_labels(G, pos, node_labels, **text_kwargs)
        
        # 设置标题
        plt.title(f"Reference Network for {self.cve_id}", fontsize=20)
        
        # 添加图例
        patch_legend = [
            mpatches.Patch(color='red', label='CVE Node'),
            mpatches.Patch(color='blue', label='Platform Node'),
            mpatches.Patch(color='#B2B2FF', label='Issue Node'),
             mpatches.Patch(color='#2F2F2F', label='Repository Node'),  # 添加仓库节点的图例
            mpatches.Patch(color='lime', label='Low Confidence Patch (<0.3)'),
            mpatches.Patch(color='yellow', label='High Confidence Patch (≥0.8)'),
            mpatches.Patch(color='gray', label='Common Reference Node')
        ]
        
        plt.legend(handles=patch_legend, loc='upper left', title='Node Types')
        plt.axis('off')
        
        # 保存图像
        if output_path:
            plt.tight_layout()
            plt.savefig(output_path, bbox_inches='tight', pad_inches=0.5)
            
        if show:
            plt.show()
            
        return plt.gcf()
    
    def __str__(self) -> str:
        """字符串表示"""
        return f"ReferenceNetwork(cve_id={self.cve_id}, nodes={len(self.nodes)}, edges={len(self.edges)})"
    
    def __repr__(self) -> str:
        """调试表示"""
        return self.__str__()

    def _get_node_levels(self) -> Dict[str, int]:
        """
        计算每个节点的层级
        
        Returns:
            Dict[str, int]: 节点ID到层级的映射
        """
        if not self.graph or not self.root_node:
            return {}
            
        levels = {}
        # 使用BFS计算层级
        queue = [(self.root_node.node_id, 0)]
        visited = {self.root_node.node_id}
        
        while queue:
            node_id, level = queue.pop(0)
            levels[node_id] = level
            
            # 获取所有邻居节点
            for neighbor in self.graph.neighbors(node_id):
                if neighbor not in visited:
                    visited.add(neighbor)
                    queue.append((neighbor, level + 1))
                    
        return levels 
        
    @staticmethod
    def identify_node_type(url: str) -> Tuple[Optional[NodeType], Optional[str]]:
        """
        根据URL识别节点类型
        该方法分析URL并确定其属于哪种节点类型（补丁、问题、仓库等）
        
        实现逻辑：
        1. 首先排除特定的黑名单后缀和关键词
        2. 验证URL有效性
        3. 按优先级识别不同类型的URL：
           - 首先尝试识别补丁URL（GitHub/GitLab commit等）
           - 然后是问题URL（GitHub/GitLab issues等）
           - 接着是代码仓库URL
           - 最后是平台URL（安全公告网站等）
        4. 对于无法识别的URL，返回通用类型
        
        对于GitLab URL的识别：
        - 使用'gitlab'关键词匹配，而不仅限于'gitlab.com'域名
        - 这允许识别如'gitlab.gnome.org'等子域名中的GitLab实例
        - 通过URL路径模式（如'/commit/'、'/merge_requests/'等）进一步确定节点类型
        
        Args:
            url: 要分析的节点URL
            
        Returns:
            Tuple[Optional[NodeType], Optional[str]]: 节点类型枚举和子类别，如果无法识别则返回(None, None)
        """
        if not url:
            return None, None
        
        # 排除黑名单后缀和关键词
        request_black_str_list = [
            '.zip', '.gz', '.tgz', '.msi', '.exe', '.tar', '.iso', 
            '.sha256', '.asc', '.xz', '.atom', '.pdf', '/blob/', 
            '/packages/', '/settings/', '/download/', '.js', '.css'
        ]
        for black_str in request_black_str_list:
            if black_str in url:
                return NodeType.COMMON, None
        
        # 首先验证是否为有效URL
        if not re.search(r'http(s)?://(([\w-]+\.)+\w+(:\d{1,5})?)', url):
            return NodeType.COMMON, None  # 不是有效URL
        
        # 1. 首先识别是否是补丁URL
        
        # GitHub commit - 最高优先级
        # 直接commit页面
        if ('github.com' in url and '/commit/' in url) or ('gitlab' in url and '/commit/' in url):
            # 验证是否包含SHA-1哈希值
            sha_pattern = r'/commit/([0-9a-f]{7,40})'
            if re.search(sha_pattern, url):
                return NodeType.PATCH, 'git_commit'
        
        # Gitweb commit URL 格式检测 - 作为补丁类型
        if 'gitweb' in url and 'commitdiff' in url:
            # 验证是否包含 'h=' 参数的哈希值
            sha_pattern = r'[;&]h=([0-9a-f]{7,40})'
            if re.search(sha_pattern, url):
                return NodeType.PATCH, 'git_commit'
        
        # 简化后的OpenSSL gitweb URL
        if 'git.openssl.org/gitweb' in url:
            return NodeType.PATCH, 'git_commit'
            
        # sourceware.org Git URL - 支持glibc等GNU项目
        if 'sourceware.org/git' in url and ('a=commit' in url or 'a=commitdiff' in url):
            # 验证是否包含 'h=' 参数的哈希值
            sha_pattern = r'[;&]h=([0-9a-f]{7,40})'
            if re.search(sha_pattern, url):
                return NodeType.PATCH, 'git_commit'
            
        # 使用仓库映射模块识别非GitHub但有官方GitHub镜像的项目URL
        try:
            # 从utils.py导入extract_commit_hash_from_url函数
            from .utils import extract_commit_hash_from_url
            # 从repo_mapping.py导入identify_project_from_url函数
            from .repo_mapping import identify_project_from_url
            
            # 判断URL是否包含commit hash
            commit_hash = extract_commit_hash_from_url(url)
            if commit_hash and len(commit_hash) >= 7:
                # 是否能映射到官方仓库
                repo_info = identify_project_from_url(url)
                if repo_info:
                    logger.debug(f"URL {url} 映射到官方GitHub仓库: {repo_info[0]}/{repo_info[1]}")
                    return NodeType.PATCH, 'mapped_git_commit'
        except Exception as e:
            logger.debug(f"使用仓库映射识别补丁URL时出错: {str(e)}")
                
        # PR中的commit链接 - 同样视为补丁
        if (('github.com' in url and '/pull/' in url and '/commits/' in url) or 
            ('gitlab' in url and '/merge_requests/' in url and '/commits/' in url)):
            # 验证是否包含SHA-1哈希值
            sha_pattern = r'/commits/([0-9a-f]{7,40})'
            if re.search(sha_pattern, url):
                return NodeType.PATCH, 'git_commit'
                
        # 单个PR commit (github.com/.../pull/123/commit/abc123) - 同样视为补丁
        if ('github' in url and '/pull/' in url and '/commit/' in url) or (
            'gitlab' in url and '/merge_requests/' in url and '/commit/' in url):
            # 验证是否包含SHA-1哈希值
            sha_pattern = r'/commit/([0-9a-f]{7,40})'
            if re.search(sha_pattern, url):
                return NodeType.PATCH, 'git_commit'
        
        # 2. GitHub/GitLab pull request (这是issue类型)
        if (('github' in url and '/pull/' in url) or 
            ('gitlab' in url and '/merge_requests/' in url)):
            pr_pattern = r'/(pull|merge_requests)/(\d+)'
            if re.search(pr_pattern, url):
                return NodeType.ISSUE, 'git_pr'
        
        # GitHub/GitLab issue (这是issue类型)
        if (('github' in url and '/issues/' in url) or 
            ('gitlab' in url and '/issues/' in url)):
            issue_pattern = r'/issues/(\d+)'
            if re.search(issue_pattern, url):
                return NodeType.ISSUE, 'git_issue'
        
        # SVN commit
        if 'svn' in url and ('revision=' in url or '/r' in url):
            return NodeType.PATCH, 'svn_commit'
        
        # GitHub/GitLab仓库
        if (('github' in url and url.count('/') <= 4) or 
            ('gitlab' in url and url.count('/') <= 4)):
            path_parts = urlparse(url).path.strip('/').split('/')
            if len(path_parts) == 2:  # 仓库路径: username/repo
                return NodeType.REPO, 'github_repo'
        
        # 安全公告网站
        security_sites = [
            'nvd.nist.gov', 'cve.mitre.org', 'cisa.gov', 
            'kb.cert.org', 'securityfocus', 'exploit-db'
        ]
        for site in security_sites:
            if site in url:
                # 判断URL是否是第一层平台节点，还是具体的安全公告
                parsed_url = urlparse(url)
                path_parts = parsed_url.path.strip('/').split('/')
                
                # 只有完全没有路径的URL才视为SOURCE类型
                # 例如 https://nvd.nist.gov/ 或 https://cve.mitre.org/
                if not parsed_url.path or parsed_url.path == '/':
                    # 提取平台名称用于分类
                    platform_name = site.split(".")[0].lower()
                    return NodeType.SOURCE, f'security_platform_{platform_name}'
                else:
                    # 如果URL包含更详细的路径（如vuln/detail/CVE-xxxx），则视为普通安全页面
                    return NodeType.COMMON, 'security_site'
        
        # 根据域名划分来源类型
        common_sources = {
            "nvd.nist.gov": "NVD",
            "debian": "DEBIAN",
            "redhat": "REDHAT",
            "apache": "APACHE"
        }
        
        for domain, source in common_sources.items():
            if domain in url:
                parsed_url = urlparse(url)
                path_parts = parsed_url.path.strip('/').split('/')
                
                # 只有完全没有路径的URL才视为SOURCE类型
                # 例如 https://apache.org/ 或 https://debian.org/
                if not parsed_url.path or parsed_url.path == '/':
                    return NodeType.SOURCE, f'source_platform_{source.lower()}'
                else:
                    # 如果URL包含任何路径，则视为普通页面
                    return NodeType.COMMON, f'source_{source.lower()}'
        
        # 无法识别或不符合特定类型的URL
        if 'github' in url:
            return NodeType.COMMON, 'common_github_url'
        
        return NodeType.COMMON, 'common_url'

    def identify_node_type_by_url(self, url: str) -> str:
        """
        使用正则表达式匹配URL模式来识别节点类型
        
        Args:
            url: 节点URL
            
        Returns:
            str: 节点类型（'root', 'patch', 'issue', 'common'）
        """
        # 补丁类型URL模式
        patch_patterns = [
            r'github\.com/[^/]+/[^/]+/commit/',
            r'github\.com/[^/]+/[^/]+/pull/',
            r'gitlab\.[^/]+/[^/]+/[^/]+/commit/',
            r'gitlab\.[^/]+/[^/]+/[^/]+/merge_requests/',
            r'/svn/[^/]+/trunk/[^/]+',
            r'/git/[^/]+/commit/',
            r'gitweb.*[?&]p=.*[?&]a=commitdiff',  # gitweb格式提交URLs
            r'git\.openssl\.org/gitweb',  # 简化后的OpenSSL gitweb URL
            r'git\.kernel\.org',  # Linux内核Git仓库
            r'git\.postgresql\.org',  # PostgreSQL Git仓库
            r'git\.php\.net',  # PHP Git仓库
            r'git\.gnome\.org',  # GNOME Git仓库
            r'git\.savannah\.gnu\.org',  # GNU Savannah Git仓库
            r'gitbox\.apache\.org',  # Apache Gitbox仓库
            r'/patch[^/]*\.diff',
            r'/patch[^/]*\.patch',
            r'/bugs/[^/]+/attachment\.cgi\?id=\d+&action=diff',
        ]
        
        for pattern in patch_patterns:
            if re.search(pattern, url):
                return 'patch'
        
        # 问题类型URL模式
        issue_patterns = [
            r'github\.com/[^/]+/[^/]+/issues/',
            r'gitlab\.[^/]+/[^/]+/[^/]+/issues/',
            r'bugs\.debian\.org/cgi-bin/bugreport\.cgi',
            r'bugzilla\.[^/]+/show_bug\.cgi',
            r'issues\.apache\.org/jira/browse/',
            r'nvd\.nist\.gov/vuln/detail/',
            r'cve\.mitre\.org/cgi-bin/cvename\.cgi',
            r'security-tracker\.debian\.org/tracker/',
            r'access\.redhat\.com/security/cve/',
            r'ubuntu\.com/security/notices/',
        ]
        
        for pattern in issue_patterns:
            if re.search(pattern, url):
                return 'issue'
                
        # 默认为普通节点
        return 'common' 

    def compute_node_priorities(self, nodes: List[ReferenceNode], method: str = 'DP', cutoff: int = 4) -> Dict[ReferenceNode, float]:
        """
        计算节点优先级
        
        Args:
            nodes: 要计算优先级的节点列表
            method: 计算方法
                - 'DP': 距离倒数优先级
                - 'CN': 所有路径权重和
                - 'DG': 节点度数
                - 'PC': 路径计数
            cutoff: 路径最大长度截断
            
        Returns:
            Dict[ReferenceNode, float]: 节点优先级映射
        """
        # 确保图已构建
        if not self.graph:
            self.build_graph()
            
        node_priority_info = {}
        
        # 获取根节点
        root_nodes = [n for n in self.nodes if n.node_type == NodeType.ROOT]
        if not root_nodes:
            logger.warning("No root node found in network")
            return {}
            
        root_node = root_nodes[0]
        
        # 获取节点度数信息
        node_degree_info = dict(self.graph.degree())
        
        for node in nodes:
            node_id = node.node_id
            
            if method == 'CN':  # 所有路径权重和
                try:
                    # 所有简单路径
                    node_paths_to_CVE = list(nx.algorithms.simple_paths.all_simple_paths(
                        self.graph, root_node.node_id, node_id, cutoff=cutoff
                    ))
                    
                    # 计算路径权重
                    node_priority_value_list = []
                    for path in node_paths_to_CVE:
                        # 计算权重：1/2^(路径长度-2)
                        # 如果路径中包含来源节点，则额外减1
                        path_nodes = [self.get_node_by_id(p) for p in path]
                        has_source = any(n.node_type == NodeType.SOURCE for n in path_nodes if n)
                        if has_source:
                            weight = 1 / pow(2, (len(path) - 3))
                        else:
                            weight = 1 / pow(2, (len(path) - 2))
                        node_priority_value_list.append(weight)
                    
                    # 总优先级为所有路径权重之和
                    node_priority = sum(node_priority_value_list)
                    
                except (nx.NetworkXNoPath, nx.NetworkXError):
                    node_priority = 0
            
            elif method == 'PC':  # 路径计数
                try:
                    # 计算从根节点到当前节点的所有路径数量
                    node_paths_to_CVE = list(nx.algorithms.simple_paths.all_simple_paths(
                        self.graph, root_node.node_id, node_id, cutoff=cutoff
                    ))
                    node_priority = len(node_paths_to_CVE)
                    
                except (nx.NetworkXNoPath, nx.NetworkXError):
                    node_priority = 0
            
            elif method == 'DG':  # 节点度数
                # 使用节点的度数作为优先级
                node_priority = node_degree_info.get(node_id, 0)
            
            else:  # 默认DP: 距离倒数优先级
                try:
                    # 计算最短路径
                    shortest_path = nx.algorithms.shortest_path(self.graph, root_node.node_id, node_id)
                    
                    # 计算距离（路径长度减去节点数）
                    path_nodes = [self.get_node_by_id(p) for p in shortest_path]
                    has_source = any(n.node_type == NodeType.SOURCE for n in path_nodes if n)
                    
                    if has_source:
                        node_distance = len(shortest_path) - 2  # 如果经过来源节点，减2
                    else:
                        node_distance = len(shortest_path) - 1  # 否则减1
                        
                    node_distance = max(node_distance, 1)  # 确保距离至少为1
                    
                    # 优先级为距离的倒数
                    node_priority = 1 / node_distance
                    
                except (nx.NetworkXNoPath, nx.NetworkXError):
                    node_priority = 0
            
            # 存储优先级
            node_priority_info[node] = node_priority
            
            # 同时更新节点的重要性评分
            node.importance_score = node_priority
            
        return node_priority_info
    
    def get_patches_by_rules(self, rules: Dict[str, List[str]] = None, 
                           repo_similarity_threshold: float = 0.6, 
                           max_date_difference_days: int = 730) -> List[ReferenceNode]:
        """
        根据规则获取补丁节点
        
        Args:
            rules: 规则字典，包含以下键:
                - 'patch_type': 补丁类型列表，如 ['git_commit', 'svn']
                - 'cutoff': 路径长度限制 (整数)
                - 'patch_content': 补丁内容要求列表，支持:
                    - 'patch_date': 检查补丁日期与CVE发布日期是否在指定范围内
                    - 'only_target_CPEs': 检查Git仓库与受影响CPE的相似度
            repo_similarity_threshold: 'only_target_CPEs' 规则的仓库相似度阈值 (0.0 到 1.0)
            max_date_difference_days: 'patch_date' 规则允许的最大日期差异（天数）
                
        Returns:
            List[ReferenceNode]: 筛选后的补丁节点列表
        """
        # 如果没有规则，返回所有补丁节点
        if not rules:
            return [node for node in self.nodes if node.node_type == NodeType.PATCH]
            
        # 找出所有补丁节点
        candidate_patch_nodes = [node for node in self.nodes if node.node_type == NodeType.PATCH]
        selected_patch_nodes = []
        
        # 解析规则中的patch_content
        patch_content_rules = rules.get('patch_content', [])
        
        for node in candidate_patch_nodes:
            # 默认节点符合要求
            node_valid = True
            
            # --- 1. 检查补丁类型 --- 
            if 'patch_type' in rules and rules['patch_type'] != ['all']:
                if not node.category_in_type or node.category_in_type not in rules['patch_type']:
                    node_valid = False
                    continue
            
            # --- 2. 检查到CVE的路径长度 (Cutoff) --- 
            if 'cutoff' in rules:
                try:
                    cutoff_value = int(rules['cutoff'])
                    root_nodes = [n for n in self.nodes if n.node_type == NodeType.ROOT]
                    if root_nodes:
                        # 确保图已构建
                        if not self.graph:
                            self.build_graph()
                            
                        # 检查根节点和当前节点是否在图中
                        if root_nodes[0].node_id not in self.graph or node.node_id not in self.graph:
                             logger.warning(f"节点 {node.node_id} 或根节点在图中不存在，无法进行cutoff检查。")
                             node_valid = False
                             continue
                             
                        # 检查是否存在足够短的路径
                        paths = list(nx.algorithms.simple_paths.all_simple_paths(
                            self.graph, root_nodes[0].node_id, node.node_id, cutoff=cutoff_value
                        ))
                        if not paths:
                            node_valid = False
                            continue
                    else:
                         logger.warning("无法执行cutoff检查：未找到根节点。")
                         node_valid = False # 没有根节点无法检查
                         continue
                         
                except (nx.NetworkXNoPath, nx.NetworkXError, ValueError) as e:
                    # 如果没有路径、图错误或cutoff值无效，则认为节点不符合
                    logger.debug(f"节点 {node.node_id} 的cutoff检查失败: {e}")
                    node_valid = False
                    continue
            
            # --- 3. 检查补丁内容规则 --- 
            if patch_content_rules:
                # 3.1 检查补丁日期 (patch_date)
                if node_valid and 'patch_date' in patch_content_rules:
                    if not self.cve_published_date:
                        # 如果没有提供CVE发布日期，仍然保留节点(宽松处理)
                        logger.info(f"由于缺少CVE发布日期，跳过对 {node.url} 的patch_date检查，但保留该节点。")
                    else:
                        # 尝试获取补丁节点的实际提交日期
                        commit_date_str = self._get_commit_date(node.url) 
                        
                        if not commit_date_str:
                            # 如果无法获取提交日期, 宽松模式: 保留节点
                            logger.debug(f"无法获取 {node.url} 的提交日期，跳过日期检查但保留该节点。")
                        else:
                            try:
                                # 比较日期
                                cve_dt = datetime.datetime.fromisoformat(self.cve_published_date.rstrip('Z'))
                                commit_dt = datetime.datetime.fromisoformat(commit_date_str.rstrip('Z'))
                                date_diff = abs((cve_dt - commit_dt).days)
                                if date_diff > max_date_difference_days:
                                    logger.debug(f"节点 {node.url} 未通过patch_date检查: 差异 {date_diff} 天 > {max_date_difference_days}")
                                    node_valid = False
                                    continue
                                else:
                                    logger.debug(f"节点 {node.url} 通过patch_date检查: 差异 {date_diff} 天。")
                            except ValueError:
                                # 如果日期格式无效，宽松模式: 保留节点
                                logger.warning(f"CVE ({self.cve_published_date}) 或提交 ({commit_date_str}) 的日期格式无效。跳过日期检查但保留该节点。")
                
                # 3.2 检查目标CPE相似度 (only_target_CPEs)
                if node_valid and 'only_target_CPEs' in patch_content_rules:
                    if not self.cve_affected_cpes or len(self.cve_affected_cpes) == 0:
                        # 如果没有提供受影响的CPE列表，宽松模式: 保留节点
                        logger.info(f"由于缺少CVE受影响的CPE，跳过对 {node.url} 的only_target_CPEs检查，但保留该节点。")
                    else:
                        # 仅对 Git Commit 类型的节点进行检查 (GitHub/GitLab)
                        if node.category_in_type == 'git_commit' and ('github.com' in node.url or 'gitlab' in node.url):
                            try:
                                parsed_url = urlparse(node.url)
                                path_parts = parsed_url.path.strip('/').split('/')
                                
                                # 检查URL格式是否能提取owner/repo
                                if len(path_parts) >= 2: 
                                    repo_owner = path_parts[0]
                                    repo_name = path_parts[1]
                                    # repo_identifier = f"{repo_owner}/{repo_name}" # 使用 owner/repo 作为标识
                                    
                                    max_similarity = 0.0
                                    for cpe_str in self.cve_affected_cpes:
                                        # 从CPE字符串中提取可能的组件名 (通常是第5部分)
                                        cpe_parts = cpe_str.split(':')
                                        if len(cpe_parts) > 4:
                                            component_name = cpe_parts[4] # 产品名
                                            # 仅比较仓库名与CPE产品名
                                            similarity = self._string_similarity(repo_name, component_name)
                                            max_similarity = max(max_similarity, similarity)
                                            
                                    if max_similarity < repo_similarity_threshold:
                                        logger.debug(f"节点 {node.url} 未通过CPE检查: 最大相似度 {max_similarity:.2f} < {repo_similarity_threshold}")
                                        node_valid = False
                                        continue
                                    else:
                                         logger.debug(f"节点 {node.url} 通过CPE检查: 最大相似度 {max_similarity:.2f}")
                                else:
                                    # 即使URL格式不符合预期，也保留节点(宽松处理)
                                    logger.debug(f"无法从 {node.url} 解析仓库信息进行CPE检查，但保留该节点。")
                            except Exception as e:
                                 # 即使出现异常，也保留节点(宽松处理)
                                 logger.error(f"对 {node.url} 进行CPE检查时出错: {e}", exc_info=True)
                        # 对于非 Git Commit 或非 GitHub/GitLab 的链接，不应用此规则
                        # else: logger.debug(f"跳过对非Git提交节点的CPE检查: {node.url}")
            
            # 如果节点通过所有检查，添加到结果列表
            if node_valid:
                logger.info(f"节点 {node.url} 通过所有指定规则。")
                selected_patch_nodes.append(node)
            # else: 
            #     logger.info(f"节点 {node.url} 未通过所有指定规则。")
        
        logger.info(f"应用规则后选择了 {len(selected_patch_nodes)} 个补丁节点。")
        return selected_patch_nodes

    def _get_commit_date(self, url: str) -> Optional[str]:
        """
        尝试获取给定Commit URL的提交日期。

        使用GitHub API或GitLab API获取commit的提交日期。

        Args:
            url: Commit URL (支持 GitHub, GitLab, 等)

        Returns:
            Optional[str]: ISO 8601 格式的日期字符串 (e.g., '2023-10-26T12:00:00Z'), 
                           如果无法获取则返回 None。
        """
        logger.debug(f"尝试获取提交日期: {url}")
        
        # --- GitHub Commit --- 
        if 'github.com' in url and ('/commit/' in url or '/-/commit/' in url): 
            try:
                # 解析GitHub URL以获取项目路径和commit SHA
                parsed_url = urlparse(url)
                path_parts = parsed_url.path.strip('/').split('/')
                
                # 解析不同形式的GitHub URL
                commit_sha = None
                project_path = None
                
                # 处理标准格式: github.com/owner/repo/commit/hash
                if len(path_parts) >= 4 and path_parts[-2] == 'commit':
                    commit_sha = path_parts[-1]
                    owner = path_parts[0]
                    repo = path_parts[1]
                # 处理带有-的格式: github.com/owner/repo/-/commit/hash
                elif len(path_parts) >= 5 and path_parts[-3] == '-' and path_parts[-2] == 'commit':
                    commit_sha = path_parts[-1]
                    owner = path_parts[0]
                    repo = path_parts[1]
                
                if commit_sha and owner and repo:
                    logger.info(f"识别到GitHub提交: {owner}/{repo}@{commit_sha}")
                    
                    # GitHub API 调用
                    import os
                    
                    github_token = os.environ.get('GITHUB_TOKEN', '')
                    headers = {}
                    if github_token:
                        headers['Authorization'] = f'token {github_token}'
                    
                    # 正确的GitHub API URL格式
                    api_url = f"https://api.github.com/repos/{owner}/{repo}/commits/{commit_sha}"
                    
                    try:
                        response = requests.get(api_url, headers=headers, timeout=10)
                        response.raise_for_status()
                        commit_data = response.json()
                        
                        # 优先使用 committer date，其次是 authored_date
                        commit_date_str = commit_data.get('commit', {}).get('committer', {}).get('date')
                        if not commit_date_str:
                             commit_date_str = commit_data.get('commit', {}).get('author', {}).get('date')
                        
                        if commit_date_str:
                             return commit_date_str  # GitHub API已提供ISO 8601格式
                        else:
                             logger.warning(f"在GitHub API响应中未找到{url}的日期信息")
                             return None
                    except requests.exceptions.RequestException as req_e:
                         logger.error(f"GitHub API请求失败 {url}: {req_e}")
                         return None
                    except Exception as e:
                        logger.error(f"处理GitHub API响应时出错 {url}: {e}")
                        return None
                else:
                    logger.warning(f"无法从URL解析GitHub组件: {url}")
                    return None
            except Exception as e:
                logger.error(f"处理GitHub URL时出错 {url}: {e}", exc_info=True)
                return None

        # --- GitLab Commit --- 
        elif 'gitlab' in url and ('/commit/' in url or '/-/commit/' in url): 
            try:
                # 解析GitLab URL以获取项目路径和commit SHA
                parsed_url = urlparse(url)
                path_parts = parsed_url.path.strip('/').split('/')
                
                # 解析不同形式的GitLab URL
                commit_sha = None
                project_path = None
                
                # 处理标准格式: gitlab/owner/repo/commit/hash
                if len(path_parts) >= 4 and path_parts[-2] == 'commit':
                    commit_sha = path_parts[-1]
                    project_path = '/'.join(path_parts[:-2])
                # 处理带有-的格式: gitlab/owner/repo/-/commit/hash
                elif len(path_parts) >= 5 and path_parts[-3] == '-' and path_parts[-2] == 'commit':
                    commit_sha = path_parts[-1]
                    project_path = '/'.join(path_parts[:-3])
                
                if commit_sha and project_path:
                    logger.info(f"识别到GitLab提交: {project_path}@{commit_sha}")
                    
                    # GitLab API 调用
                    import os
                    from urllib.parse import quote
                    
                    gitlab_token = os.environ.get('GITLAB_TOKEN', '')
                    headers = {}
                    if gitlab_token:
                        headers['PRIVATE-TOKEN'] = gitlab_token
                    
                    # URL编码项目路径，保留斜杠
                    encoded_project_path = quote(project_path, safe='/')
                    api_url = f"https://gitlab.com/api/v4/projects/{encoded_project_path}/repository/commits/{commit_sha}"
                    
                    try:
                        response = requests.get(api_url, headers=headers, timeout=10)
                        response.raise_for_status()
                        commit_data = response.json()
                        
                        # 优先使用 committed_date，其次是 authored_date
                        commit_date_str = commit_data.get('committed_date')
                        if not commit_date_str:
                             commit_date_str = commit_data.get('authored_date')
                        
                        if commit_date_str:
                             return commit_date_str  # GitLab API返回的也是ISO 8601格式
                        else:
                             logger.warning(f"在GitLab API响应中未找到{url}的日期信息")
                             return None
                    except requests.exceptions.RequestException as req_e:
                         logger.error(f"GitLab API请求失败 {url}: {req_e}")
                         return None
                    except Exception as e:
                        logger.error(f"处理GitLab API响应时出错 {url}: {e}")
                        return None
                else:
                    logger.warning(f"无法从URL解析GitLab组件: {url}")
                    return None
            except Exception as e:
                logger.error(f"处理GitLab URL时出错 {url}: {e}", exc_info=True)
                return None

        # --- SVN Commit --- 
        # 获取 SVN 提交日期通常更困难，可能需要访问 SVN 仓库或解析特定日志格式
        elif 'svn' in url and ('revision=' in url or '/r' in url):
            logger.warning(f"获取SVN URL ({url}) 的提交日期功能尚未实现。")
            
            # SVN可能需要更复杂的处理，涉及SVN客户端或特定API
            # 这里仅作为示例，返回None表示暂未实现
            return None
            
        # --- 其他 Git 仓库 (例如 Gitee, Bitbucket, 自托管 Git) --- 
        # 需要针对特定平台实现逻辑
        elif 'git' in url:
             logger.warning(f"获取通用Git URL ({url}) 的提交日期功能尚未实现。")
             return None
             
        # --- 无法识别或不支持的 URL 类型 --- 
        else:
            logger.debug(f"不支持的URL格式，无法提取提交日期: {url}")
            return None

    def get_important_nodes(self, max_nodes: int = 30, prioritize_patches: bool = True,
                           include_issues: bool = True) -> List[ReferenceNode]:
        """
        获取重要节点
        
        使用优先级算法选择最重要的节点
        
        Args:
            max_nodes: 最大节点数量
            prioritize_patches: 是否优先选择补丁节点
            include_issues: 是否包含问题节点
            
        Returns:
            List[ReferenceNode]: 重要节点列表
        """
        # 计算所有节点的优先级
        self.compute_node_priorities(self.nodes)
        
        important_nodes = []
        
        # 1. 优先添加补丁节点
        if prioritize_patches:
            patch_nodes = [n for n in self.nodes if n.node_type == NodeType.PATCH]
            # 按重要性排序
            sorted_patch_nodes = sorted(patch_nodes, key=lambda n: n.importance_score, reverse=True)
            important_nodes.extend(sorted_patch_nodes[:min(len(sorted_patch_nodes), max_nodes // 2)])
        
        # 2. 添加问题节点
        if include_issues:
            issue_nodes = [n for n in self.nodes if n.node_type == NodeType.ISSUE]
            # 按重要性排序
            sorted_issue_nodes = sorted(issue_nodes, key=lambda n: n.importance_score, reverse=True)
            important_nodes.extend(sorted_issue_nodes[:min(len(sorted_issue_nodes), max_nodes // 4)])
        
        # 3. 如果还有空位，添加其他重要节点
        remaining_slots = max_nodes - len(important_nodes)
        if remaining_slots > 0:
            other_nodes = [n for n in self.nodes if n not in important_nodes 
                         and n.node_type not in [NodeType.ROOT, NodeType.SOURCE]]
            # 按重要性排序
            sorted_other_nodes = sorted(other_nodes, key=lambda n: n.importance_score, reverse=True)
            important_nodes.extend(sorted_other_nodes[:remaining_slots])
        
        return important_nodes
    
    def to_dict(self) -> Dict[str, Any]:
        """
        转换为字典
        
        Returns:
            Dict[str, Any]: 网络的字典表示
        """
        return {
            'cve_id': self.cve_id,
            'cve_published_date': self.cve_published_date,
            'cve_affected_cpes': self.cve_affected_cpes,
            'nodes': [node.to_dict() for node in self.nodes],
            'edges': [edge.to_dict() for edge in self.edges]
        } 

    @staticmethod
    def _levenshtein_distance(s1: str, s2: str) -> int:
        """计算两个字符串之间的Levenshtein距离"""
        if len(s1) < len(s2):
            return ReferenceNetwork._levenshtein_distance(s2, s1)

        if len(s2) == 0:
            return len(s1)

        previous_row = range(len(s2) + 1)
        for i, c1 in enumerate(s1):
            current_row = [i + 1]
            for j, c2 in enumerate(s2):
                insertions = previous_row[j + 1] + 1
                deletions = current_row[j] + 1
                substitutions = previous_row[j] + (c1 != c2)
                current_row.append(min(insertions, deletions, substitutions))
            previous_row = current_row
        
        return previous_row[-1]

    @staticmethod
    def _string_similarity(s1: str, s2: str) -> float:
        """计算两个字符串的相似度 (基于Levenshtein距离)"""
        if not s1 or not s2:
            return 0.0
        max_len = max(len(s1), len(s2))
        if max_len == 0:
            return 1.0
        distance = ReferenceNetwork._levenshtein_distance(s1.lower(), s2.lower())
        return 1.0 - (distance / max_len) 